#!/usr/bin/env python
import sqlite3
import hashlib
import uuid
from datetime import datetime

def create_user(username, password, role, belong, phone, email=None):
    """创建新用户"""
    # 连接数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    # 检查用户是否已存在
    cursor.execute("SELECT u_id FROM users WHERE u_name=?", (username,))
    existing_user = cursor.fetchone()
    
    if existing_user:
        print(f"用户 {username} 已存在，ID: {existing_user[0]}")
        conn.close()
        return
    
    # 生成盐值和哈希密码
    salt = str(uuid.uuid4())
    hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
    now = datetime.now().isoformat()
    
    # 创建用户
    cursor.execute(
        "INSERT INTO users (u_name, u_pwd, salt, u_role, u_belong, u_phone, u_email, created_at, updated_at, version) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        (username, hashed_password, salt, role, belong, phone, email, now, now, 1)
    )
    
    # 获取新用户ID
    cursor.execute("SELECT u_id FROM users WHERE u_name=?", (username,))
    user_id = cursor.fetchone()[0]
    
    # 提交更改
    conn.commit()
    
    # 关闭连接
    conn.close()
    
    print(f"已创建用户 {username}，ID: {user_id}，角色: {role}，密码: {password}")
    return user_id

def main():
    """创建多个用户"""
    # 创建安保人员用户
    create_user(
        username="security",
        password="111111",
        role="security",
        belong="保卫处",
        phone="13800138001",
        email="<EMAIL>"
    )
    
    # 创建普通用户
    create_user(
        username="user1",
        password="111111",
        role="user",
        belong="计算机学院",
        phone="13800138002",
        email="<EMAIL>"
    )
    
    print("用户创建完成")

if __name__ == "__main__":
    main()
