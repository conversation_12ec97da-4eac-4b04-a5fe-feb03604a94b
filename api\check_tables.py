#!/usr/bin/env python
import sqlite3
import os

def main():
    """检查数据库中的表"""
    # 获取数据库路径
    db_path = 'sys.db'
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件 {db_path} 不存在")
        return
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查询所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    # 关闭连接
    conn.close()
    
    # 显示表信息
    print("\n数据库中的所有表：")
    print("=" * 50)
    
    if tables:
        for i, table in enumerate(tables, 1):
            print(f"{i}. {table[0]}")
    else:
        print("数据库中没有表")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
