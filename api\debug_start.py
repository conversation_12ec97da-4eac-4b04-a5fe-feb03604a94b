#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试启动脚本
"""

import os
import sys

# 强制输出立即显示
sys.stdout.flush()
sys.stderr.flush()

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print(f"当前工作目录: {os.getcwd()}", flush=True)
print("正在启动Flask服务...", flush=True)

try:
    print("开始导入Flask应用...", flush=True)
    # 导入Flask应用
    from flask_app import app
    print("Flask应用导入成功", flush=True)
    
    # 启动服务器
    print("启动Flask服务器在 http://127.0.0.1:5003", flush=True)
    app.run(host='127.0.0.1', port=5003, debug=False, use_reloader=False)
    
except Exception as e:
    print(f"启动失败: {e}", flush=True)
    import traceback
    traceback.print_exc()
