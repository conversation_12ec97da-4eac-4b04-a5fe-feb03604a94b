```mermaid
flowchart TD
    A[停车中心模块] --> B[停车场信息管理]
    A --> C[停车服务]
    A --> D[停车统计]
    
    %% 停车场信息管理子功能
    B --> B1[停车场列表]
    B --> B2[停车场详情]
    B --> B3[筛选和搜索功能]
    
    B1 --> B1a[卡片式展示停车场]
    B1 --> B1b[显示名称、地址、车位数等]
    B1 --> B1c[查看详情和开始停车按钮]
    
    B2 --> B2a[停车场基本信息]
    B2 --> B2b[车位使用情况]
    B2 --> B2c[车位类型分布]
    B2 --> B2d[车位网格图]
    
    B3 --> B3a[停车场名称搜索框]
    B3 --> B3b[区域筛选]
    B3 --> B3c[排序选项]
    
    %% 停车服务子功能
    C --> C1[快速停车对话框]
    C --> C2[选择车位停车对话框]
    
    C1 --> C1a[车辆选择下拉框]
    C1 --> C1b[车位类型选择]
    C1 --> C1c[备注输入框]
    C1 --> C1d[我要停车按钮]
    
    C2 --> C2a[车位网格图]
    C2 --> C2b[车位筛选条件]
    C2 --> C2c[车辆选择下拉框]
    C2 --> C2d[备注输入框]
    C2 --> C2e[确认停车按钮]
    
    %% 停车统计子功能
    D --> D1[数据卡片区域]
    D --> D2[停车趋势图表]
    D --> D3[停车时长分布图表]
    D --> D4[常用停车场图表]
    D --> D5[车位类型使用图表]
    
    D1 --> D1a[总停车次数]
    D1 --> D1b[进行中停车]
    D1 --> D1c[平均停车时长]
    D1 --> D1d[常用停车场]
    
    D2 --> D2a[每日停车次数柱状图]
    D2 --> D2b[时间范围选择器]
    
    D3 --> D3a[停车时长分布饼图]
    D3 --> D3b[不同时长区间占比]
    
    D4 --> D4a[常用停车场饼图]
    D4 --> D4b[不同停车场使用分布]
    
    D5 --> D5a[车位类型使用柱状图]
    D5 --> D5b[不同类型车位使用次数]
    
    %% 用户权限说明
    subgraph 权限说明
        Z1[所有用户可访问]
        Z2[管理员拥有更多权限]
    end
    
    %% 停车流程
    subgraph 停车流程
        P1[浏览停车场] --> P2[选择停车方式]
        P2 --> P3a[快速停车]
        P2 --> P3b[选择车位停车]
        P3a --> P4[选择车辆]
        P3b --> P4
        P4 --> P5[确认停车]
        P5 --> P6[生成停车记录]
    end
    
    %% 样式定义
    classDef userAccess fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef adminExtra fill:#ffcccc,stroke:#333,stroke-width:1px
    
    class A,B,C,D userAccess
    class B2d,D adminExtra
```
