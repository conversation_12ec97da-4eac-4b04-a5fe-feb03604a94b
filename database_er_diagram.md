```mermaid
erDiagram
    %% 用户表
    USERS {
        int u_id PK
        string u_name
        string u_pwd
        string salt
        string u_role
        string u_belong
        string u_phone
        string u_email
        string avatar
        datetime created_at
        datetime updated_at
        int version
    }
    
    %% 电动车表
    BIKES {
        int b_id PK
        int belong_to FK
        string b_num
        string brand
        string color
        string b_type
        string status
        datetime created_at
        datetime updated_at
    }
    
    %% 停车场表
    PARKING_LOTS {
        int id PK
        string name
        string address
        int total_spaces
        int occupied_spaces
        float longitude
        float latitude
        string opening_hours
        int status
        datetime created_at
        datetime updated_at
    }
    
    %% 停车位表
    PARKING_SPACES {
        int id PK
        int parking_lot_id FK
        string space_number
        int type
        int status
        int current_vehicle_id FK
        float power
        int charging_type
        string remarks
        datetime created_at
        datetime updated_at
    }
    
    %% 停车记录表
    PARKING_RECORDS {
        int id PK
        int vehicle_id FK
        int user_id FK
        int parking_lot_id FK
        int parking_space_id FK
        datetime entry_time
        datetime exit_time
        int status
        string remarks
        datetime created_at
        datetime updated_at
    }
    
    %% 充电记录表
    CHARGING_RECORDS {
        int id PK
        int parking_record_id FK
        int vehicle_id FK
        int user_id FK
        int parking_lot_id FK
        int parking_space_id FK
        datetime start_time
        datetime end_time
        int duration
        float power
        int charging_type
        int status
        string remarks
        datetime created_at
        datetime updated_at
    }
    
    %% 违规记录表
    VIOLATION_RECORDS {
        int id PK
        string bike_number
        int bike_id FK
        int user_id FK
        datetime violation_time
        string location
        string violation_type
        int violation_type_id FK
        string description
        int status
        int recorder_id FK
        int handler_id FK
        datetime handling_time
        string handling_result
        datetime created_at
        datetime updated_at
    }
    
    %% 违规类型表
    VIOLATION_TYPES {
        int id PK
        string name
        string description
        int needs_admin
        datetime created_at
        datetime updated_at
    }
    
    %% 申诉记录表
    APPEALS {
        int id PK
        int violation_id FK
        int user_id FK
        string reason
        int status
        int handler_id FK
        datetime handling_time
        string handling_result
        datetime created_at
        datetime updated_at
    }
    
    %% 公告表
    ANNOUNCEMENTS {
        int id PK
        string title
        string content
        string type
        int created_by FK
        datetime created_at
        datetime updated_at
    }
    
    %% 表间关系
    USERS ||--o{ BIKES : "拥有"
    USERS ||--o{ PARKING_RECORDS : "创建"
    USERS ||--o{ CHARGING_RECORDS : "创建"
    USERS ||--o{ VIOLATION_RECORDS : "关联"
    USERS ||--o{ APPEALS : "提交"
    USERS ||--o{ ANNOUNCEMENTS : "发布"
    
    BIKES ||--o{ PARKING_RECORDS : "使用"
    BIKES ||--o{ CHARGING_RECORDS : "使用"
    BIKES ||--o{ VIOLATION_RECORDS : "关联"
    
    PARKING_LOTS ||--o{ PARKING_SPACES : "包含"
    PARKING_LOTS ||--o{ PARKING_RECORDS : "关联"
    PARKING_LOTS ||--o{ CHARGING_RECORDS : "关联"
    
    PARKING_SPACES ||--o{ PARKING_RECORDS : "使用"
    PARKING_SPACES ||--o{ CHARGING_RECORDS : "使用"
    PARKING_SPACES ||--o| BIKES : "当前停放"
    
    PARKING_RECORDS ||--o{ CHARGING_RECORDS : "关联"
    
    VIOLATION_TYPES ||--o{ VIOLATION_RECORDS : "分类"
    
    VIOLATION_RECORDS ||--o{ APPEALS : "申诉"
```
