import sqlite3
import datetime

# 连接数据库
conn = sqlite3.connect('sys.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# 查询充电记录总数
cursor.execute('SELECT COUNT(*) FROM charging_records')
total = cursor.fetchone()[0]
print(f'充电记录总数: {total}')

# 查询充电记录详情
print('\n充电记录详情:')
cursor.execute('''
    SELECT id, vehicle_id, user_id, parking_lot_id, parking_space_id, 
           start_time, end_time, status, duration, power
    FROM charging_records
    ORDER BY id
''')
records = cursor.fetchall()

# 按日期分组统计记录数
date_counts = {}
for record in records:
    # 提取日期部分
    start_date = record['start_time'].split(' ')[0] if record['start_time'] else 'Unknown'
    if start_date not in date_counts:
        date_counts[start_date] = 0
    date_counts[start_date] += 1

    # 打印记录详情
    print(f"ID: {record['id']}, 车辆ID: {record['vehicle_id']}, 用户ID: {record['user_id']}, " +
          f"停车场ID: {record['parking_lot_id']}, 车位ID: {record['parking_space_id']}, " +
          f"开始时间: {record['start_time']}, 结束时间: {record['end_time']}, " +
          f"状态: {record['status']}, 时长: {record['duration']}, 功率: {record['power']}")

# 打印按日期分组的统计
print('\n按日期分组的充电记录数:')
for date, count in sorted(date_counts.items()):
    print(f"{date}: {count}条记录")

# 检查是否有测试数据脚本添加的记录
print('\n检查最近添加的记录:')
cursor.execute('''
    SELECT id, start_time, end_time
    FROM charging_records
    ORDER BY id DESC
    LIMIT 5
''')
recent_records = cursor.fetchall()
for record in recent_records:
    print(f"ID: {record['id']}, 开始时间: {record['start_time']}, 结束时间: {record['end_time']}")

# 关闭连接
conn.close()
