# 校园电动车管理系统数据库E-R图

## E-R图

```mermaid
erDiagram
    Users ||--o{ Bikes : "拥有"
    Users ||--o{ ParkingRecord : "创建"
    Users ||--o{ ChargingRecord : "创建"
    Users ||--o{ ChargingReservation : "创建"
    Users ||--o{ ViolationRecord : "接收"
    Users ||--o{ ViolationRecord : "记录"
    Users ||--o{ Appeal : "提交"
    Users ||--o{ Evidence : "上传"
    Users ||--o{ Announcement : "创建"
    Users ||--o{ Players : "关联"
    
    Bikes ||--o{ ParkingRecord : "参与"
    Bikes ||--o{ ChargingRecord : "参与"
    Bikes ||--o{ ChargingReservation : "参与"
    Bikes ||--o{ ViolationRecord : "涉及"
    
    ParkingLot ||--o{ ParkingSpace : "包含"
    ParkingLot ||--o{ ParkingRecord : "关联"
    ParkingLot ||--o{ ChargingRecord : "关联"
    ParkingLot ||--o{ ChargingReservation : "关联"
    
    ParkingSpace ||--o{ ParkingRecord : "关联"
    ParkingSpace ||--o{ ChargingRecord : "关联"
    
    ParkingRecord ||--o{ ChargingRecord : "关联"
    
    ViolationType ||--o{ ViolationRecord : "分类"
    
    ViolationRecord ||--o{ Appeal : "关联"
    ViolationRecord ||--o{ Evidence : "包含"
    
    Appeal ||--o{ Evidence : "包含"
    
    Users {
        int u_id PK
        string u_name
        string u_pwd
        string salt
        string u_role
        string u_belong
        string u_phone
        string u_email
        string avatar
        datetime created_at
        datetime updated_at
        int version
    }
    
    Bikes {
        int b_id PK
        int belong_to FK
        string b_num
        string brand
        string color
        string b_type
        string status
        datetime created_at
        datetime updated_at
    }
    
    ParkingLot {
        int id PK
        string name
        string address
        int total_spaces
        int occupied_spaces
        float longitude
        float latitude
        string opening_hours
        int status
        string description
        string campus
        string area
        string manager
        string contact
        datetime created_at
        datetime updated_at
    }
    
    ParkingSpace {
        int id PK
        int parking_lot_id FK
        string space_number
        int type
        int status
        int current_vehicle_id FK
        float power
        int charging_type
        string remarks
        datetime created_at
        datetime updated_at
    }
    
    ParkingRecord {
        int id PK
        int vehicle_id FK
        int user_id FK
        int parking_lot_id FK
        int parking_space_id FK
        datetime entry_time
        datetime exit_time
        int status
        string remarks
        datetime created_at
        datetime updated_at
    }
    
    ChargingRecord {
        int id PK
        int parking_record_id FK
        int vehicle_id FK
        int user_id FK
        int parking_lot_id FK
        int parking_space_id FK
        datetime start_time
        datetime end_time
        int duration
        float power
        int charging_type
        int status
        string remarks
        datetime created_at
        datetime updated_at
    }
    
    ChargingReservation {
        int id PK
        int user_id FK
        int vehicle_id FK
        int parking_lot_id FK
        datetime start_time
        datetime end_time
        int status
        string remarks
        datetime created_at
        datetime updated_at
    }
    
    ViolationType {
        int id PK
        string name
        string description
        int needs_admin
        datetime created_at
        datetime updated_at
    }
    
    ViolationRecord {
        int id PK
        string bike_number
        int bike_id FK
        int user_id FK
        datetime violation_time
        string location
        string violation_type
        int violation_type_id FK
        string description
        int status
        string result
        int recorder_id FK
        int handler_id FK
        datetime created_at
        datetime updated_at
    }
    
    Appeal {
        int id PK
        int violation_id FK
        int user_id FK
        string reason
        int status
        string comment
        int handler_id FK
        datetime created_at
        datetime updated_at
    }
    
    Evidence {
        int id PK
        int related_id
        string related_type
        string evidence_type
        string file_path
        int uploader_id FK
        datetime created_at
    }
    
    Announcement {
        int id PK
        string title
        string content
        string type
        int created_by FK
        datetime created_at
        datetime updated_at
    }
    
    Players {
        int id PK
        string username
        string password
        int user_id FK
    }
```

## 表间关系说明

### 一对多关系

1. **用户与车辆**：一个用户可以拥有多辆车辆
   - 关系：Users.u_id → Bikes.belong_to

2. **用户与停车记录**：一个用户可以创建多个停车记录
   - 关系：Users.u_id → ParkingRecord.user_id

3. **用户与充电记录**：一个用户可以创建多个充电记录
   - 关系：Users.u_id → ChargingRecord.user_id

4. **用户与充电预约**：一个用户可以创建多个充电预约
   - 关系：Users.u_id → ChargingReservation.user_id

5. **用户与违规记录**：一个用户可以接收多个违规记录
   - 关系：Users.u_id → ViolationRecord.user_id

6. **用户与违规记录(记录者)**：一个用户(保安)可以记录多个违规记录
   - 关系：Users.u_id → ViolationRecord.recorder_id

7. **用户与申诉**：一个用户可以提交多个申诉
   - 关系：Users.u_id → Appeal.user_id

8. **用户与证据**：一个用户可以上传多个证据
   - 关系：Users.u_id → Evidence.uploader_id

9. **用户与公告**：一个用户(管理员)可以创建多个公告
   - 关系：Users.u_id → Announcement.created_by

10. **停车场与停车位**：一个停车场包含多个停车位
    - 关系：ParkingLot.id → ParkingSpace.parking_lot_id

11. **停车场与停车记录**：一个停车场关联多个停车记录
    - 关系：ParkingLot.id → ParkingRecord.parking_lot_id

12. **停车位与停车记录**：一个停车位关联多个停车记录
    - 关系：ParkingSpace.id → ParkingRecord.parking_space_id

13. **车辆与停车记录**：一辆车可以有多个停车记录
    - 关系：Bikes.b_id → ParkingRecord.vehicle_id

14. **车辆与充电记录**：一辆车可以有多个充电记录
    - 关系：Bikes.b_id → ChargingRecord.vehicle_id

15. **车辆与充电预约**：一辆车可以有多个充电预约
    - 关系：Bikes.b_id → ChargingReservation.vehicle_id

16. **车辆与违规记录**：一辆车可以有多个违规记录
    - 关系：Bikes.b_id → ViolationRecord.bike_id

17. **违规类型与违规记录**：一个违规类型可以关联多个违规记录
    - 关系：ViolationType.id → ViolationRecord.violation_type_id

18. **违规记录与申诉**：一个违规记录可以关联多个申诉
    - 关系：ViolationRecord.id → Appeal.violation_id

19. **停车记录与充电记录**：一个停车记录可以关联多个充电记录
    - 关系：ParkingRecord.id → ChargingRecord.parking_record_id
