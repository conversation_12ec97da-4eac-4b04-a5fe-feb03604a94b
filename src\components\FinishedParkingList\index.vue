<template>
  <div class="finished-parking-list">
    <div class="list-header">
      <div class="header-title">
        <i class="el-icon-tickets"></i>
        <span>已结束的停车记录</span>
        <el-tag type="info" size="small" effect="plain" style="margin-left: 10px;">
          {{ total }} 条记录
        </el-tag>
      </div>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          size="small"
          @change="handleDateChange"
        />
        <el-select v-model="listQuery.status" placeholder="状态" size="small" style="width: 100px; margin-left: 10px;" @change="handleFilter">
          <el-option label="全部" value="" />
          <el-option label="已完成" value="1" />
          <el-option label="异常" value="2" />
        </el-select>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 15px;"
      @row-click="handleRowClick"
    >
      <el-table-column label="车牌号" min-width="120" align="center">
        <template slot-scope="scope">
          <div class="vehicle-cell">
            <i class="el-icon-bicycle" style="color: #409EFF; margin-right: 5px;"></i>
            <span v-if="scope.row.vehicle">{{ getVehicleNumber(scope.row.vehicle) }}</span>
            <span v-else-if="scope.row.vehicle_id">{{ getVehicleNumberById(scope.row.vehicle_id) }}</span>
            <span v-else>未知车辆</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="车辆信息" min-width="150" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.vehicle" class="vehicle-info-cell">
            <span class="vehicle-brand">{{ scope.row.vehicle.brand || '未知品牌' }}</span>
            <div class="color-tag" :style="{ backgroundColor: getColorCode(scope.row.vehicle.color) }">
              <span>{{ scope.row.vehicle.color || '未知颜色' }}</span>
            </div>
          </div>
          <span v-else>未知车辆</span>
        </template>
      </el-table-column>

      <el-table-column label="停车场" min-width="150">
        <template slot-scope="scope">
          <el-link
            type="primary"
            @click.native.stop="viewParkingLot(scope.row.parking_lot_id)"
          >
            {{ getParkingLotName(scope.row) }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="车位" min-width="100" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.parking_space">
            {{ scope.row.parking_space.space_number || scope.row.parking_space.number || scope.row.parking_space_number }}
          </span>
          <span v-else-if="scope.row.parking_space_number">
            {{ scope.row.parking_space_number }}
          </span>
          <span v-else>未知车位</span>
        </template>
      </el-table-column>

      <el-table-column label="开始时间" min-width="150" align="center">
        <template slot-scope="scope">
          {{ formatTime(scope.row.entry_time || scope.row.start_time || scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="结束时间" min-width="150" align="center">
        <template slot-scope="scope">
          {{ formatTime(scope.row.exit_time || scope.row.end_time) }}
        </template>
      </el-table-column>

      <el-table-column label="停车时长" min-width="120" align="center">
        <template slot-scope="scope">
          {{ calculateDuration(scope.row.entry_time || scope.row.start_time || scope.row.created_at, scope.row.exit_time || scope.row.end_time) }}
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
            {{ scope.row.status === 1 ? '已完成' : '异常' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 移除操作列，直接在表格中显示完整信息 -->
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next"
        :page-sizes="[5, 10, 20, 50]"
        :page-size="listQuery.limit"
        :total="total"
        :current-page.sync="listQuery.page"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { getUserParkingRecords } from '@/api/parking'
import socketService from '@/utils/socket'

// 添加dayjs插件
dayjs.extend(duration)

export default {
  name: 'FinishedParkingList',
  props: {
    userId: {
      type: [Number, String],
      required: true
    },
    isAdmin: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      loading: false,
      listQuery: {
        page: 1,
        limit: 10,
        status: '1', // 默认显示已完成的记录
        start_date: '',
        end_date: ''
      },
      dateRange: [],
      vehicleCache: {}, // 缓存车辆信息
      parkingLotCache: {}, // 缓存停车场信息
      fetchDataTimer: null, // 添加定时器变量
      isFetching: false // 添加标志变量，防止重复请求
    }
  },
  created() {
    this.fetchData()
    this.setupWebSocketListeners()
  },

  beforeDestroy() {
    this.removeWebSocketListeners()
    // 清除定时器
    if (this.fetchDataTimer) {
      clearTimeout(this.fetchDataTimer)
    }
  },
  methods: {
    fetchData() {
      // 如果正在获取数据，则不重复请求
      if (this.isFetching) {
        console.log('已有请求正在进行中，跳过重复请求')
        return
      }

      this.isFetching = true
      this.loading = true

      // 显示加载中提示
      const loadingMessage = this.$message({
        message: '正在加载停车记录...',
        type: 'info',
        duration: 0
      })

      // 构建查询参数
      const params = { ...this.listQuery }

      // 添加用户ID参数，确保只获取当前用户的停车记录
      // 根据API测试结果，即使是管理员也需要指定用户ID
      if (this.userId) {
        params.user_id = this.userId
      } else {
        // 如果没有用户ID，使用当前登录用户的ID
        const currentUserId = this.$store.getters.userId
        if (currentUserId) {
          params.user_id = currentUserId
        }
      }

      // 添加状态参数，只获取已结束的停车记录
      // 强制设置状态为1（已完成），确保只显示已结束的停车记录
      params.status = 1

      // 确保状态参数是数字类型
      if (params.status !== undefined && params.status !== '') {
        params.status = parseInt(params.status)
      }

      // 添加时间戳，避免缓存
      params._t = new Date().getTime()

      // 添加时间戳到URL中，确保每次请求都是新的
      const timestamp = `_t=${new Date().getTime()}`

      // 添加isAdmin参数
      if (this.isAdmin) {
        params.isAdmin = true
      }

      // 添加时间戳到URL中，确保每次请求都是新的
      getUserParkingRecords(params)
        .then(response => {
          // 关闭加载提示
          loadingMessage.close()

          // 处理响应数据
          console.log('获取到停车记录响应:', response.code)

          try {
            // 简化数据处理逻辑，只处理标准格式
            if (response && response.code === 20000 && response.data) {
              // 标准响应格式: { code: 20000, data: { items: [...] } }
              if (response.data.items && Array.isArray(response.data.items)) {
                console.log('获取到停车记录数据:', response.data.items.length, '条')
                this.list = response.data.items
                this.total = response.data.total || response.data.items.length

                // 只在有数据时显示成功提示
                if (this.list.length > 0) {
                  this.$message.success(`成功加载 ${this.list.length} 条停车记录`)
                } else {
                  this.$message.info('没有找到停车记录')
                }
              } else {
                console.log('响应中没有找到items数组')
                this.list = []
                this.total = 0
                this.$message.info('没有找到停车记录')
              }
            } else {
              console.log('响应格式不符合预期')
              this.list = []
              this.total = 0
              this.$message.info('没有找到停车记录')
            }
          } catch (error) {
            console.error('处理停车记录数据时出错:', error)
            this.list = []
            this.total = 0
            this.$message.warning('数据格式异常，无法显示停车记录')
          }

          this.loading = false
          this.isFetching = false
        })
        .catch(error => {
          // 关闭加载提示
          loadingMessage.close()

          console.error('获取停车记录失败', error)
          this.$message.error('获取停车记录失败: ' + (error.message || '未知错误'))
          this.list = []
          this.total = 0
          this.loading = false
          this.isFetching = false
        })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    handleDateChange(val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = ''
        this.listQuery.end_date = ''
      }
      this.handleFilter()
    },
    handleRowClick(row) {
      // 移除点击行时的详情查看功能
    },
    viewParkingLot(lotId) {
      this.$router.push(`/parking/details/${lotId}`)
    },
    getVehicleNumber(vehicle) {
      if (!vehicle) return '未知车辆';
      return vehicle.bike_number || vehicle.b_num || vehicle.number || `车辆#${vehicle.id || vehicle.b_id || '未知'}`
    },
    getVehicleNumberById(vehicleId) {
      if (!vehicleId) return '未知车辆';
      // 如果缓存中有该车辆信息，直接返回
      if (this.vehicleCache[vehicleId]) {
        return this.getVehicleNumber(this.vehicleCache[vehicleId])
      }
      return `车辆#${vehicleId}`
    },
    getParkingLotName(record) {
      if (!record) return '未知停车场';

      if (record.parking_lot) {
        return record.parking_lot.name || '未知停车场'
      }

      if (record.parking_lot_name) {
        return record.parking_lot_name
      }

      // 如果缓存中有该停车场信息，直接返回
      if (record.parking_lot_id && this.parkingLotCache[record.parking_lot_id]) {
        return this.parkingLotCache[record.parking_lot_id].name || '未知停车场'
      }

      return record.parking_lot_id ? `停车场#${record.parking_lot_id}` : '未知停车场'
    },
    formatTime(time) {
      if (!time) return '未知时间'
      return dayjs(time).format('YYYY-MM-DD HH:mm')
    },
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '未知'

      const start = dayjs(startTime)
      const end = dayjs(endTime)
      const diff = end.diff(start)

      // 如果时间差小于等于0，返回0分钟
      if (diff <= 0) {
        return '0分钟'
      }

      // 计算时间差
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)

      // 格式化时间差，使用更直观的显示方式
      if (days > 0) {
        // 如果超过1天
        return `${days}天 ${hours.toString().padStart(2, '0')}小时 ${minutes.toString().padStart(2, '0')}分钟`
      } else if (hours > 0) {
        // 如果超过1小时
        return `${hours}小时 ${minutes.toString().padStart(2, '0')}分钟`
      } else if (minutes > 0) {
        // 如果超过1分钟
        return `${minutes}分钟 ${seconds}秒`
      } else {
        // 如果不超过1分钟
        return `${seconds}秒`
      }
    },
    getColorCode(colorName) {
      if (!colorName) return '#909399';

      // 根据颜色名称返回对应的颜色代码
      const colorMap = {
        '红色': '#f56c6c',
        '蓝色': '#409EFF',
        '绿色': '#67C23A',
        '黄色': '#E6A23C',
        '黑色': '#303133',
        '白色': '#f5f7fa', // 改为浅灰色，以便于显示
        '灰色': '#909399',
        '粉色': '#F9A8D4',
        '棕色': '#A78BFA',
        // 添加更多颜色
        '深蓝色': '#324157',
        '浅蓝色': '#58B7FF',
        '深绿色': '#13CE66',
        '浅绿色': '#8CC152',
        '橙色': '#FF9900',
        '紫色': '#9B59B6',
        '青色': '#00BCD4',
        '金色': '#F7BA2A'
      }

      // 获取颜色代码，如果没有匹配则使用默认颜色
      const colorCode = colorMap[colorName]
      if (colorCode) {
        return colorCode
      }

      // 如果没有匹配的颜色名称，生成一个固定的颜色
      // 使用颜色名称的字符码生成颜色
      let hash = 0
      for (let i = 0; i < colorName.length; i++) {
        hash = colorName.charCodeAt(i) + ((hash << 5) - hash)
      }

      // 生成HSL颜色，限制色相在一个好看的范围内
      const h = Math.abs(hash) % 360
      const s = 65 + (Math.abs(hash) % 20) // 65-85%的饱和度
      const l = 45 + (Math.abs(hash) % 10) // 45-55%的亮度

      return `hsl(${h}, ${s}%, ${l}%)`
    },

    // 设置WebSocket监听器
    setupWebSocketListeners() {
      // 确保WebSocket连接
      socketService.connect()

      // 监听停车记录结束事件
      socketService.on('parking_record_ended', this.handleParkingRecordEnded)

      console.log('FinishedParkingList: WebSocket监听器设置完成')
    },

    // 移除WebSocket监听器
    removeWebSocketListeners() {
      // 移除停车记录结束事件监听器
      socketService.off('parking_record_ended', this.handleParkingRecordEnded)

      console.log('FinishedParkingList: WebSocket监听器已移除')
    },

    // 处理停车记录结束事件
    handleParkingRecordEnded(data) {
      console.log('FinishedParkingList: 收到停车记录结束事件:', data)

      if (data && data.record) {
        // 使用定时器延迟刷新，避免与其他组件的刷新冲突
        if (this.fetchDataTimer) {
          clearTimeout(this.fetchDataTimer)
        }

        this.fetchDataTimer = setTimeout(() => {
          this.fetchData()
        }, 1000)

        // 显示通知
        this.$notify({
          title: '停车记录已更新',
          message: `车辆 ${data.record.vehicle ? data.record.vehicle.number || data.record.vehicle.b_num : data.record.vehicle_id} 的停车记录已更新`,
          type: 'success',
          duration: 3000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.finished-parking-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .header-title {
      display: flex;
      align-items: center;

      i {
        font-size: 18px;
        color: #409EFF;
        margin-right: 8px;
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .vehicle-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .vehicle-info-cell {
    display: flex;
    align-items: center;
    justify-content: center;

    .vehicle-brand {
      margin-right: 8px;
      font-weight: 600;
    }

    .color-tag {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      color: #fff;
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
        pointer-events: none;
      }

      span {
        position: relative;
        z-index: 1;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
