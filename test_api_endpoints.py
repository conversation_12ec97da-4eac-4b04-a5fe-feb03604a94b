import requests
import json
import sys

def test_parking_api_endpoints():
    """测试停车记录相关API端点"""
    print("测试停车记录相关API端点...")
    
    # 获取 admin 用户的 token
    login_url = "http://127.0.0.1:5000/api/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        login_json = login_response.json()
        
        if login_response.status_code == 200 and login_json.get("access_token"):
            token = login_json.get("access_token")
            print(f"获取 token 成功: {token[:10]}...")
            
            # 测试API端点
            headers = {
                "Authorization": f"Bearer {token}"
            }
            
            # 测试不同的API端点
            test_endpoints = [
                {
                    "name": "获取用户停车记录",
                    "url": "http://127.0.0.1:5000/api/parking-records/user",
                    "method": "GET",
                    "params": {"status": 1, "user_id": 3}
                },
                {
                    "name": "获取所有停车记录",
                    "url": "http://127.0.0.1:5000/api/parking-records",
                    "method": "GET",
                    "params": {}
                },
                {
                    "name": "获取停车场列表",
                    "url": "http://127.0.0.1:5000/api/parking-lots",
                    "method": "GET",
                    "params": {}
                },
                {
                    "name": "获取停车场详情",
                    "url": "http://127.0.0.1:5000/api/parking-lots/1",
                    "method": "GET",
                    "params": {}
                },
                {
                    "name": "获取停车场车位",
                    "url": "http://127.0.0.1:5000/api/parking-lots/1/spaces",
                    "method": "GET",
                    "params": {}
                },
                {
                    "name": "获取车辆列表",
                    "url": "http://127.0.0.1:5000/api/bikes",
                    "method": "GET",
                    "params": {}
                },
                {
                    "name": "检查车辆是否有进行中的停车记录",
                    "url": "http://127.0.0.1:5000/api/parking-records/check-active",
                    "method": "GET",
                    "params": {"vehicle_id": 28}
                }
            ]
            
            for endpoint in test_endpoints:
                print(f"\n测试 {endpoint['name']} API...")
                try:
                    if endpoint['method'] == 'GET':
                        response = requests.get(endpoint["url"], headers=headers, params=endpoint["params"])
                    elif endpoint['method'] == 'POST':
                        response = requests.post(endpoint["url"], headers=headers, json=endpoint["params"])
                    elif endpoint['method'] == 'PUT':
                        response = requests.put(endpoint["url"], headers=headers, json=endpoint["params"])
                    elif endpoint['method'] == 'DELETE':
                        response = requests.delete(endpoint["url"], headers=headers, params=endpoint["params"])
                    
                    print(f"状态码: {response.status_code}")
                    print(f"URL: {response.url}")
                    
                    try:
                        response_json = response.json()
                        print(f"响应类型: {type(response_json)}")
                        print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)[:500]}...")
                        
                        # 分析响应结构
                        analyze_response_structure(response_json)
                        
                    except json.JSONDecodeError:
                        print(f"响应不是有效的JSON: {response.text[:500]}...")
                except Exception as e:
                    print(f"请求失败: {e}")
            
            # 测试创建停车记录API
            print("\n测试创建停车记录API...")
            try:
                # 获取可用车位
                spaces_response = requests.get("http://127.0.0.1:5000/api/parking-lots/1/spaces", headers=headers, params={"status": 0})
                spaces_json = spaces_response.json()
                
                available_spaces = []
                if spaces_json.get("data") and spaces_json["data"].get("spaces"):
                    available_spaces = [space for space in spaces_json["data"]["spaces"] if space["status"] == 0]
                
                if available_spaces:
                    space_id = available_spaces[0]["id"]
                    
                    # 获取用户车辆
                    vehicles_response = requests.get("http://127.0.0.1:5000/api/bikes", headers=headers, params={"belong_to": 3})
                    vehicles_json = vehicles_response.json()
                    
                    user_vehicles = []
                    if vehicles_json.get("data") and vehicles_json["data"].get("bikes"):
                        user_vehicles = vehicles_json["data"]["bikes"]
                    elif isinstance(vehicles_json, list):
                        user_vehicles = vehicles_json
                    
                    if user_vehicles:
                        vehicle_id = user_vehicles[0]["b_id"]
                        
                        # 创建停车记录
                        create_data = {
                            "vehicle_id": vehicle_id,
                            "user_id": 3,
                            "parking_lot_id": 1,
                            "parking_space_id": space_id,
                            "remarks": "API测试创建的停车记录"
                        }
                        
                        create_response = requests.post("http://127.0.0.1:5000/api/parking-records", headers=headers, json=create_data)
                        
                        print(f"状态码: {create_response.status_code}")
                        
                        try:
                            create_json = create_response.json()
                            print(f"响应内容: {json.dumps(create_json, indent=2, ensure_ascii=False)[:500]}...")
                            
                            # 如果创建成功，测试结束停车记录API
                            if create_response.status_code == 201 and create_json.get("id"):
                                record_id = create_json["id"]
                                
                                print("\n测试结束停车记录API...")
                                end_data = {
                                    "remarks": "API测试结束的停车记录"
                                }
                                
                                end_response = requests.put(f"http://127.0.0.1:5000/api/parking-records/{record_id}/end", headers=headers, json=end_data)
                                
                                print(f"状态码: {end_response.status_code}")
                                
                                try:
                                    end_json = end_response.json()
                                    print(f"响应内容: {json.dumps(end_json, indent=2, ensure_ascii=False)[:500]}...")
                                except json.JSONDecodeError:
                                    print(f"响应不是有效的JSON: {end_response.text[:500]}...")
                        except json.JSONDecodeError:
                            print(f"响应不是有效的JSON: {create_response.text[:500]}...")
                    else:
                        print("没有找到用户车辆，无法测试创建停车记录")
                else:
                    print("没有找到可用车位，无法测试创建停车记录")
            except Exception as e:
                print(f"测试创建停车记录失败: {e}")
        else:
            print(f"登录失败: {login_json}")
    except Exception as e:
        print(f"测试过程中出错: {e}")

def analyze_response_structure(response):
    """分析响应结构"""
    if isinstance(response, dict):
        print("响应是一个字典")
        
        # 检查常见的响应字段
        if "code" in response:
            print(f"- 包含 'code' 字段: {response['code']}")
        
        if "data" in response:
            print("- 包含 'data' 字段")
            data = response["data"]
            
            if isinstance(data, dict):
                print("  - data 是一个字典")
                for key in data:
                    print(f"    - 包含键: '{key}'")
                    
                if "items" in data:
                    items = data["items"]
                    print(f"    - items 是一个 {type(items).__name__}")
                    if isinstance(items, list):
                        print(f"    - items 包含 {len(items)} 个元素")
                        if items:
                            print(f"    - 第一个元素类型: {type(items[0]).__name__}")
                            if isinstance(items[0], dict):
                                print(f"    - 第一个元素键: {', '.join(items[0].keys())}")
                
                if "records" in data:
                    records = data["records"]
                    print(f"    - records 是一个 {type(records).__name__}")
                    if isinstance(records, list):
                        print(f"    - records 包含 {len(records)} 个元素")
                        if records:
                            print(f"    - 第一个元素类型: {type(records[0]).__name__}")
                            if isinstance(records[0], dict):
                                print(f"    - 第一个元素键: {', '.join(records[0].keys())}")
            
            elif isinstance(data, list):
                print(f"  - data 是一个列表，包含 {len(data)} 个元素")
                if data:
                    print(f"  - 第一个元素类型: {type(data[0]).__name__}")
                    if isinstance(data[0], dict):
                        print(f"  - 第一个元素键: {', '.join(data[0].keys())}")
        
        if "message" in response:
            print(f"- 包含 'message' 字段: {response['message']}")
        
        if "total" in response:
            print(f"- 包含 'total' 字段: {response['total']}")
        
        # 打印所有顶级键
        print(f"- 所有顶级键: {', '.join(response.keys())}")
    
    elif isinstance(response, list):
        print(f"响应是一个列表，包含 {len(response)} 个元素")
        if response:
            print(f"- 第一个元素类型: {type(response[0]).__name__}")
            if isinstance(response[0], dict):
                print(f"- 第一个元素键: {', '.join(response[0].keys())}")
    
    else:
        print(f"响应是一个 {type(response).__name__}")

if __name__ == "__main__":
    test_parking_api_endpoints()
