"""
检查京A12345车辆状态和相关记录
"""
import sqlite3
import sys
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_database_tables():
    """检查数据库中的表"""
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()

    try:
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("数据库中的表:", [table[0] for table in tables])

        # 检查关键表的结构
        key_tables = ['bikes', 'violation_records', 'vehicle_disable_records']
        for table in key_tables:
            if (table,) in tables:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                print(f"\n{table}表的列结构:")
                for col in columns:
                    print(f"  {col[1]} ({col[2]}), 是否可为空: {col[3] == 0}")
            else:
                print(f"\n表 {table} 不存在")
    except Exception as e:
        print(f"检查数据库表结构出错: {e}")
    finally:
        conn.close()

def list_all_vehicles():
    """列出所有车辆"""
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT b_id, b_num, brand, color, status FROM bikes')
        vehicles = cursor.fetchall()

        print(f"数据库中共有 {len(vehicles)} 辆车")
        print("车辆列表:")
        for vehicle in vehicles:
            print(f"ID={vehicle[0]}, 车牌号={vehicle[1]}, 品牌={vehicle[2]}, 颜色={vehicle[3]}, 状态={vehicle[4]}")

        # 查找包含"京A12345"的车辆
        matching_vehicles = [v for v in vehicles if "京A12345" in str(v[1])]
        if matching_vehicles:
            print("\n找到匹配'京A12345'的车辆:")
            for vehicle in matching_vehicles:
                print(f"ID={vehicle[0]}, 车牌号={vehicle[1]}, 品牌={vehicle[2]}, 颜色={vehicle[3]}, 状态={vehicle[4]}")
        else:
            print("\n未找到匹配'京A12345'的车辆")

    except Exception as e:
        print(f"查询车辆列表出错: {e}")
    finally:
        conn.close()

def check_vehicle(bike_number):
    """检查车辆状态和相关记录"""
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()

    try:
        # 1. 查询车辆信息 - 尝试使用b_num列
        cursor.execute('SELECT * FROM bikes WHERE b_num = ?', (bike_number,))
        vehicle_info = cursor.fetchone()

        if not vehicle_info:
            print(f"未找到车牌号为 {bike_number} 的车辆")
            return

        # 打印车辆信息
        print(f"车辆信息: ID={vehicle_info[0]}, 车牌号={vehicle_info[2]}, 状态={vehicle_info[6]}")

        vehicle_id = vehicle_info[0]

        # 2. 查询违规记录
        cursor.execute('SELECT * FROM violation_records WHERE bike_id = ? ORDER BY id DESC', (vehicle_id,))
        violation_records = cursor.fetchall()

        print(f"\n违规记录数量: {len(violation_records)}")
        for record in violation_records:
            print(f"违规记录: ID={record[0]}, 车牌号={record[1]}, 状态={record[9]}, 类型={record[6]}")

            # 3. 查询关联的禁用记录
            cursor.execute('SELECT * FROM vehicle_disable_records WHERE violation_id = ? ORDER BY id DESC', (record[0],))
            disable_records = cursor.fetchall()

            print(f"  关联的禁用记录数量: {len(disable_records)}")
            for disable_record in disable_records:
                print(f"  禁用记录: ID={disable_record[0]}, 是否激活={disable_record[4]}, 开始时间={disable_record[3]}, 原因={disable_record[8]}")

        # 4. 查询所有与该车辆相关的禁用记录
        cursor.execute('SELECT * FROM vehicle_disable_records WHERE bike_id = ? ORDER BY id DESC', (vehicle_id,))
        all_disable_records = cursor.fetchall()

        print(f"\n所有禁用记录数量: {len(all_disable_records)}")
        for record in all_disable_records:
            print(f"禁用记录: ID={record[0]}, 违规ID={record[2]}, 是否激活={record[4]}, 开始时间={record[3]}, 原因={record[8]}")

        # 5. 检查数据一致性
        vehicle_status = vehicle_info[6]
        active_disable_records = [r for r in all_disable_records if r[4] == 1]  # is_active=1

        if vehicle_status == "废弃" and not active_disable_records:
            print("\n数据不一致: 车辆状态为'废弃'，但没有激活的禁用记录")
        elif vehicle_status != "废弃" and active_disable_records:
            print("\n数据不一致: 车辆状态不是'废弃'，但有激活的禁用记录")
        else:
            print("\n数据一致性检查通过")

    except Exception as e:
        print(f"查询出错: {e}")
    finally:
        conn.close()

def check_violation_records():
    """检查所有违规记录"""
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT id, bike_number, bike_id, status, violation_type, created_at FROM violation_records ORDER BY id DESC LIMIT 20')
        records = cursor.fetchall()

        print(f"最近20条违规记录:")
        for record in records:
            print(f"ID={record[0]}, 车牌号={record[1]}, 车辆ID={record[2]}, 状态={record[3]}, 类型={record[4]}, 创建时间={record[5]}")

        # 查找包含"京A12345"的违规记录
        cursor.execute('SELECT id, bike_number, bike_id, status, violation_type, created_at FROM violation_records WHERE bike_number LIKE ?', ('%京A12345%',))
        matching_records = cursor.fetchall()

        if matching_records:
            print("\n找到匹配'京A12345'的违规记录:")
            for record in matching_records:
                print(f"ID={record[0]}, 车牌号={record[1]}, 车辆ID={record[2]}, 状态={record[3]}, 类型={record[4]}, 创建时间={record[5]}")

                # 查找关联的禁用记录
                if record[2]:  # 如果有车辆ID
                    cursor.execute('SELECT id, is_active, disable_start_time, reason FROM vehicle_disable_records WHERE violation_id = ?', (record[0],))
                    disable_records = cursor.fetchall()

                    if disable_records:
                        print("  关联的禁用记录:")
                        for dr in disable_records:
                            print(f"  ID={dr[0]}, 是否激活={dr[1]}, 开始时间={dr[2]}, 原因={dr[3]}")
                    else:
                        print("  没有关联的禁用记录")
        else:
            print("\n未找到匹配'京A12345'的违规记录")

    except Exception as e:
        print(f"查询违规记录出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("开始检查数据库表结构...")
    check_database_tables()

    print("\n开始列出所有车辆...")
    list_all_vehicles()

    print("\n开始检查违规记录...")
    check_violation_records()

    bike_number = "京A12345"
    print(f"\n开始检查车牌号为 {bike_number} 的车辆...")
    check_vehicle(bike_number)
