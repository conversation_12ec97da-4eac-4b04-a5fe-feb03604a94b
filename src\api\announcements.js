import request from '@/utils/request'

export function getAnnouncements(params) {
  return request({
    url: '/api/announcements',
    method: 'get',
    params
  })
}

export function getAnnouncement(id) {
  return request({
    url: `/api/announcements/${id}`,
    method: 'get'
  })
}

export function createAnnouncement(data) {
  return request({
    url: '/api/announcements',
    method: 'post',
    data
  })
}

export function updateAnnouncement(id, data) {
  return request({
    url: `/api/announcements/${id}`,
    method: 'put',
    data
  })
}

export function deleteAnnouncement(id) {
  return request({
    url: `/api/announcements/${id}`,
    method: 'delete'
  })
} 