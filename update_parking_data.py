import sqlite3

# 连接到数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 更新停车场信息
updates = [
    (1, '主校区', '教学区', '张主管', '13800138001'),
    (2, '主校区', '宿舍区', '李管理', '13800138002'),
    (3, '主校区', '图书馆区', '王管理', '13800138003'),
    (4, '东校区', '食堂区', '赵管理', '13800138004'),
    (5, '东校区', '体育区', '孙管理', '13800138005')
]

for update in updates:
    cursor.execute('''
        UPDATE parking_lots 
        SET campus = ?, area = ?, manager = ?, contact = ? 
        WHERE id = ?
    ''', (update[1], update[2], update[3], update[4], update[0]))

# 提交更改并关闭连接
conn.commit()
conn.close()

print("已更新 5 个停车场的校区和区域信息")
