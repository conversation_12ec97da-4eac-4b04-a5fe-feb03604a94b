from flask import jsonify, request, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.users.models import Users
from app.utils.paginate import paginate_data
from .models import ParkingLot, ParkingSpace
from app.utils.validation import admin_required, validate_request
from app.parkinglots import parkinglots_bp
from sqlalchemy import func
from app import db

# 导入车位类型更新路由
from .routes_space_type import update_space_type, batch_update_space_type

# 导入工具函数
from .utils import generate_space_number

# 获取所有停车场（带分页）
@parkinglots_bp.route('/parkinglots', methods=['GET'])
@parkinglots_bp.route('/parking-lots', methods=['GET'])  # 修正API路径，避免重复的/api前缀
@jwt_required()
def get_all_parkinglots():
    """获取所有停车场数据，支持分页、排序和搜索"""
    try:
        # 添加调试日志
        current_app.logger.info("开始处理获取停车场列表请求")

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 10, type=int)

        # 搜索条件
        search_term = request.args.get('search', '')
        status = request.args.get('status', None)

        # 打印请求参数
        current_app.logger.info(f"请求参数: page={page}, limit={per_page}, search='{search_term}', status={status}")

        # 先检查数据库中是否有停车场数据
        total_count = ParkingLot.query.count()
        current_app.logger.info(f"数据库中停车场总数: {total_count}")

        # 如果没有数据，添加一些测试数据
        if total_count == 0:
            current_app.logger.info("数据库中没有停车场数据，添加测试数据")
            try:
                test_lots = [
                    {
                        'name': '北门停车场',
                        'address': '校园北门附近',
                        'total_spaces': 20,
                        'status': 1,
                        'description': '位于学校北门，靠近图书馆'
                    },
                    {
                        'name': '东区停车场',
                        'address': '东区教学楼旁',
                        'total_spaces': 30,
                        'status': 1,
                        'description': '东区主停车场，靠近食堂'
                    },
                    {
                        'name': '科研楼停车场',
                        'address': '科研楼南侧',
                        'total_spaces': 15,
                        'status': 1,
                        'description': '仅供科研人员使用'
                    }
                ]

                for lot_data in test_lots:
                    new_lot = ParkingLot(
                        name=lot_data['name'],
                        address=lot_data['address'],
                        total_spaces=lot_data['total_spaces'],
                        status=lot_data['status'],
                        description=lot_data['description']
                    )
                    db.session.add(new_lot)
                    db.session.flush()

                    # 添加车位
                    for i in range(1, lot_data['total_spaces'] + 1):
                        space_number = f"{new_lot.id}-{i:03d}"
                        space = ParkingSpace(
                            parking_lot_id=new_lot.id,
                            space_number=space_number,
                            type=1,
                            status=0
                        )
                        db.session.add(space)

                db.session.commit()
                current_app.logger.info("成功添加测试停车场数据")
                total_count = ParkingLot.query.count()
                current_app.logger.info(f"添加后停车场总数: {total_count}")
            except Exception as e:
                current_app.logger.error(f"添加测试数据失败: {str(e)}")
                db.session.rollback()

        query = ParkingLot.query

        # 应用搜索过滤
        if search_term:
            query = query.filter(ParkingLot.name.ilike(f'%{search_term}%') |
                                ParkingLot.address.ilike(f'%{search_term}%'))

        if status is not None:
            try:
                status_int = int(status)
                query = query.filter(ParkingLot.status == status_int)
                current_app.logger.info(f"按状态过滤: {status_int}")
            except (ValueError, TypeError):
                current_app.logger.warning(f"无效的状态值: {status}")

        # 排序
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        if sort_order == 'desc':
            query = query.order_by(getattr(ParkingLot, sort_field).desc())
        else:
            query = query.order_by(getattr(ParkingLot, sort_field).asc())

        # 获取分页前的总数，用于调试
        pre_paginate_count = query.count()
        current_app.logger.info(f"过滤后但分页前的记录数: {pre_paginate_count}")

        # 获取分页后的停车场
        lots_pagination = query.paginate(page=page, per_page=per_page)

        # 将停车场对象转换为可序列化的字典
        lots_data = []
        for lot in lots_pagination.items:
            try:
                # 安全地获取停车场详情
                lot_details = lot.get_details()
                lots_data.append(lot_details)
            except Exception as detail_error:
                # 记录获取详情错误，但继续处理其他停车场
                current_app.logger.error(f"获取停车场 {lot.id} 详情失败: {str(detail_error)}")
                # 添加一个简化版的停车场信息
                lots_data.append({
                    'id': lot.id,
                    'name': lot.name,
                    'address': lot.address,
                    'total_spaces': lot.total_spaces,
                    'occupied_spaces': lot.occupied_spaces,
                    'status': lot.status,
                    'status_text': '正常运营' if lot.status == 1 else '暂停使用',
                    'error': '获取详情失败'
                })

        current_app.logger.info(f"获取到 {len(lots_data)} 条停车场数据")

        # 构建分页响应
        result = {
            'items': lots_data,
            'pagination': {
                'total': lots_pagination.total,
                'page': lots_pagination.page,
                'per_page': lots_pagination.per_page,
                'pages': lots_pagination.pages
            }
        }

        # 添加调试信息到响应
        current_app.logger.info(f"返回停车场数据: {len(lots_data)} 条记录")

        # 使用api_response函数统一响应格式
        from app.utils.response import api_response
        return api_response(data=result, message="获取停车场列表成功")
    except Exception as e:
        # 记录详细错误信息
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"获取停车场列表失败: {str(e)}\n{error_traceback}")

        # 使用api_response函数统一响应格式
        from app.utils.response import api_response
        return api_response(message="获取停车场列表失败，请联系管理员检查服务器日志", status="error", code=500)

# 获取单个停车场详情
@parkinglots_bp.route('/parkinglots/<int:lot_id>', methods=['GET'])
@parkinglots_bp.route('/api/parking-lots/<int:lot_id>', methods=['GET'])  # 添加新的API路径
@parkinglots_bp.route('/api/parkinglots/<int:lot_id>', methods=['GET'])  # 添加兼容路径
@jwt_required()
def get_parkinglot(lot_id):
    """获取指定停车场的详细信息"""
    try:
        # 记录请求信息，便于调试
        current_app.logger.info(f"获取停车场详情，ID: {lot_id}")

        parking_lot = ParkingLot.query.get(lot_id)

        if not parking_lot:
            current_app.logger.warning(f"未找到指定停车场，ID: {lot_id}")
            # 使用api_response函数统一响应格式
            from app.utils.response import api_response
            return api_response(message="未找到指定停车场", status="error", code=404)

        try:
            # 使用模型的get_details方法获取完整的停车场信息
            lot_details = parking_lot.get_details()

            # 使用api_response函数统一响应格式
            from app.utils.response import api_response
            return api_response(data=lot_details, message="获取停车场详情成功")
        except Exception as detail_error:
            # 记录获取详情错误
            current_app.logger.error(f"获取停车场 {lot_id} 详情失败: {str(detail_error)}")
            # 构建简化版的停车场信息
            simple_details = {
                'id': parking_lot.id,
                'name': parking_lot.name,
                'address': parking_lot.address,
                'total_spaces': parking_lot.total_spaces,
                'occupied_spaces': parking_lot.occupied_spaces,
                'status': parking_lot.status,
                'status_text': '正常运营' if parking_lot.status == 1 else '暂停使用',
                'error': '获取详情失败'
            }

            # 使用api_response函数统一响应格式
            from app.utils.response import api_response
            return api_response(data=simple_details, message="获取停车场详情部分成功，某些信息无法获取")
    except Exception as e:
        # 记录详细错误信息
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"获取停车场详情失败: {str(e)}\n{error_traceback}")

        # 使用api_response函数统一响应格式
        from app.utils.response import api_response
        return api_response(message="获取停车场详情失败，请联系管理员检查服务器日志", status="error", code=500)

# 添加新停车场（仅管理员）
@parkinglots_bp.route('/parkinglots', methods=['POST'])
@jwt_required()
@admin_required
def create_parkinglot():
    """创建新的停车场（仅管理员）"""
    try:
        data = request.get_json()

        # 验证请求数据
        required_fields = ['name', 'address', 'total_spaces']
        validation_result = validate_request(data, required_fields)
        if validation_result:
            return validation_result

        # 检查名称是否已存在
        existing_lot = ParkingLot.query.filter_by(name=data['name']).first()
        if existing_lot:
            return jsonify({'message': '该停车场名称已存在'}), 400

        # 创建新停车场
        new_parking_lot = ParkingLot(
            name=data['name'],
            address=data['address'],
            total_spaces=data['total_spaces'],
            status=data.get('status', 1),  # 默认为启用状态
            description=data.get('description')
        )

        new_parking_lot.save()

        # 初始化停车位
        for i in range(1, data['total_spaces'] + 1):
            # 使用辅助函数生成车位号
            space_number = generate_space_number(new_parking_lot.id, 1)  # 1=普通车位
            parking_space = ParkingSpace(
                parking_lot_id=new_parking_lot.id,
                space_number=space_number,
                type=1,  # 默认为普通车位（1）
                status=0  # 默认为空闲状态（0）
            )
            parking_space.save()

        return jsonify({
            'message': '停车场创建成功',
            'id': new_parking_lot.id,
            'name': new_parking_lot.name,
            'address': new_parking_lot.address,
            'total_spaces': new_parking_lot.total_spaces,
            'occupied_spaces': new_parking_lot.occupied_spaces,
            'status': new_parking_lot.status,
            'created_at': new_parking_lot.created_at.strftime('%Y-%m-%d %H:%M:%S') if new_parking_lot.created_at else None
        }), 201
    except Exception as e:
        current_app.logger.error(f'创建停车场出错: {str(e)}')
        return jsonify({'message': '创建停车场失败', 'error': str(e)}), 500

# 更新停车场信息（仅管理员）
@parkinglots_bp.route('/parkinglots/<int:lot_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_parkinglot(lot_id):
    """更新停车场信息（仅管理员）"""
    try:
        parking_lot = ParkingLot.query.get(lot_id)

        if not parking_lot:
            return jsonify({'message': '未找到指定停车场'}), 404

        data = request.get_json()

        # 检查名称是否与其他停车场重复
        if 'name' in data and data['name'] != parking_lot.name:
            existing_lot = ParkingLot.query.filter_by(name=data['name']).first()
            if existing_lot:
                return jsonify({'message': '该停车场名称已存在'}), 400

        # 更新停车场信息
        if 'name' in data:
            parking_lot.name = data['name']
        if 'address' in data:
            parking_lot.address = data['address']
        if 'status' in data:
            parking_lot.status = data['status']
        if 'description' in data:
            parking_lot.description = data['description']
        if 'longitude' in data:
            parking_lot.longitude = data['longitude']
        if 'latitude' in data:
            parking_lot.latitude = data['latitude']
        if 'opening_hours' in data:
            parking_lot.opening_hours = data['opening_hours']
        if 'campus' in data:
            parking_lot.campus = data['campus']
        if 'area' in data:
            parking_lot.area = data['area']
        if 'manager' in data:
            parking_lot.manager = data['manager']
        if 'contact' in data:
            parking_lot.contact = data['contact']

        # 如果更新总车位数，需要处理停车位
        if 'total_spaces' in data:
            # 获取当前车位数量
            current_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot_id).count()
            new_total = data['total_spaces']

            # 如果新的总车位数小于当前已占用车位数，则不允许减少
            if new_total < parking_lot.occupied_spaces:
                return jsonify({'message': '无法将总车位数减少到小于当前已占用车位数'}), 400

            # 如果新的总车位数大于当前车位数，则添加新车位
            if new_total > current_spaces:
                for i in range(current_spaces + 1, new_total + 1):
                    # 使用辅助函数生成车位号
                    space_number = generate_space_number(lot_id, 1)  # 1=普通车位
                    parking_space = ParkingSpace(
                        parking_lot_id=lot_id,
                        space_number=space_number,
                        type=1,  # 默认为普通车位（1）
                        status=0  # 默认为空闲状态（0）
                    )
                    parking_space.save()
            # 如果新的总车位数小于当前车位数，则删除多余车位（从后往前删）
            elif new_total < current_spaces:
                # 获取所有车位，按ID排序
                spaces = ParkingSpace.query.filter_by(
                    parking_lot_id=lot_id
                ).order_by(ParkingSpace.id.desc()).all()

                # 删除多余的车位，但要确保不删除已占用的车位
                spaces_to_remove = spaces[:current_spaces - new_total]
                for space in spaces_to_remove:
                    if space.status == 1:  # 1表示已占用
                        return jsonify({'message': f'车位 {space.space_number} 已被占用，无法删除'}), 400
                    space.delete()

            parking_lot.total_spaces = new_total

        parking_lot.save()

        return jsonify({
            'message': '停车场信息更新成功',
            'id': parking_lot.id,
            'name': parking_lot.name,
            'address': parking_lot.address,
            'total_spaces': parking_lot.total_spaces,
            'occupied_spaces': parking_lot.occupied_spaces,
            'status': parking_lot.status,
            'description': parking_lot.description,
            'longitude': parking_lot.longitude,
            'latitude': parking_lot.latitude,
            'opening_hours': parking_lot.opening_hours,
            'campus': parking_lot.campus,
            'area': parking_lot.area,
            'manager': parking_lot.manager,
            'contact': parking_lot.contact,
            'updated_at': parking_lot.updated_at.strftime('%Y-%m-%d %H:%M:%S') if parking_lot.updated_at else None
        }), 200
    except Exception as e:
        current_app.logger.error(f'更新停车场出错: {str(e)}')
        return jsonify({'message': '更新停车场失败', 'error': str(e)}), 500

# 删除停车场（仅管理员）
@parkinglots_bp.route('/parkinglots/<int:lot_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_parkinglot(lot_id):
    """删除停车场（仅管理员）"""
    try:
        parking_lot = ParkingLot.query.get(lot_id)

        if not parking_lot:
            return jsonify({'message': '未找到指定停车场'}), 404

        # 检查是否有占用的车位
        if parking_lot.occupied_spaces > 0:
            return jsonify({'message': '停车场内仍有车辆，无法删除'}), 400

        # 删除所有关联的车位
        ParkingSpace.query.filter_by(parking_lot_id=lot_id).delete()

        # 删除停车场
        lot_info = {
            'id': parking_lot.id,
            'name': parking_lot.name,
            'address': parking_lot.address
        }
        parking_lot.delete()

        return jsonify({
            'message': '停车场删除成功',
            'deleted_parking_lot': lot_info
        }), 200
    except Exception as e:
        current_app.logger.error(f'删除停车场出错: {str(e)}')
        return jsonify({'message': '删除停车场失败', 'error': str(e)}), 500

# 获取停车场的所有车位
@parkinglots_bp.route('/parkinglots/<int:lot_id>/spaces', methods=['GET'])
@parkinglots_bp.route('/api/parking-lots/<int:lot_id>/spaces', methods=['GET'])  # 添加新的API路径
@jwt_required()
def get_parking_spaces(lot_id):
    """获取指定停车场的所有车位"""
    try:
        # 验证停车场是否存在
        parking_lot = ParkingLot.query.get(lot_id)
        if not parking_lot:
            return jsonify({'message': '未找到指定停车场'}), 404

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)
        status = request.args.get('status', None)
        space_type = request.args.get('type', None)

        # 添加调试日志
        current_app.logger.info(f"get_parking_spaces 函数接收到的查询参数: page={page}, limit={per_page}, status={status}, type={space_type}")

        # 查询车位
        query = ParkingSpace.query.filter_by(parking_lot_id=lot_id)

        # 按状态过滤
        if status:
            try:
                status_int = int(status)
                query = query.filter_by(status=status_int)
                current_app.logger.info(f"按状态过滤: {status_int}")
            except (ValueError, TypeError):
                current_app.logger.warning(f"无效的状态值: {status}")

        # 按车位类型过滤
        if space_type:
            try:
                type_int = int(space_type)
                query = query.filter_by(type=type_int)
                current_app.logger.info(f"按车位类型过滤: {type_int}")
            except (ValueError, TypeError):
                current_app.logger.warning(f"无效的车位类型值: {space_type}")

        # 排序
        query = query.order_by(ParkingSpace.space_number.asc())

        # 获取分页数据
        spaces_pagination = query.paginate(page=page, per_page=per_page)

        # 将车位对象转换为可序列化的字典
        spaces_data = [space.get_details() for space in spaces_pagination.items]

        # 构建分页响应
        result = {
            'items': spaces_data,
            'pagination': {
                'total': spaces_pagination.total,
                'page': spaces_pagination.page,
                'per_page': spaces_pagination.per_page,
                'pages': spaces_pagination.pages
            }
        }

        return jsonify(result), 200
    except Exception as e:
        current_app.logger.error(f'获取停车位列表出错: {str(e)}')
        return jsonify({'message': '获取停车位列表失败', 'error': str(e)}), 500

# 更新车位状态（仅管理员）
@parkinglots_bp.route('/spaces/<int:space_id>/status', methods=['PUT'])
@jwt_required()
@admin_required
def update_space_status(space_id):
    """更新车位状态（仅管理员）"""
    try:
        # 获取车位
        space = ParkingSpace.query.get(space_id)
        if not space:
            return jsonify({'message': '未找到指定车位'}), 404

        # 获取请求数据
        data = request.get_json()
        if 'status' not in data:
            return jsonify({'message': '缺少状态参数'}), 400

        new_status = data['status']

        # 验证状态值
        if new_status not in [0, 1, 2, 3, 4]:
            return jsonify({'message': '无效的状态值，必须为 0（空闲）、1（已占用）、2（故障）、3（维修中）或 4（禁用）'}), 400

        # 检查当前状态
        old_status = space.status

        # 如果车位已被占用，且要设置为维护中，需要先结束停车记录
        if old_status == 1 and new_status == 2:
            # 检查是否有进行中的停车记录
            from app.parking_records.models import ParkingRecord
            active_record = ParkingRecord.query.filter_by(
                parking_space_id=space_id,
                status=0  # 进行中
            ).first()

            if active_record:
                return jsonify({
                    'message': '车位当前有进行中的停车记录，无法设置为维护状态',
                    'record_id': active_record.id
                }), 400

        # 更新车位状态
        space.status = new_status

        # 如果设置为空闲、故障、维修中或禁用，清除当前车辆ID
        if new_status in [0, 2, 3, 4]:
            space.current_vehicle_id = None

        # 保存更改
        db.session.commit()

        # 更新停车场占用数
        parking_lot = ParkingLot.query.get(space.parking_lot_id)
        if parking_lot:
            parking_lot.update_occupied_spaces()

        # 发送WebSocket通知
        try:
            from app import socketio
            socketio.emit('parking_space_updated', {
                'parking_lot_id': space.parking_lot_id,
                'parking_space_id': space.id,
                'status': space.status,
                'vehicle_id': space.current_vehicle_id
            }, room=f'parking_lot_{space.parking_lot_id}')

            # 通知管理员
            socketio.emit('admin_notification', {
                'type': 'space_status_updated',
                'space': space.get_details(),
                'old_status': old_status,
                'new_status': new_status
            }, room='admin')

            current_app.logger.info(f'实时通知发送成功: 车位状态更新 ID={space.id}')
        except Exception as ws_error:
            current_app.logger.error(f'发送WebSocket通知失败: {str(ws_error)}')

        # 返回成功响应
        status_map = {0: '空闲', 1: '已占用', 2: '故障', 3: '维修中', 4: '禁用'}
        return jsonify({
            'message': f'车位状态已更新为{status_map.get(new_status, "未知")}',
            'space': space.get_details()
        }), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新车位状态出错: {str(e)}')
        return jsonify({'message': '更新车位状态失败', 'error': str(e)}), 500

# 批量更新车位状态（仅管理员）
@parkinglots_bp.route('/spaces/batch-update', methods=['POST'])
@jwt_required()
@admin_required
def batch_update_spaces():
    """批量更新车位状态（仅管理员）"""
    try:
        # 获取请求数据
        data = request.get_json()
        if 'space_ids' not in data or 'status' not in data:
            return jsonify({'message': '缺少必要参数'}), 400

        space_ids = data['space_ids']
        new_status = data['status']

        # 验证状态值
        if new_status not in [0, 1, 2, 3, 4]:
            return jsonify({'message': '无效的状态值，必须为 0（空闲）、1（已占用）、2（故障）、3（维修中）或 4（禁用）'}), 400

        # 检查车位是否存在
        spaces = ParkingSpace.query.filter(ParkingSpace.id.in_(space_ids)).all()
        if len(spaces) != len(space_ids):
            return jsonify({'message': '部分车位不存在'}), 404

        # 如果要设置为维护中，检查是否有进行中的停车记录
        if new_status == 2:
            from app.parking_records.models import ParkingRecord
            occupied_spaces = []

            for space in spaces:
                if space.status == 1:  # 已占用
                    active_record = ParkingRecord.query.filter_by(
                        parking_space_id=space.id,
                        status=0  # 进行中
                    ).first()

                    if active_record:
                        occupied_spaces.append({
                            'space_id': space.id,
                            'space_number': space.space_number,
                            'record_id': active_record.id
                        })

            if occupied_spaces:
                return jsonify({
                    'message': '部分车位当前有进行中的停车记录，无法设置为维护状态',
                    'occupied_spaces': occupied_spaces
                }), 400

        # 更新车位状态
        affected_lots = set()  # 记录受影响的停车场
        updated_spaces = []

        for space in spaces:
            old_status = space.status
            space.status = new_status

            # 如果设置为空闲、故障、维修中或禁用，清除当前车辆ID
            if new_status in [0, 2, 3, 4]:
                space.current_vehicle_id = None

            affected_lots.add(space.parking_lot_id)
            updated_spaces.append({
                'id': space.id,
                'space_number': space.space_number,
                'old_status': old_status,
                'new_status': new_status
            })

        # 提交更改
        db.session.commit()

        # 更新受影响的停车场占用数
        for lot_id in affected_lots:
            parking_lot = ParkingLot.query.get(lot_id)
            if parking_lot:
                parking_lot.update_occupied_spaces()

        # 发送WebSocket通知
        try:
            from app import socketio
            for space in spaces:
                socketio.emit('parking_space_updated', {
                    'parking_lot_id': space.parking_lot_id,
                    'parking_space_id': space.id,
                    'status': space.status,
                    'vehicle_id': space.current_vehicle_id
                }, room=f'parking_lot_{space.parking_lot_id}')

            # 通知管理员
            socketio.emit('admin_notification', {
                'type': 'spaces_batch_updated',
                'spaces': updated_spaces,
                'new_status': new_status
            }, room='admin')

            current_app.logger.info(f'实时通知发送成功: 批量更新车位状态')
        except Exception as ws_error:
            current_app.logger.error(f'发送WebSocket通知失败: {str(ws_error)}')

        # 返回成功响应
        status_map = {0: '空闲', 1: '已占用', 2: '故障', 3: '维修中', 4: '禁用'}
        return jsonify({
            'message': f'已成功更新 {len(spaces)} 个车位状态为{status_map.get(new_status, "未知")}',
            'updated_spaces': updated_spaces
        }), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'批量更新车位状态出错: {str(e)}')
        return jsonify({'message': '批量更新车位状态失败', 'error': str(e)}), 500

# 用户停车场状态统计
@parkinglots_bp.route('/parkinglots/user-stats', methods=['GET'])
def get_user_parking_stats():
    """用户停车场状态统计"""
    try:
        # 获取用户ID
        user_id = get_jwt_identity()

        if not user_id:
            return jsonify({'message': '无法获取用户信息'}), 401

        # 获取用户
        user = Users.query.get(user_id)
        if not user:
            return jsonify({'message': '用户不存在'}), 404

        # 从停车记录中获取用户的停车场使用情况
        from app.parking_records.models import ParkingRecord
        from sqlalchemy import func

        # 计算用户在每个停车场的停车记录数
        lot_stats = db.session.query(
            ParkingRecord.parking_lot_id,
            func.count(ParkingRecord.id).label('count')
        ).filter_by(
            user_id=user_id
        ).group_by(
            ParkingRecord.parking_lot_id
        ).all()

        # 获取停车场详情
        stats = []
        for lot_id, count in lot_stats:
            lot = ParkingLot.query.get(lot_id)
            if lot:
                stats.append({
                    'id': lot.id,
                    'name': lot.name,
                    'count': count
                })

        # 获取用户正在进行中的停车记录
        active_records = ParkingRecord.query.filter_by(
            user_id=user_id,
            status=0  # 进行中
        ).all()

        active_lots = []
        for record in active_records:
            lot = ParkingLot.query.get(record.parking_lot_id)
            if lot:
                active_lots.append({
                    'id': lot.id,
                    'name': lot.name,
                    'parking_record_id': record.id,
                    'vehicle_id': record.vehicle_id,
                    'entry_time': record.entry_time.strftime('%Y-%m-%d %H:%M:%S') if record.entry_time else None
                })

        return jsonify({
            'favorite_lots': stats,
            'active_lots': active_lots
        }), 200
    except Exception as e:
        current_app.logger.error(f'获取用户停车场统计出错: {str(e)}')
        return jsonify({'message': '获取用户停车场统计失败', 'error': str(e)}), 500

# 手动触发停车场数据一致性检查（仅管理员）
@parkinglots_bp.route('/parkinglots/check-consistency', methods=['POST'])
@jwt_required()
@admin_required
def trigger_consistency_check():
    """手动触发停车场数据一致性检查（仅管理员）"""
    try:
        from app.tasks.parking_tasks import check_parking_lot_consistency, check_parking_space_consistency

        # 检查停车场统计数据
        fixed_lots = check_parking_lot_consistency()

        # 检查车位状态与停车记录的一致性
        fixed_spaces = check_parking_space_consistency()

        return jsonify({
            'message': '停车场数据一致性检查完成',
            'fixed_lots': fixed_lots,
            'fixed_spaces': fixed_spaces
        }), 200
    except Exception as e:
        current_app.logger.error(f'触发停车场数据一致性检查失败: {str(e)}')
        return jsonify({'message': '触发停车场数据一致性检查失败', 'error': str(e)}), 500

# 获取停车场统计信息
@parkinglots_bp.route('/parkinglots/stats', methods=['GET'])
@jwt_required()
def get_parking_lots_stats():
    """获取所有停车场的统计信息"""
    try:
        # 获取所有停车场
        parking_lots = ParkingLot.query.all()

        # 计算总体统计信息
        total_lots = len(parking_lots)
        total_spaces = sum(lot.total_spaces for lot in parking_lots)
        total_occupied = sum(lot.occupied_spaces for lot in parking_lots)
        avg_utilization = round((total_occupied / total_spaces) * 100, 2) if total_spaces > 0 else 0

        # 获取每个停车场的详细统计信息
        lots_stats = []
        for lot in parking_lots:
            # 查询实际占用车位数
            actual_occupied = ParkingSpace.query.filter_by(
                parking_lot_id=lot.id,
                status=1  # 已占用状态
            ).count()

            # 查询实际总车位数
            actual_total = ParkingSpace.query.filter_by(
                parking_lot_id=lot.id
            ).count()

            # 检查是否存在不一致
            is_consistent = (lot.occupied_spaces == actual_occupied and lot.total_spaces == actual_total)

            lots_stats.append({
                'id': lot.id,
                'name': lot.name,
                'total_spaces': lot.total_spaces,
                'occupied_spaces': lot.occupied_spaces,
                'available_spaces': lot.total_spaces - lot.occupied_spaces,
                'utilization_rate': round((lot.occupied_spaces / lot.total_spaces) * 100, 2) if lot.total_spaces > 0 else 0,
                'actual_occupied': actual_occupied,
                'actual_total': actual_total,
                'is_consistent': is_consistent
            })

        return jsonify({
            'overview': {
                'total_lots': total_lots,
                'total_spaces': total_spaces,
                'total_occupied': total_occupied,
                'avg_utilization': avg_utilization
            },
            'lots': lots_stats
        }), 200
    except Exception as e:
        current_app.logger.error(f'获取停车场统计信息失败: {str(e)}')
        return jsonify({'message': '获取停车场统计信息失败', 'error': str(e)}), 500