<template>
  <div class="app-container">
    <!-- 页面标题和描述 -->
    <div class="page-title">
      <h2>校园电动车停车中心</h2>
      <p class="subtitle">查看校园停车信息、管理停车记录和停车服务</p>
    </div>

    <!-- 通知栏 -->
    <el-card class="notice-card" shadow="hover">
      <div class="notice-header">
        <i class="el-icon-bell" style="color: #E6A23C; margin-right: 8px;"></i>
        <span style="font-weight: 600;">最新通知</span>
      </div>
      <div class="notice-content">
        <el-carousel height="36px" direction="vertical" :autoplay="true" :interval="5000">
          <el-carousel-item>
            <span>通知：学校将于下周对主校区停车场进行维护，请提前停放车辆</span>
          </el-carousel-item>
          <el-carousel-item>
            <span>提示：请将电动车停放在指定区域，不要堵塞消防通道</span>
          </el-carousel-item>
          <el-carousel-item>
            <span>提醒：请使用校园卡或手机扫码支付停车费用，便捷快速</span>
          </el-carousel-item>
        </el-carousel>
      </div>
    </el-card>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="main-tabs" type="border-card">
      <el-tab-pane label="停车区" name="parkingLots">
        <span slot="label"><i class="el-icon-s-grid"></i> 停车区</span>
      </el-tab-pane>
      <el-tab-pane label="我的停车" name="myRecords">
        <span slot="label"><i class="el-icon-s-order"></i> 我的停车</span>
      </el-tab-pane>
      <el-tab-pane label="统计分析" name="statistics">
        <span slot="label"><i class="el-icon-data-analysis"></i> 统计分析</span>
      </el-tab-pane>
    </el-tabs>

    <!-- 停车场搜索栏 -->
    <div v-if="activeTab === 'parkingLots'" class="card-container filter-container">
      <div class="filter-header">
        <i class="el-icon-search" style="color: #4A9BFF; margin-right: 8px;"></i>
        <span style="font-weight: 600;">停车场筛选</span>
      </div>
      <div class="filter-content">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8" :md="6" :lg="5">
            <el-input
              v-model="listQuery.search"
              placeholder="搜索停车场名称"
              prefix-icon="el-icon-search"
              clearable
              size="small"
              @keyup.enter.native="handleFilter"
            />
          </el-col>
          <el-col :xs="24" :sm="8" :md="6" :lg="4">
            <el-select
              v-model="listQuery.campus"
              placeholder="选择校区"
              clearable
              size="small"
              style="width: 100%"
              @change="handleFilter"
            >
              <el-option
                v-for="item in campusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="8" :md="6" :lg="4">
            <el-select
              v-model="listQuery.area"
              placeholder="选择区域"
              clearable
              size="small"
              style="width: 100%"
              @change="handleFilter"
            >
              <el-option
                v-for="item in areaOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="8" :md="6" :lg="4">
            <el-select
              v-model="listQuery.status"
              placeholder="运营状态"
              clearable
              size="small"
              style="width: 100%"
              @change="handleFilter"
            >
              <el-option label="正常运营" :value="1" />
              <el-option label="暂停使用" :value="0" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="8" :md="6" :lg="4">
            <el-select
              v-model="listQuery.availableSpaces"
              placeholder="空闲车位"
              clearable
              size="small"
              style="width: 100%"
              @change="handleFilter"
            >
              <el-option label="有空位" value="available" />
              <el-option label="即将满员" value="almostFull" />
              <el-option label="已满员" value="full" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="8" :md="6" :lg="7">
            <div class="filter-buttons">
              <el-button
                type="primary"
                icon="el-icon-search"
                size="small"
                @click="handleFilter"
              >
                搜索
              </el-button>
              <el-button
                type="info"
                icon="el-icon-refresh"
                size="small"
                plain
                @click="fetchData"
              >
                刷新
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 停车记录搜索栏 -->
    <div v-if="activeTab === 'myRecords'" class="card-container filter-container">
      <div class="filter-header">
        <i class="el-icon-search" style="color: #4A9BFF; margin-right: 8px;"></i>
        <span style="font-weight: 600;">停车记录筛选</span>
      </div>
      <div class="filter-content">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8" :md="6" :lg="4">
            <el-select
              v-model="recordsQuery.status"
              placeholder="停车状态"
              clearable
              size="small"
              style="width: 100%"
              @change="fetchRecords"
            >
              <el-option label="进行中" :value="0" />
              <el-option label="已完成" :value="1" />
              <el-option label="异常" :value="2" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="16" :md="12" :lg="10">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              size="small"
              value-format="yyyy-MM-dd"
              @change="handleDateChange"
            />
          </el-col>
          <el-col :xs="24" :sm="8" :md="6" :lg="10">
            <div class="filter-buttons">
              <el-button
                type="primary"
                icon="el-icon-search"
                size="small"
                @click="fetchRecords"
              >
                搜索
              </el-button>
              <el-button
                type="info"
                icon="el-icon-refresh"
                size="small"
                plain
                @click="fetchRecords"
              >
                刷新
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 停车场列表 -->
    <div v-if="activeTab === 'parkingLots'" v-loading="listLoading" element-loading-text="加载中..." class="parking-lots-container">
      <el-row :gutter="20">
        <el-col v-for="item in list" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6" class="card-col">
          <div class="parking-card">
            <div class="card-header">
              <h3 class="title">{{ item.name }}</h3>
              <div class="status-tag" :class="item.status === 1 ? 'operating' : 'paused'">
                <span class="dot" :class="item.status === 1 ? 'operating' : 'paused'"></span>
                {{ item.status === 1 ? '正常运营' : '暂停使用' }}
              </div>
            </div>
            <div class="card-body">
              <div class="campus-info">
                <el-tag size="mini" effect="plain" type="info">
                  {{ item.campus || '主校区' }}
                </el-tag>
                <el-tag size="mini" effect="plain" type="success" style="margin-left: 5px;">
                  {{ item.area || '未分类' }}
                </el-tag>
              </div>
              <p class="address"><i class="el-icon-location"></i> {{ item.address }}</p>

              <div class="utilization-info">
                <div class="utilization-header">
                  <span>车位利用率</span>
                  <span class="utilization-value" :class="getUtilizationClass(item)">
                    {{ calculateUtilization(item) }}%
                  </span>
                </div>
                <el-progress
                  :percentage="calculateUtilization(item)"
                  :status="getUtilizationStatus(item)"
                  :stroke-width="10"
                  :show-text="false"
                  :color="getProgressColor(item)"
                />
                <div class="space-count">
                  <div class="space-count-item">
                    <span class="label">已用:</span>
                    <span class="value">{{ item.occupied_spaces }}</span>
                  </div>
                  <div class="space-count-item">
                    <span class="label">总数:</span>
                    <span class="value">{{ item.total_spaces }}</span>
                  </div>
                  <div class="space-count-item">
                    <span class="label">空闲:</span>
                    <span class="value">{{ item.total_spaces - item.occupied_spaces }}</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- 校园特色信息 -->
            <div class="campus-info-section">
              <div class="section-title">
                <i class="el-icon-info"></i> 联系信息
              </div>
              <div class="campus-info-item">
                <i class="el-icon-time"></i>
                <span>开放时间：{{ item.opening_hours || '全天开放' }}</span>
              </div>
              <div class="campus-info-item">
                <i class="el-icon-user"></i>
                <span>管理员：{{ item.manager }}</span>
              </div>
              <div class="campus-info-item">
                <i class="el-icon-phone-outline"></i>
                <span>联系电话：{{ item.contact }}</span>
              </div>
            </div>



            <div class="card-footer">
              <el-button size="small" type="primary" plain icon="el-icon-view" @click="handleViewDetails(item)">查看详情</el-button>
              <el-dropdown size="small" split-button type="success" @click="handleQuickParking(item)" @command="handleParkingCommand">
                <i class="el-icon-bicycle"></i> 快速停车
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="normalParking">
                    <div class="parking-type-item">
                      <div class="parking-type-info">
                        <i class="el-icon-bicycle"></i> 普通车位
                      </div>
                      <el-tag size="mini" type="success" v-if="getAvailableSpaceCount(item, 1) > 0">
                        {{ getAvailableSpaceCount(item, 1) }} 个可用
                      </el-tag>
                      <el-tag size="mini" type="info" v-else>无可用</el-tag>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="roles.includes('security') || roles.includes('admin')" command="disabledParking">
                    <div class="parking-type-item">
                      <div class="parking-type-info">
                        <i class="el-icon-user"></i> 残疾人车位
                      </div>
                      <el-tag size="mini" type="success" v-if="getAvailableSpaceCount(item, 2) > 0">
                        {{ getAvailableSpaceCount(item, 2) }} 个可用
                      </el-tag>
                      <el-tag size="mini" type="info" v-else>无可用</el-tag>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="chargingParking">
                    <div class="parking-type-item">
                      <div class="parking-type-info">
                        <i class="el-icon-lightning"></i> 充电车位
                      </div>
                      <el-tag size="mini" type="success" v-if="getAvailableSpaceCount(item, 3) > 0">
                        {{ getAvailableSpaceCount(item, 3) }} 个可用
                      </el-tag>
                      <el-tag size="mini" type="info" v-else>无可用</el-tag>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 空数据提示 -->
      <div v-if="list.length === 0 && !listLoading" class="empty-data">
        <el-alert
          title="没有找到停车场数据"
          type="info"
          :closable="false"
          center
          show-icon
        >
          <template slot="title">
            <div class="empty-title">
              <i class="el-icon-info"></i>
              <span>没有找到停车场数据</span>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 停车场分页 -->
    <div v-if="activeTab === 'parkingLots'" class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="listQuery.page"
        :page-sizes="[8, 16, 24, 32]"
        :page-size="listQuery.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 停车记录列表 -->
    <div v-if="activeTab === 'myRecords'" class="records-container">


      <!-- 进行中的停车记录 -->
      <div v-if="activeRecords.length > 0" class="card-container active-records-section">
        <div class="card-title">
          <i class="el-icon-time" style="color: #E6A23C; margin-right: 8px;"></i>
          <span>进行中的停车</span>
          <el-tag type="warning" size="small" effect="plain" style="margin-left: 10px;">
            {{ activeRecords.length }} 条记录
          </el-tag>
        </div>
        <el-row :gutter="20">
          <el-col v-for="record in activeRecords" :key="record.id" :xs="24" :sm="12" :md="8" :lg="6">
            <active-parking-card
              :record="record"
              :loading="recordsLoading"
              @view-details="handleViewRecordDetails"
              @end-parking="handleEndParking"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 已结束停车记录 -->
      <finished-parking-list
        :user-id="userId"
        :is-admin="isAdmin"
        @view-details="handleViewRecordDetails"
      />
    </div>



    <!-- 统计图表 -->
    <div v-if="activeTab === 'statistics'" class="statistics-container">
      <!-- 统计概览 -->
      <div class="card-container stats-overview">
        <div class="card-title">
          <i class="el-icon-data-analysis" style="color: #4A9BFF; margin-right: 8px;"></i>
          <span>停车数据概览</span>
          <el-select v-model="statsTimeRange" size="small" style="float: right; width: 120px">
            <el-option label="最近7天" value="7" />
            <el-option label="最近30天" value="30" />
            <el-option label="最近90天" value="90" />
          </el-select>
        </div>

        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :xs="24" :sm="12" :md="6">
            <div class="stat-card">
              <div class="stat-title">总停车次数</div>
              <div class="stat-value">{{ statsData.totalParkingCount || 0 }}</div>
              <i class="el-icon-s-data stat-icon"></i>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <div class="stat-card">
              <div class="stat-title">平均停车时长</div>
              <div class="stat-value">{{ statsData.avgDuration || '0小时' }}</div>
              <i class="el-icon-time stat-icon"></i>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <div class="stat-card">
              <div class="stat-title">常用停车场</div>
              <div class="stat-value">{{ statsData.topParkingLot || '无数据' }}</div>
              <i class="el-icon-location stat-icon"></i>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6">
            <div class="stat-card">
              <div class="stat-title">当前进行中</div>
              <div class="stat-value">{{ activeRecords.length || 0 }}</div>
              <i class="el-icon-bicycle stat-icon"></i>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-row :gutter="20">
        <!-- 停车次数统计 -->
        <el-col :xs="24" :sm="24" :md="12">
          <div class="card-container">
            <div class="card-title">
              <i class="el-icon-data-line" style="color: #4A9BFF; margin-right: 8px;"></i>
              <span>最近停车次数</span>
            </div>
            <div ref="parkingCountChart" class="chart-container" />
          </div>
        </el-col>

        <!-- 停车时长统计 -->
        <el-col :xs="24" :sm="24" :md="12">
          <div class="card-container">
            <div class="card-title">
              <i class="el-icon-time" style="color: #E6A23C; margin-right: 8px;"></i>
              <span>停车时长分布</span>
            </div>
            <div ref="parkingDurationChart" class="chart-container" />
          </div>
        </el-col>

        <!-- 常用停车场统计 -->
        <el-col :xs="24" :sm="12">
          <div class="card-container">
            <div class="card-title">
              <i class="el-icon-location" style="color: #67C23A; margin-right: 8px;"></i>
              <span>常用停车场</span>
            </div>
            <div ref="frequentParkingLotsChart" class="chart-container" />
          </div>
        </el-col>

        <!-- 常用车位类型统计 -->
        <el-col :xs="24" :sm="12">
          <div class="card-container">
            <div class="card-title">
              <i class="el-icon-s-grid" style="color: #909399; margin-right: 8px;"></i>
              <span>常用车位类型</span>
            </div>
            <div ref="spaceTypesChart" class="chart-container" />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 开始停车组件 -->
    <start-parking
      v-if="showStartParking"
      :parking-lot-id="startParkingParams.parkingLotId"
      :parking-lot-name="startParkingParams.parkingLotName"
      :spaces="spaces"
      @success="handleParkingSuccess"
      @cancel="handleCancelParking"
      @refresh-spaces="refreshParkingSpaces"
    />

    <!-- 快速停车对话框 -->
    <quick-parking-dialog
      v-if="showQuickParkingDialog"
      :key="dialogKey"
      :parking-lot-id="startParkingParams.parkingLotId"
      :parking-lot-name="startParkingParams.parkingLotName"
      :space-type="startParkingParams.spaceType"
      @success="handleParkingSuccess"
      @cancel="handleCancelParking"
    />

    <!-- 结束停车对话框 -->
    <el-dialog
      title="结束停车"
      :visible.sync="endParkingDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="end-parking-dialog">
        <div v-if="currentRecord" class="record-info">
          <div class="info-item">
            <span class="label">车辆：</span>
            <span class="value">{{ getVehicleInfo(currentRecord) }}</span>
          </div>
          <div class="info-item">
            <span class="label">停车场：</span>
            <span class="value">{{ currentRecord.parking_lot_name || '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="label">车位：</span>
            <span class="value">{{ currentRecord.parking_space_number || '未知' }}</span>
          </div>
          <div class="info-item">
            <span class="label">开始时间：</span>
            <span class="value">{{ formatDate(currentRecord.entry_time) }}</span>
          </div>
          <div class="info-item">
            <span class="label">停车时长：</span>
            <span class="value">{{ calculateDuration(currentRecord.entry_time) }}</span>
          </div>
        </div>

        <el-form :model="endParkingForm" label-width="80px" class="end-parking-form">
          <el-form-item label="备注">
            <el-input
              v-model="endParkingForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="可选填写结束停车备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="endParkingDialogVisible = false">取消</el-button>
        <el-button type="danger" :loading="submitLoading" @click="submitEndParking">确认结束</el-button>
      </span>
    </el-dialog>

    <!-- 记录详情对话框 -->
    <el-dialog
      title="停车记录详情"
      :visible.sync="recordDetailDialogVisible"
      width="600px"
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="车辆信息">{{ getVehicleInfo(currentRecord) }}</el-descriptions-item>
          <el-descriptions-item label="停车场">{{ currentRecord.parking_lot_name || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="车位编号">{{ currentRecord.parking_space_number || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="车位类型">{{ getSpaceTypeText(currentRecord.parking_space_type) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDate(currentRecord.entry_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ currentRecord.exit_time ? formatDate(currentRecord.exit_time) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="停车时长">{{ calculateParkingDuration(currentRecord) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentRecord.created_at) }}</el-descriptions-item>
          <el-descriptions-item :span="2" label="备注">
            {{ currentRecord.remarks || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentRecord.status === 0" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button type="danger" @click="handleEndParkingFromDetail">结束停车</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getParkingLots, getParkingSpaces, updateParkingSpaceStatus } from '@/api/parkinglot'
import { getUserParkingRecords, endParkingRecord, getParkingStats } from '@/api/parking'
import { getActiveChargingRecords as fetchActiveChargingRecords } from '@/api/charging'
import StartParking from '@/components/StartParking'
import QuickParkingDialog from '@/components/QuickParkingDialog'
import ActiveParkingCard from '@/components/ActiveParkingCard'
import FinishedParkingList from '@/components/FinishedParkingList'

import * as echarts from 'echarts'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import socketService from '@/utils/socket'

// 添加dayjs插件以支持时间计算
dayjs.extend(duration)

export default {
  name: 'ParkingCenter',
  components: {
    StartParking,
    QuickParkingDialog,
    ActiveParkingCard,
    FinishedParkingList
  },
  data() {
    return {
      // 当前激活的标签页
      activeTab: 'parkingLots',

      // 自动刷新定时器
      autoRefreshTimer: null,
      autoRefreshInterval: 60000, // 60秒自动刷新一次

      // 停车场列表相关数据
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 8,
        search: '',
        status: '',
        campus: '',
        area: '',
        availableSpaces: '',
        sort_field: 'id',
        sort_order: 'asc'
      },

      // 校区和区域选项
      campusOptions: [
        { label: '主校区', value: '主校区' },
        { label: '东校区', value: '东校区' }
      ],
      areaOptions: [
        { label: '教学区', value: '教学区' },
        { label: '宿舍区', value: '宿舍区' },
        { label: '图书馆区', value: '图书馆区' },
        { label: '食堂区', value: '食堂区' },
        { label: '体育区', value: '体育区' }
      ],



      // 停车记录相关数据
      records: [],
      activeRecords: [],
      recordsTotal: 0,
      recordsLoading: false,
      recordsQuery: {
        page: 1,
        limit: 10,
        status: '',
        start_date: '',
        end_date: ''
      },
      dateRange: [],

      // 统计图表相关数据
      statsTimeRange: '7',
      statsLoading: false,
      parkingCountChart: null,
      parkingDurationChart: null,
      frequentParkingLotsChart: null,
      spaceTypesChart: null,
      statsData: {
        totalParkingCount: 0,
        avgDuration: '0小时',
        topParkingLot: '无数据',
        activeCount: 0
      },

      // 缓存对象，用于存储已经请求过的车位数量
      spaceCountCache: {},

      // 对话框相关数据
      parkingDialogVisible: false,
      endParkingDialogVisible: false,
      recordDetailDialogVisible: false,
      submitLoading: false,
      currentRecord: null,
      endParkingForm: {
        id: null,
        remarks: ''
      },

      // 车位选择相关数据
      selectedParkingLot: null,
      spaces: [],
      spacesLoading: false,
      spacesQuery: {
        search: '',
        type: '',
        status: '' // 状态筛选，空字符串表示显示所有状态
      },
      selectedSpace: null,
      showStartParking: false,
      showQuickParkingDialog: false,
      dialogKey: 0, // 添加dialogKey属性，用于强制重新创建对话框组件
      startParkingParams: {
        parkingLotId: null,
        parkingLotName: '',
        parkingSpaceId: null,
        spaceType: '0'
      },


    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'userId'
    ]),
    isAdmin() {
      return this.roles.includes('admin')
    },
    filteredSpaces() {
      if (!this.spaces.length) return []

      return this.spaces.filter(space => {
        // 如果不是管理员，只显示空闲车位
        if (!this.isAdmin && space.status !== 0) return false

        // 按编号搜索
        if (this.spacesQuery.search && !space.space_number.toString().includes(this.spacesQuery.search)) {
          return false
        }

        // 按类型筛选
        if (this.spacesQuery.type && space.type !== parseInt(this.spacesQuery.type)) {
          return false
        }

        // 按状态筛选（仅管理员）
        if (this.isAdmin && this.spacesQuery.status !== '' && space.status !== parseInt(this.spacesQuery.status)) {
          return false
        }

        return true
      })
    }
  },
  created() {
    // 在created钩子中只加载停车场数据
    this.fetchData()
  },
  mounted() {
    // 监听标签页变化，加载相应数据
    this.$nextTick(() => {
      // 不再重复加载停车场数据
      // this.fetchData() - 移除重复调用

      if (this.activeTab === 'myRecords') {
        // 只有当前标签页是停车记录时才加载记录
        this.fetchRecords()
        this.startAutoRefresh()
      } else if (this.activeTab === 'statistics') {
        // 延迟初始化图表，确保 DOM 已经渲染
        setTimeout(() => {
          this.initCharts()
          console.log('延迟初始化图表')
        }, 500)
      }

      // 设置WebSocket监听器
      this.setupWebSocketListeners()
    })
  },
  beforeDestroy() {
    // 销毁图表实例，避免内存泄漏
    this.disposeCharts()
    window.removeEventListener('resize', this.resizeCharts)

    // 清除自动刷新定时器
    this.stopAutoRefresh()

    // 移除WebSocket监听器
    this.removeWebSocketListeners()
  },
  methods: {
    // 标签页切换处理
    handleTabClick(tab) {
      console.log('切换到标签页:', tab.name)
      // 切换到停车记录标签页
      if (tab.name === 'myRecords') {
        this.fetchRecords()
        this.startAutoRefresh()
      } else {
        // 切换到其他标签页，停止自动刷新
        this.stopAutoRefresh()

        if (tab.name === 'statistics') {
          console.log('切换到统计分析标签页，准备初始化图表')
          // 延迟初始化图表，确保 DOM 已经渲染
          setTimeout(() => {
            this.initCharts()
          }, 500)
        }
      }
    },

    // 启动自动刷新
    startAutoRefresh() {
      // 先清除现有定时器
      this.stopAutoRefresh()

      // 设置新的定时器
      this.autoRefreshTimer = setInterval(() => {
        if (this.activeTab === 'myRecords' && !this.recordsLoading) {
          console.log('自动刷新停车记录...')
          // 添加时间戳参数，确保每次刷新都是新的请求
          this.fetchRecords(true) // 传入true表示强制刷新
        }
      }, this.autoRefreshInterval)

      console.log('已启动自动刷新，间隔：', this.autoRefreshInterval / 1000, '秒')
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.autoRefreshTimer) {
        clearInterval(this.autoRefreshTimer)
        this.autoRefreshTimer = null
        console.log('已停止自动刷新')
      }
    },

    // 获取进行中的充电记录
    getActiveChargingRecords() {
      return new Promise((resolve, reject) => {
        // 准备请求参数
        const params = {
          status: 0, // 只获取进行中的充电记录
          user_id: this.userId
        }

        // 调用API获取进行中的充电记录
        fetchActiveChargingRecords(params).then(response => {
          let data = response
          if (response.data) {
            data = response.data
          }

          let chargingRecords = []
          if (Array.isArray(data)) {
            chargingRecords = data
          } else if (data.items) {
            chargingRecords = data.items
          } else if (data.data && Array.isArray(data.data)) {
            chargingRecords = data.data
          } else if (data.data && data.data.items) {
            chargingRecords = data.data.items
          }

          console.log('获取到进行中的充电记录:', chargingRecords.length, '条')
          resolve(chargingRecords)
        }).catch(error => {
          console.error('获取充电记录失败:', error)
          // 如果获取失败，返回空数组，不影响停车记录的显示
          resolve([])
        })
      })
    },

    // 获取停车场列表
    fetchData() {
      this.listLoading = true
      getParkingLots(this.listQuery).then(response => {
        // 添加调试信息
        console.log('停车场原始响应数据:', JSON.stringify(response))

        let data = response
        if (response.data) {
          data = response.data
          console.log('从 response.data 提取的数据:', JSON.stringify(data))
        }

        let parkingLots = []
        if (Array.isArray(data)) {
          parkingLots = data
          this.total = data.length
        } else if (data.items) {
          parkingLots = data.items
          this.total = data.total || data.items.length
        } else if (data.data && Array.isArray(data.data)) {
          parkingLots = data.data
          this.total = data.total || data.data.length
        } else {
          console.error('无法识别的数据格式:', data)
          parkingLots = []
          this.total = 0
        }

        // 在前端进行筛选
        // 1. 校区筛选
        if (this.listQuery.campus) {
          parkingLots = parkingLots.filter(lot => {
            return lot.campus === this.listQuery.campus
          })
        }

        // 2. 区域筛选
        if (this.listQuery.area) {
          parkingLots = parkingLots.filter(lot => {
            return lot.area === this.listQuery.area
          })
        }

        // 3. 空闲车位筛选
        if (this.listQuery.availableSpaces) {
          parkingLots = parkingLots.filter(lot => {
            const availableCount = lot.total_spaces - lot.occupied_spaces
            const utilizationRate = lot.total_spaces > 0 ? (lot.occupied_spaces / lot.total_spaces) * 100 : 0

            switch (this.listQuery.availableSpaces) {
              case 'available':
                // 有空位：至少有1个空闲车位
                return availableCount > 0
              case 'almostFull':
                // 即将满员：利用率在80%以上，但还有空位
                return utilizationRate >= 80 && availableCount > 0
              case 'full':
                // 已满员：没有空闲车位
                return availableCount === 0
              default:
                return true
            }
          })
        }

        // 更新总数
        this.total = parkingLots.length

        // 打印每个停车场的详细信息
        console.log('停车场列表详细信息:')
        parkingLots.forEach((lot, index) => {
          console.log(`停车场 ${index + 1}:`, lot)
          console.log(`- 管理员: ${lot.manager || '无'}`)
          console.log(`- 联系电话: ${lot.contact || '无'}`)
        })

        // 更新列表
        this.list = parkingLots
        this.listLoading = false
      }).catch(error => {
        console.error('获取停车场列表失败', error)
        this.$message.error('获取停车场列表失败')
        this.listLoading = false
      })
    },

    // 获取停车记录
    fetchRecords(forceRefresh = false) {
      // 如果已经在加载中，则不重复请求
      if (this.recordsLoading && !forceRefresh) {
        console.log('停车记录正在加载中，跳过重复请求')
        return
      }

      this.recordsLoading = true

      // 显示加载中提示
      const loadingMessage = this.$message({
        message: '正在加载停车记录...',
        type: 'info',
        duration: 0
      })

      // 准备请求参数
      const params = { ...this.recordsQuery }

      // 增大分页大小，获取更多记录
      params.per_page = 100

      // 如果是强制刷新，添加时间戳参数
      if (forceRefresh) {
        params._t = new Date().getTime()
        console.log('强制刷新停车记录，添加时间戳:', params._t)
      }

      // 添加用户ID参数，确保只获取当前用户的停车记录
      if (this.userId) {
        params.user_id = this.userId
        console.log('添加用户ID参数:', this.userId)
      }

      // 如果是管理员，添加isAdmin参数
      if (this.isAdmin) {
        params.isAdmin = true
        console.log('添加isAdmin参数')
      }

      // 首先获取进行中的充电记录，然后过滤停车记录
      this.getActiveChargingRecords().then(chargingRecords => {
        // 获取正在充电的车辆ID列表
        const chargingVehicleIds = chargingRecords.map(record => record.vehicle_id)
        console.log('正在充电的车辆ID:', chargingVehicleIds)

        // 获取停车记录
        getUserParkingRecords(params).then(response => {
          // 关闭加载提示
          loadingMessage.close()

          // 简化数据处理逻辑
          if (response && response.code === 20000 && response.data && response.data.items) {
            // 过滤掉正在充电的车辆的停车记录
            this.records = response.data.items.filter(record => !chargingVehicleIds.includes(record.vehicle_id))
            this.recordsTotal = this.records.length

            // 只在有数据时显示成功提示
            if (this.records.length > 0) {
              this.$message.success(`成功加载 ${this.records.length} 条停车记录`)
            } else {
              this.$message.info('没有找到停车记录')
            }

            // 分离出进行中的停车记录
            this.activeRecords = this.records.filter(record => record.status === 0)

            // 如果有状态过滤，则不显示进行中的记录卡片
            if (this.recordsQuery.status !== '' && this.recordsQuery.status !== 0) {
              this.activeRecords = []
            }
          } else {
            console.error('无法识别的停车记录数据格式:', response)
            this.$message.warning('没有找到停车记录或数据格式不正确')
            this.records = []
            this.recordsTotal = 0
            this.activeRecords = []
          }

          this.recordsLoading = false
        }).catch(error => {
          // 关闭加载提示
          loadingMessage.close()

          console.error('获取停车记录失败', error)
          this.$message.error('获取停车记录失败: ' + (error.message || '未知错误'))
          this.records = []
          this.recordsTotal = 0
          this.activeRecords = []
          this.recordsLoading = false
        })
      }).catch(error => {
        console.error('获取充电记录失败', error)

        // 如果获取充电记录失败，仍然继续获取停车记录
        getUserParkingRecords(params).then(response => {
          // 关闭加载提示
          loadingMessage.close()

          let data = response
          if (response.data) {
            data = response.data
          }

          // 处理返回的数据（不过滤充电记录）
          if (data.items) {
            this.records = data.items
            this.recordsTotal = data.total || data.items.length

            if (this.records.length > 0) {
              this.$message.success(`成功加载 ${this.records.length} 条停车记录`)
            }

            // 分离出进行中的停车记录
            this.activeRecords = this.records.filter(record => record.status === 0)

            // 如果有状态过滤，则不显示进行中的记录卡片
            if (this.recordsQuery.status !== '' && this.recordsQuery.status !== 0) {
              this.activeRecords = []
            }
          } else if (data.data && data.data.items) {
            this.records = data.data.items
            this.recordsTotal = data.data.total || data.data.items.length

            if (this.records.length > 0) {
              this.$message.success(`成功加载 ${this.records.length} 条停车记录`)
            }

            // 分离出进行中的停车记录
            this.activeRecords = this.records.filter(record => record.status === 0)

            // 如果有状态过滤，则不显示进行中的记录卡片
            if (this.recordsQuery.status !== '' && this.recordsQuery.status !== 0) {
              this.activeRecords = []
            }
          } else if (Array.isArray(data)) {
            this.records = data
            this.recordsTotal = data.length

            if (this.records.length > 0) {
              this.$message.success(`成功加载 ${this.records.length} 条停车记录`)
            }

            // 分离出进行中的停车记录
            this.activeRecords = this.records.filter(record => record.status === 0)

            // 如果有状态过滤，则不显示进行中的记录卡片
            if (this.recordsQuery.status !== '' && this.recordsQuery.status !== 0) {
              this.activeRecords = []
            }
          } else {
            console.error('无法识别的停车记录数据格式:', data)
            this.$message.warning('没有找到停车记录或数据格式不正确')
            this.records = []
            this.recordsTotal = 0
            this.activeRecords = []
          }

          this.recordsLoading = false
        }).catch(error => {
          // 关闭加载提示
          loadingMessage.close()

          console.error('获取停车记录失败', error)
          this.$message.error('获取停车记录失败: ' + (error.message || '未知错误'))
          this.records = []
          this.recordsTotal = 0
          this.activeRecords = []
          this.recordsLoading = false
        })
      })
    },

    calculateUtilization(item) {
      if (!item.total_spaces || item.total_spaces === 0) return 0
      return Math.round((item.occupied_spaces / item.total_spaces) * 100)
    },

    getUtilizationStatus(row) {
      const rate = this.calculateUtilization(row)
      if (rate >= 90) return 'exception'
      if (rate >= 70) return 'warning'
      return 'success'
    },

    getUtilizationClass(row) {
      const rate = this.calculateUtilization(row)
      if (rate >= 90) return 'high'
      if (rate >= 70) return 'medium'
      return 'low'
    },

    getProgressColor(item) {
      const rate = this.calculateUtilization(item)
      if (rate >= 90) return '#F56C6C' // 红色
      if (rate >= 70) return '#E6A23C' // 黄色
      return '#67C23A' // 绿色
    },

    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },

    handleSizeChange(val) {
      this.listQuery.limit = val
      this.fetchData()
    },

    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchData()
    },

    handleViewDetails(item) {
      // 对于普通用户，跳转到用户版停车场详情页面
      this.$router.push(`/profile/lot-details/${item.id}`)
    },



    // 处理快速停车按钮点击
    handleQuickParking(item) {
      // 设置停车参数
      this.selectedParkingLot = item
      this.startParkingParams = {
        parkingLotId: item.id,
        parkingLotName: item.name,
        spaceType: '0' // 默认使用普通车位
      }

      // 更新dialogKey，强制重新创建对话框组件
      this.dialogKey++
      console.log('更新dialogKey:', this.dialogKey)

      // 打开快速停车对话框
      this.showQuickParkingDialog = true
    },

    // 原始的停车处理方法（使用StartParking组件）
    handleStartParking(item, spaceType = null) {
      // 直接打开停车对话框，并加载车位数据
      this.selectedParkingLot = item
      this.spacesLoading = true

      // 构建查询参数
      const params = { limit: 100 }

      // 如果指定了车位类型，添加到查询参数中
      if (spaceType !== null && spaceType !== undefined) {
        // 直接使用统一的车位类型编码
        params.type = spaceType

        // 检查该类型车位是否有可用的
        const availableCount = this.getAvailableSpaceCount(item, spaceType)
        if (availableCount <= 0) {
          const typeText = this.getSpaceTypeText(spaceType)
          this.$message.warning(`该停车场没有可用的${typeText}`)
          this.spacesLoading = false
          return
        }
      }

      // 获取用户角色
      const roles = this.roles || []
      const isAdmin = roles.includes('admin')
      const isSecurity = roles.includes('security')

      // 检查权限
      if (spaceType === 1 && !isSecurity && !isAdmin) {
        this.$message.warning('只有保安和管理员可以使用残疾人车位')
        this.spacesLoading = false
        return
      }

      getParkingSpaces(item.id, params).then(response => {
        let allSpaces = []
        let data = response

        if (response.data) {
          data = response.data
        }

        if (Array.isArray(data)) {
          allSpaces = data
        } else if (data.items) {
          allSpaces = data.items
        } else if (data.data && Array.isArray(data.data)) {
          allSpaces = data.data
        }

        // 如果指定了车位类型，过滤车位
        if (spaceType && allSpaces.length > 0) {
          allSpaces = allSpaces.filter(space => space.type === spaceType)
        }

        // 获取可用车位
        const availableSpaces = allSpaces.filter(space => space.status === 0)

        if (availableSpaces.length > 0) {
          // 将所有车位保存到组件中
          this.spaces = allSpaces

          // 设置停车参数
          this.startParkingParams = {
            parkingLotId: item.id,
            parkingLotName: item.name
          }

          // 直接打开停车对话框
          this.showStartParking = true
        } else {
          const typeText = spaceType !== null && spaceType !== undefined ?
            (spaceType === 0 ? '普通' :
             spaceType === 1 ? '残疾人专用' :
             spaceType === 2 ? '大型' : '') : ''
          this.$message.warning(`该停车场没有可用的${typeText}车位`)
        }

        this.spacesLoading = false
      }).catch(error => {
        console.error('获取车位列表失败', error)
        this.$message.error('获取车位列表失败')
        this.spacesLoading = false
      })
    },

    fetchSpaces() {
      if (!this.selectedParkingLot) return

      this.spacesLoading = true
      getParkingSpaces(this.selectedParkingLot.id, {
        limit: 100,
        // 如果是管理员，获取所有状态的车位，否则只获取空闲车位
        status: this.isAdmin ? '' : 0
      }).then(response => {
        let data = response
        if (response.data) {
          data = response.data
        }

        if (Array.isArray(data)) {
          this.spaces = data
        } else if (data.items) {
          this.spaces = data.items
        } else if (data.data && Array.isArray(data.data)) {
          this.spaces = data.data
        } else {
          console.error('无法识别的车位数据格式:', data)
          this.spaces = []
        }

        this.spacesLoading = false
      }).catch(error => {
        console.error('获取车位列表失败', error)
        this.$message.error('获取车位列表失败')
        this.spacesLoading = false
      })
    },

    handleSpacesFilter() {
      // 本地过滤，不需要重新请求
    },

    getParkingSpaceClasses(space) {
      return {
        'available': space.status === 0,
        'occupied': space.status === 1,
        'maintenance': space.status === 2,
        [`type-${space.type}`]: true,
        'selected': this.selectedSpace && this.selectedSpace.id === space.id
      }
    },

    getVehicleInfo(space) {
      if (!space.vehicle_info) {
        return '加载中...'
      }
      return space.vehicle_info
    },

    // 已移除selectSpace和confirmStartParking方法，因为这些功能现在在StartParking组件中实现

    handleParkingSuccess(recordData) {
      this.showStartParking = false
      this.showQuickParkingDialog = false
      this.$message.success('停车成功')

      console.log('停车成功，记录数据:', recordData)

      // 如果有记录数据，清除相应停车场的车位数量缓存
      if (recordData && recordData.parking_lot_id) {
        this.clearSpaceCountCache(recordData.parking_lot_id)
      }

      // 刷新停车场列表
      this.fetchData()

      // 强制刷新停车记录，无论当前是否在记录页面
      setTimeout(() => {
        console.log('延时刷新停车记录')
        this.fetchRecords(true) // 传入true表示强制刷新
      }, 1000) // 延时一秒，等待后端处理完成
    },

    // 处理取消停车
    handleCancelParking() {
      console.log('取消停车操作')
      this.showStartParking = false
      this.showQuickParkingDialog = false
      // 重置相关状态
      this.selectedParkingLot = null
      this.spaces = []
    },

    // 刷新停车位数据
    refreshParkingSpaces() {
      if (!this.selectedParkingLot) return

      console.log('刷新停车位数据')
      this.spacesLoading = true

      getParkingSpaces(this.selectedParkingLot.id, { limit: 100 })
        .then(response => {
          let data = response
          if (response.data) {
            data = response.data
          }

          if (Array.isArray(data)) {
            this.spaces = data
          } else if (data.items) {
            this.spaces = data.items
          } else if (data.data && Array.isArray(data.data)) {
            this.spaces = data.data
          } else {
            console.error('无法识别的车位数据格式:', data)
          }

          // 检查是否还有可用车位
          const availableSpaces = this.spaces.filter(space => space.status === 0)
          if (availableSpaces.length === 0) {
            this.$message.warning('该停车场没有可用的车位了')
          }

          this.spacesLoading = false
        })
        .catch(error => {
          console.error('获取车位列表失败', error)
          this.$message.error('获取车位列表失败')
          this.spacesLoading = false
        })
    },

    // 处理结束停车
    handleEndParking(record) {
      if (!record || !record.id) {
        this.$message.error('停车记录信息不完整，无法结束停车')
        return
      }

      this.currentRecord = record
      this.endParkingForm.id = record.id
      this.endParkingForm.remarks = ''
      this.endParkingDialogVisible = true
    },

    // 从详情对话框中结束停车
    handleEndParkingFromDetail() {
      this.recordDetailDialogVisible = false
      this.handleEndParking(this.currentRecord)
    },

    // 提交结束停车
    submitEndParking() {
      if (!this.currentRecord || !this.endParkingForm.id) {
        this.$message.error('停车记录信息不完整')
        return
      }

      this.submitLoading = true

      // 显示加载中提示
      const loadingMessage = this.$message({
        message: '正在结束停车...',
        type: 'info',
        duration: 0
      })

      endParkingRecord(this.endParkingForm.id, this.endParkingForm.remarks)
        .then(response => {
          // 关闭加载提示
          loadingMessage.close()

          // 检查响应状态
          if (response && response.code === 20000) {
            this.$message.success('停车已结束')
            this.endParkingDialogVisible = false

            // 立即从活动记录列表中移除该记录
            if (this.activeRecords && this.activeRecords.length > 0) {
              this.activeRecords = this.activeRecords.filter(record => record.id !== this.endParkingForm.id)
            }

            // 获取停车记录的车位和停车场信息，用于更新车位状态
            const parkingLotId = this.currentRecord.parking_lot_id
            const parkingSpaceId = this.currentRecord.parking_space_id

            // 如果当前正在查看该停车场，直接更新车位状态
            if (this.selectedParkingLot && this.selectedParkingLot.id === parkingLotId && this.spaces && this.spaces.length > 0) {
              // 找到对应的车位并更新状态
              const spaceIndex = this.spaces.findIndex(space => space.id === parkingSpaceId)
              if (spaceIndex !== -1) {
                // 直接更新车位状态为可用
                this.spaces[spaceIndex].status = 0
                this.spaces[spaceIndex].current_vehicle_id = null

                // 强制视图更新
                this.spaces = [...this.spaces]

                console.log('直接更新车位状态为可用:', this.spaces[spaceIndex])
              }
            }

            // 刷新停车记录
            this.fetchRecords()

            // 刷新停车场列表
            this.fetchData()

            // 如果有车位可视化组件，刷新车位状态
            if (this.selectedParkingLot) {
              this.refreshParkingSpaces()
            }

            // 通知其他组件停车已结束
            this.$emit('parking-ended', {
              recordId: this.endParkingForm.id,
              parkingLotId: this.currentRecord.parking_lot_id,
              parkingSpaceId: this.currentRecord.parking_space_id
            })
          } else {
            const errorMsg = response && response.message ? response.message : '结束停车失败'
            this.$message.error(errorMsg)
            console.error('结束停车失败，响应数据：', response)
          }
        })
        .catch(error => {
          // 关闭加载提示
          loadingMessage.close()

          console.error('结束停车失败', error)
          this.$message.error(
            error.response && error.response.data
              ? error.response.data.message
              : '结束停车失败: ' + (error.message || '未知错误')
          )
        })
        .finally(() => {
          this.submitLoading = false
        })
    },

    // 查看记录详情
    handleViewRecordDetails(record) {
      this.currentRecord = record
      this.recordDetailDialogVisible = true
    },

    // 处理记录操作下拉菜单命令
    handleRecordCommand(command, record) {
      if (command === 'end') {
        this.handleEndParking(record)
      } else if (command === 'details') {
        this.handleViewRecordDetails(record)
      }
    },

    // 日期过滤器变化
    handleDateChange(val) {
      if (val) {
        this.recordsQuery.start_date = val[0]
        this.recordsQuery.end_date = val[1]
      } else {
        this.recordsQuery.start_date = ''
        this.recordsQuery.end_date = ''
      }
      this.fetchRecords()
    },

    // 停车记录分页处理
    handleRecordsSizeChange(val) {
      this.recordsQuery.limit = val
      this.fetchRecords()
    },

    handleRecordsCurrentChange(val) {
      this.recordsQuery.page = val
      this.fetchRecords()
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
    },

    // 计算停车时长
    calculateDuration(startTime, endTime) {
      if (!startTime) return '-'

      const start = dayjs(startTime)
      const end = endTime ? dayjs(endTime) : dayjs()

      const diff = dayjs.duration(end.diff(start))
      const days = diff.days()
      const hours = diff.hours()
      const minutes = diff.minutes()

      let result = ''
      if (days > 0) result += `${days}天`
      if (hours > 0 || days > 0) result += `${hours}小时`
      result += `${minutes}分钟`

      return result
    },

    // 计算停车时长（已结束的记录）
    calculateParkingDuration(record) {
      if (!record.entry_time) return '-'

      if (record.status === 0) {
        // 进行中的停车，计算到当前时间
        return this.calculateDuration(record.entry_time)
      } else if (record.exit_time) {
        // 已结束的停车，计算实际时长
        return this.calculateDuration(record.entry_time, record.exit_time)
      }

      return '-'
    },

    // 处理车位操作
    handleSpaceAction(command, space) {
      if (!space) return

      // 根据命令执行相应操作
      switch (command) {
        case 'setAvailable':
          this.updateSpaceStatus(space, 0) // 设置为空闲
          break
        case 'setMaintenance':
          this.updateSpaceStatus(space, 2) // 设置为维护中
          break
        default:
          console.warn('未知的车位操作命令:', command)
      }
    },

    // 更新车位状态
    updateSpaceStatus(space, status) {
      const statusText = status === 0 ? '空闲' : status === 1 ? '已占用' : '维护中'
      const loadingInstance = this.$loading({
        lock: true,
        text: `正在将车位 ${space.space_number} 设置为${statusText}...`,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      updateParkingSpaceStatus(space.id, status)
        .then(() => {
          this.$message.success(`车位 ${space.space_number} 已设置为${statusText}`)

          // 更新本地车位状态
          space.status = status
          if (status !== 1) {
            space.current_vehicle_id = null
          }

          // 如果设置为维护中，且该车位已被选中，则取消选中
          if (status === 2 && this.selectedSpace && this.selectedSpace.id === space.id) {
            this.selectedSpace = null
          }

          // 刷新停车场列表
          this.fetchData()
        })
        .catch(error => {
          console.error('更新车位状态失败', error)
          this.$message.error(
            error.response && error.response.data
              ? error.response.data.message
              : '更新车位状态失败'
          )
        })
        .finally(() => {
          loadingInstance.close()
        })
    },

    // 获取车辆信息
    getVehicleInfo(record) {
      if (!record) return '-'

      if (record.vehicle_info) {
        return record.vehicle_info
      }

      if (record.vehicle) {
        const vehicle = record.vehicle
        // 优先使用数据库字段名 b_num
        return `${vehicle.b_num || vehicle.bike_number || '未知车牌'} (${vehicle.brand || '未知品牌'} ${vehicle.color || ''})`
      }

      return `车辆ID: ${record.vehicle_id || '-'}`
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '已完成',
        2: '异常'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取车位类型文本
    getSpaceTypeText(type) {
      // 确保类型是数字
      const numType = parseInt(type, 10)
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[numType] || '普通车位'
    },

    // 获取指定停车场和车位类型的可用车位数量
    getAvailableSpaceCount(parkingLot, spaceType) {
      // 如果没有停车场信息，返回0
      if (!parkingLot || !parkingLot.id) {
        return 0
      }

      // 生成缓存键
      const cacheKey = `${parkingLot.id}_${spaceType}`

      // 如果有缓存，直接返回缓存的值
      if (this.spaceCountCache[cacheKey] !== undefined) {
        return this.spaceCountCache[cacheKey]
      }

      // 如果我们有缓存的车位数据，尝试从中计算
      if (this.spaces && this.spaces.length > 0 && this.selectedParkingLot && this.selectedParkingLot.id === parkingLot.id) {
        try {
          // 过滤出指定类型和状态为空闲的车位
          const availableSpaces = this.spaces.filter(space => space.type === spaceType && space.status === 0)
          const count = availableSpaces.length

          // 将结果存入缓存
          this.spaceCountCache[cacheKey] = count
          return count
        } catch (error) {
          console.error('计算可用车位数量失败:', error)
        }
      }

      // 如果没有缓存数据，使用默认值
      // 根据停车场大小估算不同类型的车位数量
      const totalSpaces = parkingLot.total_spaces || 0
      const occupiedSpaces = parkingLot.occupied_spaces || 0
      const availableSpaces = totalSpaces - occupiedSpaces

      if (availableSpaces <= 0) {
        this.spaceCountCache[cacheKey] = 0
        return 0
      }

      // 根据车位类型分配一定比例的可用车位
      let estimatedCount = 0
      switch (parseInt(spaceType, 10)) {
        case 0: // 普通车位，占总数的80%
          estimatedCount = Math.ceil(availableSpaces * 0.8)
          break
        case 1: // 残疾人车位，占总数的10%
          estimatedCount = Math.ceil(availableSpaces * 0.1)
          break
        case 2: // 大型车位，占总数的10%
          estimatedCount = Math.ceil(availableSpaces * 0.1)
          break
        default:
          estimatedCount = 0
      }

      // 将结果存入缓存
      this.spaceCountCache[cacheKey] = estimatedCount

      // 在后台异步获取真实数据，但不阻塞界面渲染
      this.fetchSpaceCountInBackground(parkingLot.id, spaceType, cacheKey)

      return estimatedCount
    },

    // 在后台异步获取车位数量
    fetchSpaceCountInBackground(parkingLotId, spaceType, cacheKey) {
      // 使用节流限制请求频率
      setTimeout(() => {
        getParkingSpaces(parkingLotId, { type: spaceType, status: 0 })
          .then(response => {
            if (response && response.data) {
              try {
                let spaces = []
                if (Array.isArray(response.data)) {
                  spaces = response.data
                } else if (response.data.items && Array.isArray(response.data.items)) {
                  spaces = response.data.items
                } else if (response.data.spaces && Array.isArray(response.data.spaces)) {
                  spaces = response.data.spaces
                } else {
                  // 如果数据不是数组格式，不进行处理
                  return
                }

                // 过滤出空闲的车位
                const availableSpaces = spaces.filter(space => space.status === 0)
                const count = availableSpaces.length

                // 更新缓存
                this.spaceCountCache[cacheKey] = count

                // 强制更新视图，但不频繁调用
                this.$nextTick(() => {
                  this.$forceUpdate()
                })
              } catch (error) {
                console.error('处理车位数据失败:', error)
              }
            }
          })
          .catch(error => {
            console.error(`获取停车场 ${parkingLotId} 的车位数据失败:`, error)
          })
      }, parkingLotId * 200) // 根据停车场ID错开请求时间，避免并发请求
    },



    // 处理停车命令
    handleParkingCommand(command) {
      const selectedParkingLot = this.selectedParkingLot
      if (!selectedParkingLot) return

      let spaceType = null

      switch (command) {
        case 'normalParking':
          // 普通停车，使用普通车位
          spaceType = 1 // 1 = 普通车位
          break
        case 'disabledParking':
          // 残疾人停车，使用残疾人车位
          spaceType = 2 // 2 = 残疾人车位
          break
        case 'chargingParking':
          // 充电停车，使用充电车位
          spaceType = 3 // 3 = 充电车位
          break
        default:
          return
      }

      // 检查该类型车位是否有可用的
      const availableCount = this.getAvailableSpaceCount(selectedParkingLot, spaceType)
      if (availableCount <= 0) {
        const typeText = this.getSpaceTypeText(spaceType)
        this.$message.warning(`该停车场没有可用的${typeText}`)
        return
      }

      // 如果是残疾人车位，检查权限
      if (spaceType === 2) {
        const roles = this.roles || []
        const isAdmin = roles.includes('admin')
        const isSecurity = roles.includes('security')

        if (!isAdmin && !isSecurity) {
          this.$message.warning('只有保安和管理员可以使用残疾人车位')
          return
        }
      }

      // 设置停车参数
      this.selectedParkingLot = selectedParkingLot
      this.startParkingParams = {
        parkingLotId: selectedParkingLot.id,
        parkingLotName: selectedParkingLot.name,
        spaceType: spaceType.toString() // 使用选择的车位类型
      }

      // 打开快速停车对话框
      this.showQuickParkingDialog = true
    },



    // 初始化图表
    initCharts() {
      console.log('开始初始化图表')
      // 首先清除现有图表
      this.disposeCharts()

      // 确保 DOM 元素已经渲染
      this.$nextTick(() => {
        // 检查 DOM 元素是否存在
        if (this.$refs.parkingCountChart) {
          console.log('DOM 元素存在，初始化停车次数图表')
          this.initParkingCountChart()
        } else {
          console.warn('parkingCountChart DOM 元素不存在')
        }

        if (this.$refs.parkingDurationChart) {
          console.log('DOM 元素存在，初始化停车时长图表')
          this.initParkingDurationChart()
        } else {
          console.warn('parkingDurationChart DOM 元素不存在')
        }

        if (this.$refs.frequentParkingLotsChart) {
          console.log('DOM 元素存在，初始化常用停车场图表')
          this.initFrequentParkingLotsChart()
        } else {
          console.warn('frequentParkingLotsChart DOM 元素不存在')
        }

        if (this.$refs.spaceTypesChart) {
          console.log('DOM 元素存在，初始化车位类型图表')
          this.initSpaceTypesChart()
        } else {
          console.warn('spaceTypesChart DOM 元素不存在')
        }

        // 添加窗口调整事件
        window.addEventListener('resize', this.resizeCharts)
      })
    },

    // 设置WebSocket监听器
    setupWebSocketListeners() {
      // 确保WebSocket连接
      socketService.connect()

      // 监听停车记录结束事件
      socketService.on('parking_record_ended', this.handleParkingRecordEnded)

      // 监听车位状态更新事件
      socketService.on('parking_space_updated', this.handleParkingSpaceUpdated)

      console.log('WebSocket监听器设置完成')
    },

    // 移除WebSocket监听器
    removeWebSocketListeners() {
      // 移除停车记录结束事件监听器
      socketService.off('parking_record_ended', this.handleParkingRecordEnded)

      // 移除车位状态更新事件监听器
      socketService.off('parking_space_updated', this.handleParkingSpaceUpdated)

      console.log('WebSocket监听器已移除')
    },

    // 处理停车记录结束事件
    handleParkingRecordEnded(data) {
      console.log('收到停车记录结束事件:', data)

      if (data && data.record && data.record.id) {
        // 从活动记录列表中移除该记录
        if (this.activeRecords && this.activeRecords.length > 0) {
          this.activeRecords = this.activeRecords.filter(record => record.id !== data.record.id)
        }

        // 刷新停车记录
        this.fetchRecords()

        // 如果在停车场页面，也刷新停车场列表
        if (this.activeTab === 'parkingLots') {
          this.fetchData()
        }

        // 如果有车位可视化组件，刷新车位状态
        if (this.selectedParkingLot && this.selectedParkingLot.id === data.record.parking_lot_id) {
          this.refreshParkingSpaces()
        }

        // 显示通知
        this.$notify({
          title: '停车已结束',
          message: `车辆 ${data.record.vehicle ? data.record.vehicle.number || data.record.vehicle.b_num : data.record.vehicle_id} 的停车已结束`,
          type: 'success',
          duration: 3000
        })
      }
    },

    // 处理车位状态更新事件
    handleParkingSpaceUpdated(data) {
      console.log('收到车位状态更新事件:', data)

      // 如果当前选中的停车场与更新的车位所属停车场相同，则刷新车位列表
      if (this.selectedParkingLot && this.selectedParkingLot.id === data.parking_lot_id) {
        // 在本地更新车位状态，避免全量刷新
        const index = this.spaces.findIndex(space => space.id === data.parking_space_id)
        if (index !== -1) {
          // 更新车位状态
          this.spaces[index].status = data.status
          this.spaces[index].current_vehicle_id = data.vehicle_id

          // 强制视图更新
          this.spaces = [...this.spaces]

          console.log(`车位 ${this.spaces[index].space_number} 状态已更新为 ${data.status}`)
        } else {
          // 如果找不到车位，则刷新整个列表
          this.refreshParkingSpaces()
        }
      }

      // 刷新停车场列表，更新占用数量
      this.fetchData()

      // 清除车位数量缓存，确保下次获取时重新计算
      this.clearSpaceCountCache(data.parking_lot_id)
    },

    // 清除车位数量缓存
    clearSpaceCountCache(parkingLotId) {
      console.log(`清除停车场 ${parkingLotId} 的车位数量缓存`)

      // 清除指定停车场的所有车位类型缓存
      if (parkingLotId) {
        // 遍历缓存对象，删除与指定停车场相关的缓存
        Object.keys(this.spaceCountCache).forEach(key => {
          if (key.startsWith(`${parkingLotId}_`)) {
            delete this.spaceCountCache[key]
            console.log(`删除缓存键: ${key}`)
          }
        })
      } else {
        // 如果没有指定停车场ID，清除所有缓存
        this.spaceCountCache = {}
        console.log('清除所有车位数量缓存')
      }

      // 强制刷新UI，确保下拉菜单中的车位数量能够及时更新
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },

    // 初始化停车次数图表
    initParkingCountChart() {
      if (!this.$refs.parkingCountChart) return

      this.parkingCountChart = echarts.init(this.$refs.parkingCountChart)

      // 获取当前用户的停车记录统计数据
      const days = parseInt(this.statsTimeRange)
      const dates = []
      const counts = []

      // 默认值已设置，不需要额外的参数

      // 默认值，如果无法获取数据时使用
      for (let i = days - 1; i >= 0; i--) {
        const date = dayjs().subtract(i, 'day').format('MM-DD')
        dates.push(date)
        counts.push(0) // 默认值
      }

      // 使用停车记录统计API获取数据
      console.log('调用停车统计API，用户ID:', this.userId);
      getParkingStats({
        date_range: 'week',
        status: 1, // 只分析已结束的停车记录
        scope: 'user', // 只分析当前用户的记录
        user_id: this.userId, // 指定用户ID
        _t: new Date().getTime() // 添加时间戳防止缓存
      }).then(response => {
        console.log('停车统计API响应:', response);
        console.log('停车统计API返回数据:', JSON.stringify(response.data));

        // 检查数据是否为空
        if (!response.data || !response.data.daily_stats || response.data.daily_stats.length === 0) {
          console.warn('停车统计API返回的数据为空');
        }
        if (response && response.data) {
          // 如果有每日统计数据，使用实际数据
          if (response.data.daily_stats && response.data.daily_stats.length > 0) {
            console.log('每日统计数据:', response.data.daily_stats);

            // 清空默认数据
            dates.length = 0
            counts.length = 0

            // 使用实际数据
            response.data.daily_stats.forEach(item => {
              let dateStr = '';
              if (typeof item.date === 'string') {
                dateStr = dayjs(item.date).format('MM-DD');
              } else if (item.date) {
                dateStr = item.date;
              } else {
                dateStr = '未知日期';
              }

              dates.push(dateStr);
              counts.push(item.count || 0);
            })
          } else {
            console.warn('没有每日统计数据');
            // 使用空数据
            dates.length = 0
            counts.length = 0

            // 添加一个空数据点，显示"暂无数据"
            dates.push('暂无数据');
            counts.push(0);
          }
        }

        // 设置图表选项
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: dates,
            axisTick: {
              alignWithLabel: true
            }
          },
          yAxis: {
            type: 'value',
            minInterval: 1
          },
          series: [{
            name: '停车次数',
            type: 'bar',
            barWidth: '60%',
            data: counts,
            itemStyle: {
              color: '#409EFF'
            }
          }]
        }

        // 设置图表
        this.parkingCountChart.setOption(option)
      }).catch(error => {
        console.error('获取停车统计数据失败:', error)

        // 出错时使用默认选项
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: dates,
            axisTick: {
              alignWithLabel: true
            }
          },
          yAxis: {
            type: 'value',
            minInterval: 1
          },
          series: [{
            name: '停车次数',
            type: 'bar',
            barWidth: '60%',
            data: counts,
            itemStyle: {
              color: '#409EFF'
            }
          }]
        }

        // 设置图表
        this.parkingCountChart.setOption(option)
      })

      // 图表选项将在API调用后设置
    },

    // 初始化停车时长分布图表
    initParkingDurationChart() {
      if (!this.$refs.parkingDurationChart) return

      this.parkingDurationChart = echarts.init(this.$refs.parkingDurationChart)

      // 使用停车记录统计API获取数据
      getParkingStats({
        date_range: 'month',
        status: 1, // 只分析已结束的停车记录
        scope: 'user', // 只分析当前用户的记录
        user_id: this.userId, // 指定用户ID
        _t: new Date().getTime() // 添加时间戳防止缓存
      }).then(response => {
        console.log('停车时长统计API响应:', response);
        console.log('停车时长统计API返回数据:', JSON.stringify(response.data));

        // 检查数据是否为空
        if (!response.data || !response.data.duration_stats) {
          console.warn('停车时长统计API返回的数据为空');
        }
        // 初始化空数据
        let chartData = []
        if (response && response.data && response.data.duration_stats) {
          console.log('停车时长分布数据:', response.data.duration_stats);

          // 检查数据是否有效
          if (Array.isArray(response.data.duration_stats) && response.data.duration_stats.length > 0) {
            // 确保数据格式正确
            chartData = response.data.duration_stats.map(item => ({
              value: item.value || 0,
              name: item.name || '未知'
            }));
          } else {
            console.warn('停车时长分布数据无效，使用默认数据');
          }
        } else {
          console.warn('没有停车时长分布数据，使用默认数据');
        }

        const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['<1小时', '1-3小时', '3-6小时', '6-12小时', '12-24小时', '1天以上']
        },
        series: [
          {
            name: '停车时长',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }

      this.parkingDurationChart.setOption(option)
      }).catch(error => {
        console.error('获取停车时长统计数据失败:', error)

        // 出错时使用默认选项
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            data: ['<1小时', '1-3小时', '3-6小时', '6-12小时', '12-24小时', '1天以上']
          },
          series: [
            {
              name: '停车时长',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '18',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: defaultData
            }
          ]
        }

        this.parkingDurationChart.setOption(option)
      })
    },

    // 初始化常用停车场图表
    initFrequentParkingLotsChart() {
      if (!this.$refs.frequentParkingLotsChart) return

      this.frequentParkingLotsChart = echarts.init(this.$refs.frequentParkingLotsChart)

      // 使用停车记录统计API获取数据
      getParkingStats({
        date_range: 'month',
        status: 1, // 只分析已结束的停车记录
        scope: 'user', // 只分析当前用户的记录
        user_id: this.userId, // 指定用户ID
        _t: new Date().getTime() // 添加时间戳防止缓存
      }).then(response => {
        console.log('常用停车场统计API响应:', response);
        console.log('常用停车场统计API返回数据:', JSON.stringify(response.data));

        // 检查数据是否为空
        if (!response.data || !response.data.parking_lots) {
          console.warn('常用停车场统计API返回的数据为空');
        }
        // 初始化空数据
        let chartData = []
        if (response && response.data && response.data.parking_lots) {
          console.log('停车场数据:', response.data.parking_lots);

          // 检查数据是否有效
          if (Array.isArray(response.data.parking_lots) && response.data.parking_lots.length > 0) {
            // 将停车场数据转换为图表所需格式
            chartData = response.data.parking_lots.map(lot => ({
              value: lot.total_records || 0,
              name: lot.name || '未命名停车场'
            }));

            // 只显示有记录的停车场
            chartData = chartData.filter(item => item.value > 0);

            // 如果没有数据，显示提示信息
            if (chartData.length === 0) {
              console.warn('没有有效的停车场数据');
              chartData = [{ value: 0, name: '暂无数据' }];
            }
          } else {
            console.warn('停车场数据无效');
            chartData = [{ value: 0, name: '暂无数据' }];
          }
        } else {
          console.warn('没有停车场数据');
          chartData = [{ value: 0, name: '暂无数据' }];
        }

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} 次'
          },
          series: [
            {
              name: '常用停车场',
              type: 'pie',
              radius: '70%',
              center: ['50%', '50%'],
              data: chartData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }

        this.frequentParkingLotsChart.setOption(option)
      }).catch(error => {
        console.error('获取停车场统计数据失败:', error)

        // 出错时使用默认选项
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} 次'
          },
          series: [
            {
              name: '常用停车场',
              type: 'pie',
              radius: '70%',
              center: ['50%', '50%'],
              data: [
                { value: 12, name: '西区停车场' },
                { value: 8, name: '东区停车场' },
                { value: 6, name: '南区停车场' },
                { value: 4, name: '北区停车场' },
                { value: 2, name: '其他停车场' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }

        this.frequentParkingLotsChart.setOption(option)
      })
    },

    // 初始化车位类型图表
    initSpaceTypesChart() {
      if (!this.$refs.spaceTypesChart) return

      this.spaceTypesChart = echarts.init(this.$refs.spaceTypesChart)

      // 使用停车记录统计API获取数据
      getParkingStats({
        date_range: 'month',
        status: 1, // 只分析已结束的停车记录
        scope: 'user', // 只分析当前用户的记录
        user_id: this.userId, // 指定用户ID
        _t: new Date().getTime() // 添加时间戳防止缓存
      }).then(response => {
        console.log('车位类型统计API响应:', response);
        console.log('车位类型统计API返回数据:', JSON.stringify(response.data));

        // 检查数据是否为空
        if (!response.data || (!response.data.space_types && !response.data.vehicle_types)) {
          console.warn('车位类型统计API返回的数据为空');
        }
        // 默认数据
        const defaultData = [
          { value: 30, name: '普通车位' },
          { value: 10, name: '残疾人专用车位' },
          { value: 15, name: '大型车位' }
        ]

        // 如果有数据，尝试使用实际数据
        let chartData = defaultData
        if (response && response.data) {
          if (response.data.space_types) {
            console.log('车位类型数据:', response.data.space_types);

            // 检查数据是否有效
            if (Array.isArray(response.data.space_types) && response.data.space_types.length > 0) {
              // 确保数据格式正确
              chartData = response.data.space_types.map(item => ({
                value: item.value || 0,
                name: item.name || '未知类型'
              }));

              // 只显示有记录的类型
              chartData = chartData.filter(item => item.value > 0);

              // 如果没有数据，使用默认数据
              if (chartData.length === 0) {
                console.warn('没有有效的车位类型数据，使用默认数据');
                chartData = defaultData;
              }
            } else {
              console.warn('车位类型数据无效，使用默认数据');
            }
          } else if (response.data.vehicle_types) {
            console.log('车辆类型数据:', response.data.vehicle_types);

            // 如果没有车位类型数据，尝试使用车辆类型数据
            if (Array.isArray(response.data.vehicle_types) && response.data.vehicle_types.length > 0) {
              chartData = response.data.vehicle_types.map(item => ({
                value: item.count || 0,
                name: item.type || '未知类型'
              }));

              // 只显示有记录的类型
              chartData = chartData.filter(item => item.value > 0);

              // 如果没有数据，使用默认数据
              if (chartData.length === 0) {
                console.warn('没有有效的车辆类型数据，使用默认数据');
                chartData = defaultData;
              }
            } else {
              console.warn('车辆类型数据无效，使用默认数据');
            }
          } else {
            console.warn('没有类型数据，使用默认数据');
          }
        } else {
          console.warn('没有数据，使用默认数据');
        }

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} 次 ({d}%)'
          },
          legend: {
            orient: 'horizontal',
            bottom: 10,
            data: chartData.map(item => item.name)
          },
          series: [
            {
              name: '车位类型',
              type: 'pie',
              radius: ['30%', '60%'],
              center: ['50%', '40%'],
              roseType: 'radius',
              data: chartData,
              label: {
                formatter: '{b}: {c} 次'
              },
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }

        this.spaceTypesChart.setOption(option)
      }).catch(error => {
        console.error('获取车位类型统计数据失败:', error)

        // 出错时使用默认选项
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} 次 ({d}%)'
          },
          legend: {
            orient: 'horizontal',
            bottom: 10,
            data: ['普通车位', '残疾人专用车位', '大型车位']
          },
          series: [
            {
              name: '车位类型',
              type: 'pie',
              radius: ['30%', '60%'],
              center: ['50%', '40%'],
              roseType: 'radius',
              data: [
                { value: 30, name: '普通车位' },
                { value: 10, name: '残疾人专用车位' },
                { value: 15, name: '大型车位' }
              ],
              label: {
                formatter: '{b}: {c} 次'
              },
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }

        this.spaceTypesChart.setOption(option)
      })
    },

    // 调整图表大小
    resizeCharts() {
      if (this.parkingCountChart) this.parkingCountChart.resize()
      if (this.parkingDurationChart) this.parkingDurationChart.resize()
      if (this.frequentParkingLotsChart) this.frequentParkingLotsChart.resize()
      if (this.spaceTypesChart) this.spaceTypesChart.resize()
    },

    // 销毁图表
    disposeCharts() {
      if (this.parkingCountChart) {
        this.parkingCountChart.dispose()
        this.parkingCountChart = null
      }

      if (this.parkingDurationChart) {
        this.parkingDurationChart.dispose()
        this.parkingDurationChart = null
      }

      if (this.frequentParkingLotsChart) {
        this.frequentParkingLotsChart.dispose()
        this.frequentParkingLotsChart = null
      }

      if (this.spaceTypesChart) {
        this.spaceTypesChart.dispose()
        this.spaceTypesChart = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

// 停车中心容器
.app-container {
  background-color: $pageBgColor;
  min-height: calc(100vh - 84px);

  // 停车场列表容器
  .parking-lots-container {
    margin-bottom: 20px;
  }

  // 空数据提示
  .el-empty {
    padding: 40px 0;
    background-color: $cardBgColor;
    border-radius: 8px;
    box-shadow: $boxShadow;
  }
}

// 通知卡片样式
.notice-card {
  margin-bottom: 20px;
  background: linear-gradient(135deg, rgba($primaryColor, 0.05), rgba($secondaryColor, 0.05));
  border-left: 4px solid $warningColor;

  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 16px;
  }

  .notice-content {
    .el-carousel__item {
      line-height: 36px;
      color: $regularTextColor;
    }
  }
}

// 主标签页样式
.main-tabs {
  margin-bottom: 20px;

  ::v-deep .el-tabs__header {
    margin-bottom: 0;
  }

  ::v-deep .el-tabs__item {
    height: 50px;
    line-height: 50px;
    font-size: 15px;
    transition: all 0.3s;

    &.is-active {
      color: $primaryColor;
      font-weight: 600;
    }

    i {
      margin-right: 5px;
      font-size: 18px;
      vertical-align: middle;
    }
  }

  ::v-deep .el-tabs__active-bar {
    background-color: $primaryColor;
    height: 3px;
  }
}

// 筛选器样式
.filter-container {
  margin-bottom: 20px;

  .filter-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
  }

  .filter-content {
    .el-row {
      margin-bottom: 0;

      .el-col {
        margin-bottom: 15px;
      }
    }
  }

  .filter-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-start;

    @media (min-width: 768px) {
      justify-content: flex-end;
    }
  }
}

// 停车场卡片样式
.parking-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: $boxShadow;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  background-color: $cardBgColor;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-8px);
    box-shadow: $hoverShadow;

    &::before {
      opacity: 1;
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, $primaryColor, $secondaryColor);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // 空间计数样式
  .space-count {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    font-size: 13px;
    color: $secondaryTextColor;
    background-color: rgba($primaryColor, 0.05);
    padding: 10px 15px;
    border-radius: 8px;

    .space-count-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .label {
        font-size: 12px;
        margin-bottom: 5px;
      }

      .value {
        font-size: 16px;
        font-weight: 600;
        color: $primaryTextColor;
      }
    }
  }
}

// 统计卡片样式
.stats-overview {
  margin-bottom: 20px;
}

// 图表容器样式
.chart-container {
  height: 300px;
  width: 100%;
}

// 顶部标题和操作区
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  position: relative;
  padding-bottom: 15px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, rgba(64, 158, 255, 0.5), rgba(64, 158, 255, 0.1), transparent);
  }

  .dashboard-title {
    h2 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      position: relative;
      display: inline-block;

      &::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 40px;
        height: 3px;
        background-color: #409EFF;
        border-radius: 3px;
        transition: width 0.3s ease;
      }

      &:hover::after {
        width: 100%;
      }
    }

    .subtitle {
      margin: 0;
      font-size: 14px;
      color: #909399;
      max-width: 500px;
      line-height: 1.5;
    }
  }

  .dashboard-actions {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: rgba(64, 158, 255, 0.1);
    }

    .el-tabs__item {
      transition: all 0.3s;
      padding: 0 20px;
      height: 40px;
      line-height: 40px;

      &:hover {
        color: #409EFF;
      }

      &.is-active {
        font-weight: 600;
      }

      i {
        margin-right: 5px;
        font-size: 16px;
        vertical-align: middle;
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;

    .dashboard-title {
      margin-bottom: 15px;
    }

    .dashboard-actions {
      width: 100%;
    }
  }
}

// 过滤器容器
.filter-container {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;

    .el-input, .el-select {
      width: 100% !important;
      margin-right: 0 !important;
    }
  }
}

// 校园通知栏
.campus-notice {
  display: flex;
  align-items: center;
  background-color: #f0f9eb;
  padding: 8px 15px;
  border-radius: 6px;
  margin-top: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #67C23A;

  i {
    font-size: 18px;
    color: #67C23A;
    margin-right: 10px;
  }

  .notice-content {
    flex: 1;
    overflow: hidden;

    .el-carousel {
      color: #606266;
      font-size: 14px;
    }
  }
}

// 停车场卡片容器
.card-col {
  margin-bottom: 20px;
}

// 停车场卡片
.parking-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);

  // 校园信息
  .campus-info {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }

  // 充电档信息
  .charging-info {
    margin-top: 10px;
    display: flex;
    align-items: center;
    background-color: #f0f9eb;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    color: #67C23A;
    gap: 10px;

    i {
      font-size: 16px;
    }

    b {
      font-weight: 600;
    }
  }

  // 校园特色信息区域
  .campus-info-section {
    margin-top: 15px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 6px;
    font-size: 13px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px dashed #ebeef5;
      display: flex;
      align-items: center;

      i {
        margin-right: 5px;
        color: #409eff;
      }
    }

    .campus-info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      color: #606266;
      background-color: #ffffff;
      padding: 8px 10px;
      border-radius: 4px;
      transition: all 0.3s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        background-color: #f0f7ff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      i {
        width: 16px;
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #409EFF, #67C23A);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);

    &::before {
      opacity: 1;
    }
  }

  .card-header {
    padding: 18px 20px;
    border-bottom: 1px solid #f0f2f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    .title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      transition: color 0.3s;
    }

    .status-tag {
      padding: 4px 10px;
      border-radius: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      font-weight: 500;
      transition: all 0.3s ease;

      &.operating {
        background-color: rgba(103, 194, 58, 0.15);
        color: #67c23a;

        &:hover {
          background-color: rgba(103, 194, 58, 0.25);
        }
      }

      &.paused {
        background-color: rgba(144, 147, 153, 0.15);
        color: #909399;

        &:hover {
          background-color: rgba(144, 147, 153, 0.25);
        }
      }

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
        position: relative;

        &.operating {
          background-color: #67c23a;
          box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
          animation: pulse 2s infinite;
        }

        &.paused {
          background-color: #909399;
        }
      }
    }
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(103, 194, 58, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
    }
  }

  .card-body {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .address {
      margin: 0 0 18px 0;
      color: #606266;
      font-size: 14px;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #f9f9f9;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f2f5;
      }

      i {
        margin-right: 8px;
        color: #409eff;
        font-size: 16px;
      }
    }

    .utilization-info {
      margin-top: 10px;
      flex: 1;
      display: flex;
      flex-direction: column;

      .utilization-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 15px;
        color: #606266;

        .utilization-value {
          font-weight: 600;
          font-size: 16px;
          transition: all 0.3s ease;
          padding: 2px 8px;
          border-radius: 4px;

          &.low {
            color: #67c23a;
            background-color: rgba(103, 194, 58, 0.1);
          }

          &.medium {
            color: #e6a23c;
            background-color: rgba(230, 162, 60, 0.1);
          }

          &.high {
            color: #f56c6c;
            background-color: rgba(245, 108, 108, 0.1);
          }
        }
      }

      .el-progress {
        margin: 5px 0;

        .el-progress-bar__outer {
          border-radius: 4px;
          background-color: #ebeef5;
        }

        .el-progress-bar__inner {
          border-radius: 4px;
          transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
      }

      .space-count {
        display: flex;
        justify-content: space-between;
        margin-top: 12px;
        font-size: 13px;
        color: #909399;
        background-color: #f9f9f9;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f0f2f5;
        }

        b {
          color: #303133;
          font-weight: 600;
          margin-left: 4px;
        }
      }
    }
  }

  .card-footer {
    padding: 15px 20px;
    border-top: 1px solid #f0f2f5;
    display: flex;
    justify-content: flex-end;
    background-color: #fafafa;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    .el-button {
      padding: 8px 16px;
      font-size: 13px;
      border-radius: 20px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .el-button + .el-button {
      margin-left: 10px;
    }
  }
}

// 分页容器
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

// 停车记录容器
.records-container {
  .section-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    margin-bottom: 15px;
    color: #303133;

    i {
      margin-right: 8px;
      color: #409EFF;
    }

    .count-badge {
      margin-left: 10px;
      background-color: #409EFF;
      color: white;
      border-radius: 10px;
      padding: 2px 8px;
      font-size: 12px;
    }
  }

  // 快速停车部分
  .quick-parking-section {
    margin-bottom: 30px;

    .empty-parking-lot {
      padding: 30px 0;
      background-color: #f9f9f9;
      border-radius: 8px;
      text-align: center;
    }
  }

  .active-records-section {
    margin-bottom: 30px;
  }

  .record-card {
    height: 100%;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.15);
    }

    &.active {
      border-left: 4px solid #E6A23C;
    }

    .record-header {
      padding: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;

      .vehicle-info {
        display: flex;
        align-items: center;
        font-weight: 600;

        i {
          margin-right: 5px;
          color: #409EFF;
        }
      }
    }

    .record-body {
      padding: 15px;

      .info-item {
        margin-bottom: 10px;
        display: flex;

        .label {
          color: #909399;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #606266;
          flex-grow: 1;
        }
      }
    }

    .record-footer {
      padding: 10px 15px;
      border-top: 1px solid #ebeef5;
      display: flex;
      justify-content: flex-end;
      background-color: #f9f9f9;

      .record-actions {
        display: flex;
        gap: 10px;

        .el-button {
          transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  .vehicle-info-cell {
    display: flex;
    align-items: center;

    i {
      margin-right: 5px;
      color: #409EFF;
    }
  }
}

// 充电服务容器
.charging-service-container {
  .charging-intro-card {
    margin-bottom: 20px;

    .charging-intro {
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
    }
  }

  .charging-card {
    height: 100%;

    .el-card__header {
      padding: 15px;
      font-weight: 600;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }

    .empty-reservations {
      padding: 20px 0;
    }
  }
}

// 统计图表容器
.statistics-container {
  .chart-card {
    margin-bottom: 20px;

    .el-card__header {
      padding: 15px;
      font-weight: 600;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }

    .chart-container {
      height: 300px;
      width: 100%;
    }
  }
}

// 结束停车对话框
.end-parking-dialog {
  .record-info {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;

    .info-item {
      margin-bottom: 10px;
      display: flex;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #909399;
        width: 80px;
        flex-shrink: 0;
      }

      .value {
        color: #606266;
        flex-grow: 1;
        font-weight: 600;
      }
    }
  }
}

// 记录详情对话框
.record-detail {
  .el-descriptions {
    margin-bottom: 20px;
  }
}

// 停车对话框
.parking-dialog-content {
  .dialog-header {
    margin-bottom: 20px;

    .dialog-title {
      display: flex;
      align-items: center;
      margin-bottom: 5px;

      i {
        font-size: 20px;
        color: #409EFF;
        margin-right: 8px;
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .dialog-subtitle {
      font-size: 14px;
      color: #909399;
      margin-left: 28px;
    }
  }

  .spaces-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
  }
}

// 停车场布局
.parking-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f9fafc;
  border-radius: 12px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="100" height="100" fill="none" stroke="%23e0e0e0" stroke-width="0.5" stroke-dasharray="5,5" /></svg>');
    opacity: 0.3;
    pointer-events: none;
    border-radius: 12px;
  }

  .parking-space {
    position: relative;
    width: 110px;
    height: 160px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 15px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 100%);
      z-index: -1;
    }

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border: 3px solid #409EFF;
      box-shadow: 0 0 20px rgba(64, 158, 255, 0.5);
      transform: translateY(-10px) scale(1.05);
      z-index: 10;

      &::after {
        content: '✔';
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: #409EFF;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.5);
        animation: pulse-blue 2s infinite;
      }
    }

    .space-number {
      font-weight: 600;
      font-size: 18px;
      text-align: center;
      padding: 6px 10px;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      margin-bottom: 10px;

      &:hover {
        background-color: white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .vehicle-info {
      background-color: rgba(0, 0, 0, 0.08);
      padding: 8px 10px;
      border-radius: 8px;
      font-size: 12px;
      text-align: center;
      word-break: break-all;
      transition: all 0.3s ease;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        background-color: rgba(0, 0, 0, 0.12);
      }
    }

    .maintenance-info {
      background-color: rgba(144, 147, 153, 0.15);
      padding: 8px 10px;
      border-radius: 8px;
      font-size: 13px;
      text-align: center;
      font-weight: bold;
      transition: all 0.3s ease;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        background-color: rgba(144, 147, 153, 0.25);
      }
    }

    .admin-actions {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 5;
      opacity: 0;
      transition: all 0.3s ease;
      transform: translateY(-5px);

      .el-dropdown-link {
        cursor: pointer;
        color: #606266;
        font-size: 16px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          color: #409EFF;
          background: white;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          transform: scale(1.1);
        }
      }
    }

    &:hover .admin-actions {
      opacity: 1;
      transform: translateY(0);
    }

    // 状态样式
    &.available {
      background-color: #f0f9eb;
      border: 2px solid #67C23A;
      color: #333;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #67C23A, #95d475);
      }
    }

    &.occupied {
      background-color: #fdf6ec;
      border: 2px solid #E6A23C;
      color: #333;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #E6A23C, #f3d19e);
      }
    }

    &.maintenance {
      background-color: #f4f4f5;
      border: 2px solid #909399;
      color: #333;
      background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0, 0, 0, 0.03) 10px, rgba(0, 0, 0, 0.03) 20px);

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #909399, #c8c9cc);
      }

      &::before {
        content: '维护中';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-30deg);
        font-size: 20px;
        font-weight: bold;
        color: rgba(144, 147, 153, 0.4);
        white-space: nowrap;
        z-index: 0;
        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
      }
    }

    // 类型样式
    &.type-1 {
      // 普通车位
      &::before {
        content: "🚕"; // 小型车图标
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 16px;
        color: #409EFF;
        background-color: rgba(255, 255, 255, 0.8);
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
        z-index: 2;
      }
    }

    &.type-2 {
      // 残疾人专用车位
      width: 130px; // 更宽的车位
      height: 180px; // 更高的车位
      background-color: #ecf5ff; // 浅蓝色背景
      border: 2px solid #0066CC; // 蓝色边框

      &::before {
        content: "♿"; // 轮椅图标
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 16px;
        color: #ffffff;
        background-color: #0066CC;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0, 102, 204, 0.3);
        z-index: 2;
      }

      // 添加标识条
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #0066CC, #409EFF);
      }

      .space-number {
        background-color: rgba(0, 102, 204, 0.1);
        color: #0066CC;
        font-weight: bold;
      }
    }

    &.type-3 {
      // 大型车位
      width: 130px; // 更宽的车位
      height: 180px; // 更高的车位
      background-color: #f0f9eb; // 浅绿色背景
      border: 2px solid #67C23A; // 绿色边框

      &::before {
        content: "🏍"; // 摩托车图标
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 16px;
        color: #ffffff;
        background-color: #67C23A;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
        z-index: 2;
      }

      // 添加标识条
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #67C23A, #95d475);
      }

      .space-number {
        background-color: rgba(103, 194, 58, 0.1);
        color: #67C23A;
        font-weight: bold;
      }
    }
  }
}

// 动画效果
@keyframes pulse-blue {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

// 卡片标题样式
.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 快速停车控制样式
.quick-parking-controls {
  display: flex;
  align-items: center;
}

// 空停车场提示样式
.empty-parking-lot {
  padding: 40px 0;
  text-align: center;

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;

    i {
      margin-bottom: 15px;
    }

    p {
      color: #909399;
      font-size: 14px;
      margin: 0;
    }
  }
}

// 停车类型项样式
.parking-type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 2px 0;

  .parking-type-info {
    display: flex;
    align-items: center;

    i {
      margin-right: 5px;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .parking-layout .parking-space {
    width: 90px;
    height: 130px;
    padding: 8px;
  }

  .spaces-filter {
    flex-direction: column;
    align-items: stretch;

    .el-input, .el-select {
      width: 100% !important;
      margin-right: 0 !important;
    }
  }

  // 移动端下拉菜单样式调整
  .parking-type-item {
    flex-direction: column;
    align-items: flex-start;

    .el-tag {
      margin-top: 5px;
      margin-left: 20px;
    }
  }
}
</style>
