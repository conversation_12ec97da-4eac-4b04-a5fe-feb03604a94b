import sqlite3
import os
from datetime import datetime, timedelta

# 获取数据库路径
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sys.db')
print(f"数据库路径: {db_path}")

# 连接到数据库
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 测试数据
test_announcements = [
    {
        'title': '系统维护通知',
        'content': '系统将于2024年3月1日凌晨2:00-4:00进行例行维护，期间系统将暂停服务。',
        'type': 'system',
        'created_by': 1,  # 假设ID为1的用户是管理员
        'created_at': (datetime.now() - timedelta(days=2)).isoformat()
    },
    {
        'title': '停车场空位情况',
        'content': '本周停车场空位情况：A区剩余20个车位，B区剩余15个车位，C区剩余10个车位。',
        'type': 'parking',
        'created_by': 1,
        'created_at': (datetime.now() - timedelta(days=1)).isoformat()
    },
    {
        'title': '违规停车警告',
        'content': '近期发现多起违规停车行为，请各位车主注意遵守停车规定，违者将按规定处理。',
        'type': 'violation',
        'created_by': 1,
        'created_at': datetime.now().isoformat()
    }
]

# 插入测试数据
for announcement in test_announcements:
    cursor.execute('''
    INSERT INTO announcements (title, content, type, created_by, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        announcement['title'],
        announcement['content'],
        announcement['type'],
        announcement['created_by'],
        announcement['created_at'],
        announcement['created_at']
    ))

# 提交更改
conn.commit()

# 关闭连接
conn.close()

print("测试公告数据添加成功") 