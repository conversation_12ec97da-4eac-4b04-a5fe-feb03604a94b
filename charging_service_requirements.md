# 2.2.4 充电服务功能需求分析

充电服务是校园电动车管理系统的核心功能模块之一，旨在为校园电动车用户提供便捷、高效的充电管理服务。该模块主要包括充电车位管理和充电记录管理两大部分，通过这些功能满足用户充电需求，同时帮助管理员有效管理校园充电资源。

## 1. 充电车位管理

充电车位管理是充电服务的基础，通过对充电车位的全面管理，确保充电资源的合理分配和高效利用。

### 1.1 车位信息维护（重点功能）

**功能描述**：
- 系统应支持管理员对充电车位基本信息进行全面管理，包括添加、编辑、删除和查询充电车位
- 每个充电车位应包含以下基本信息：
  * 车位编号：唯一标识符，格式为"C-停车场id-序号"（如"C-1-1"）
  * 所属停车场：关联到停车场表的外键，指明车位所在停车场
  * 充电功率：以千瓦(kW)为单位的充电功率，如3.3kW、7kW、11kW等
  * 状态：空闲(0)、占用(1)、维护中(2)三种状态
  * 当前车辆ID：当状态为占用时，记录当前使用车辆的ID
  * 备注：可选字段，记录车位的特殊情况或注意事项
- 系统应支持批量导入充电车位信息，通过Excel模板导入多个车位信息，提高管理效率
- 车位信息变更应记录详细的操作日志，包括操作人、操作时间、操作类型、变更前后的值等，便于追溯
- 系统应提供车位信息的导出功能，支持Excel和PDF两种格式

**操作流程**：
1. 管理员登录系统，进入充电车位管理页面
2. 可通过表单添加新充电车位，填写必要信息（车位编号、所属停车场、充电功率等）
3. 可选择已有充电车位进行编辑（修改状态、充电功率等）或删除操作
4. 批量导入时，先下载模板，填写后上传，系统验证数据有效性后导入
5. 所有操作完成后，系统更新车位信息并记录详细操作日志
6. 管理员可查看车位变更历史，了解车位信息的变更过程

**权限控制**：
- 仅管理员可进行车位信息的添加、编辑和删除操作
- 普通用户和保安可查看车位信息，但无修改权限
- 系统管理员可查看和导出操作日志

### 1.2 车位状态监控

**功能描述**：
- 系统实时显示所有充电车位的当前状态（空闲/占用/维护中）
- 对于占用状态的车位，显示当前占用车辆信息和开始充电时间d
- 支持车位状态的手动更新，如将车位标记为维护中
- 车位状态变更自动记录到系统日志

**权限控制**：
- 所有用户可查看车位状态
- 仅管理员和保安可手动更新车位状态

### 1.3 车位使用统计

**功能描述**：
- 系统自动统计充电车位使用情况，包括使用频率、平均充电时长等
- 提供可视化图表展示统计结果
- 支持统计数据导出功能

**权限控制**：
- 仅管理员可访问详细的使用统计数据
- 普通用户可查看基本的车位可用情况

## 2. 充电记录管理

充电记录管理负责记录和管理用户的充电活动，是充电服务的核心业务逻辑部分。

### 2.1 记录创建（重点功能）

**功能描述**：
- 系统支持用户发起充电请求，自动创建充电记录，是用户使用充电服务的核心入口
- 充电记录包含以下详细信息：
  * 记录ID：系统自动生成的唯一标识符
  * 关联的停车记录ID：外键，关联到对应的停车记录
  * 用户ID：充电操作的执行用户
  * 车辆ID：进行充电的电动车ID
  * 停车场ID：充电所在的停车场
  * 车位ID：充电所使用的具体车位
  * 开始时间：开始充电的时间戳
  * 结束时间：结束充电的时间戳（充电完成后更新）
  * 充电时长：以分钟为单位的充电持续时间（结束时自动计算）
  * 充电功率：以kW为单位的充电功率，从车位信息获取
  * 充电状态：进行中(0)、已完成(1)、异常(2)
  * 备注：用户或系统添加的备注信息
- 创建充电记录时，系统执行以下验证和处理：
  1. 验证用户身份和权限，确保用户有权为该车辆创建充电记录
  2. 验证车辆信息，确保车辆存在且为电动车类型
  3. 验证车位状态，确保选择的是充电类型车位且当前为空闲状态
  4. 检查用户是否有未完成的充电记录，避免一辆车同时有多条进行中的充电记录
  5. 创建事务，确保充电记录创建和车位状态更新的原子性
- 充电开始后，系统自动执行以下操作：
  1. 更新相关车位状态为"占用"，并记录当前车辆ID
  2. 更新车位使用统计数据
  3. 向用户发送充电开始的确认通知
  4. 记录操作日志

**操作流程**：
1. 用户在停车记录详情页或充电车位列表页选择"开始充电"
2. 系统显示充电确认对话框，包含以下信息：
   - 车辆信息（车牌号、品牌、类型）
   - 车位信息（车位编号、充电类型、充电功率）
   - 预估充电时间（可选）
   - 备注输入框
3. 用户确认信息无误后，点击"确认充电"按钮
4. 系统执行验证流程，创建充电记录
5. 更新车位状态，显示充电已开始的确认信息
6. 用户可在"我的充电"页面查看进行中的充电记录

**权限控制**：
- 所有注册用户可创建充电记录
- 用户只能为自己的车辆创建充电记录
- 系统自动验证用户与车辆的所有权关系

### 2.2 记录查询

**功能描述**：
- 系统支持用户查询充电记录，提供多种查询条件
- 查询结果以列表形式展示，支持分页和排序
- 提供记录详情查看功能

**查询条件**：
- 时间范围、车辆信息、停车场/车位、充电状态等

**权限控制**：
- 普通用户只能查询自己的充电记录
- 管理员可查询所有用户的充电记录
- 保安可查询其负责区域内的充电记录

## 3. 系统集成与交互

充电服务模块与系统其他模块紧密集成，与停车管理、用户管理和车辆管理模块有密切的交互关系。

### 3.1 与停车管理的集成

- 充电车位是特殊类型的停车位，共享车位基础信息
- 用户充电前需先完成停车操作，支持从停车记录直接发起充电请求
- 充电结束后，相关车位状态自动更新

### 3.2 与用户和车辆管理的集成

- 验证用户对车辆的所有权关系
- 用户个人中心展示充电记录和统计信息
- 车辆详情页显示充电历史记录

## 4. 非功能需求

### 4.1 性能需求

- 充电记录创建操作响应迅速
- 记录查询支持基本的分页和筛选
- 系统能处理日常使用场景下的并发请求

### 4.2 安全需求

- 充电操作进行基本的身份验证
- 充电记录保护用户隐私
- 关键操作记录基本日志

### 4.3 可用性需求

- 充电服务界面简洁直观
- 提供清晰的状态反馈
- 对异常情况提供基本的错误提示
