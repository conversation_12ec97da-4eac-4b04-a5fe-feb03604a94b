```mermaid
flowchart TD
    %% 系统主要组件
    User([用户]) --- |访问|UI[用户界面层]
    UI --- |请求/响应|API[API层]
    API --- |CRUD操作|BL[业务逻辑层]
    BL --- |数据访问|DB[(数据库)]
    
    %% 核心业务模块
    subgraph 核心业务模块
        UserModule[用户管理模块]
        VehicleModule[车辆管理模块]
        ParkingModule[停车中心模块]
        ChargingModule[充电中心模块]
        ViolationModule[违规中心模块]
        AnnouncementModule[公告管理模块]
    end
    
    %% 模块与业务逻辑层的连接
    BL --- UserModule
    BL --- VehicleModule
    BL --- ParkingModule
    BL --- ChargingModule
    BL --- ViolationModule
    BL --- AnnouncementModule
    
    %% 主要数据流向
    subgraph 用户认证流程
        A1[用户输入凭证] --> A2[前端发送登录请求]
        A2 --> A3[后端验证凭证]
        A3 --> A4[生成JWT令牌]
        A4 --> A5[返回令牌给前端]
        A5 --> A6[前端存储令牌]
        A6 --> A7[后续请求携带令牌]
    end
    
    subgraph 车辆注册流程
        B1[用户填写车辆信息] --> B2[前端验证表单]
        B2 --> B3[发送注册请求]
        B3 --> B4[后端验证数据]
        B4 --> B5[检查车牌唯一性]
        B5 --> B6[创建车辆记录]
        B6 --> B7[关联用户与车辆]
        B7 --> B8[返回成功响应]
    end
    
    subgraph 停车流程
        C1[用户浏览停车场] --> C2[查询可用车位]
        C2 --> C3[选择车辆和车位]
        C3 --> C4[提交停车请求]
        C4 --> C5[验证车位可用性]
        C5 --> C6[创建停车记录]
        C6 --> C7[更新车位状态]
        C7 --> C8[返回停车凭证]
    end
    
    subgraph 充电流程
        D1[查看停车记录] --> D2[选择开始充电]
        D2 --> D3[验证车位类型]
        D3 --> D4[创建充电记录]
        D4 --> D5[更新充电状态]
        D5 --> D6[返回充电开始确认]
        D6 --> D7[用户结束充电]
        D7 --> D8[计算充电时长]
        D8 --> D9[更新充电记录]
    end
    
    subgraph 违规处理流程
        E1[保安记录违规] --> E2[关联车辆和用户]
        E2 --> E3[保存违规记录]
        E3 --> E4[用户查看违规]
        E4 --> E5[用户提交申诉]
        E5 --> E6[管理员处理申诉]
        E6 --> E7[更新违规状态]
        E7 --> E8[通知用户结果]
    end
    
    %% 数据流向连接
    UserModule --- A3
    UserModule --- A4
    VehicleModule --- B4
    VehicleModule --- B5
    VehicleModule --- B6
    VehicleModule --- B7
    ParkingModule --- C2
    ParkingModule --- C5
    ParkingModule --- C6
    ParkingModule --- C7
    ChargingModule --- D3
    ChargingModule --- D4
    ChargingModule --- D5
    ChargingModule --- D8
    ChargingModule --- D9
    ViolationModule --- E2
    ViolationModule --- E3
    ViolationModule --- E5
    ViolationModule --- E6
    ViolationModule --- E7
    
    %% 样式定义
    classDef process fill:#f9f9f9,stroke:#333,stroke-width:1px
    classDef module fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef layer fill:#e6e6fa,stroke:#333,stroke-width:1px
    classDef database fill:#f5f5f5,stroke:#333,stroke-width:1px
    
    class A1,A2,A3,A4,A5,A6,A7,B1,B2,B3,B4,B5,B6,B7,B8,C1,C2,C3,C4,C5,C6,C7,C8,D1,D2,D3,D4,D5,D6,D7,D8,D9,E1,E2,E3,E4,E5,E6,E7,E8 process
    class UserModule,VehicleModule,ParkingModule,ChargingModule,ViolationModule,AnnouncementModule module
    class UI,API,BL layer
    class DB database
```
