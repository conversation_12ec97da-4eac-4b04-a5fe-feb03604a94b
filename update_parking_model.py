from app import db, app
from flask_migrate import Migrate, MigrateCommand
from flask_script import Manager
from app.parkinglots.models import ParkingLot, ParkingSpace

# 添加新字段到ParkingLot模型
with app.app_context():
    # 添加新字段
    db.session.execute('ALTER TABLE parking_lots ADD COLUMN campus TEXT')
    db.session.execute('ALTER TABLE parking_lots ADD COLUMN area TEXT')
    db.session.execute('ALTER TABLE parking_lots ADD COLUMN manager TEXT')
    db.session.execute('ALTER TABLE parking_lots ADD COLUMN contact TEXT')
    
    # 提交更改
    db.session.commit()
    
    print("数据库结构更新成功！")
    
    # 更新现有数据
    parking_lots = ParkingLot.query.all()
    for lot in parking_lots:
        if lot.id == 1:
            lot.campus = '主校区'
            lot.area = '教学区'
            lot.manager = '张主管'
            lot.contact = '13800138001'
        elif lot.id == 2:
            lot.campus = '主校区'
            lot.area = '宿舍区'
            lot.manager = '李管理'
            lot.contact = '13800138002'
        elif lot.id == 3:
            lot.campus = '主校区'
            lot.area = '图书馆区'
            lot.manager = '王管理'
            lot.contact = '13800138003'
        elif lot.id == 4:
            lot.campus = '东校区'
            lot.area = '食堂区'
            lot.manager = '赵管理'
            lot.contact = '13800138004'
        elif lot.id == 5:
            lot.campus = '东校区'
            lot.area = '体育区'
            lot.manager = '孙管理'
            lot.contact = '13800138005'
    
    # 提交更改
    db.session.commit()
    
    print("停车场数据更新成功！")
