#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电车位管理和故障报修管理功能测试脚本
"""

import sqlite3
import requests
import json
import time
from datetime import datetime, timedelta

# API服务器地址
BASE_URL = "http://localhost:5000"
# 测试用户Token（需要在运行前填入有效的Token）
ADMIN_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NTkwNzIwNiwianRpIjoiMzYyNzEzNzgtMWIyMC00ZmNjLTkzNDAtYzViOTRhYWRhYTE5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoxLCJuYmYiOjE3NDU5MDcyMDYsImNzcmYiOiJmNzUzYTMyMy0xYWRlLTQxZDEtODczYy0zNjhjYmMzZTAxZmUiLCJleHAiOjE3NDU5OTM2MDYsInJvbGUiOiJhZG1pbiIsInN1YiI6IjEifQ.kR_jOzDr3NGCk463ecGcd5TKRsNSOe3HVSIIpuMu7mo"
USER_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NTkwNzMxNiwianRpIjoiM2Y4ZWU0YzUtOTM3Ni00MzUxLTk3ZDktODIxZmJjYzBhYmU5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoyLCJuYmYiOjE3NDU5MDczMTYsImNzcmYiOiI4ZTY4YWY3Yy0zNDM5LTRiZDEtYjcwNS1mODNkYWI4MjUxMzQiLCJleHAiOjE3NDU5OTM3MTYsInJvbGUiOiJ1c2VyIiwic3ViIjoiMiJ9.uMgV57bE0lpZQJdllWUQUGRKSE__Pw5hR5MreA1ki3I"

# 测试结果统计
total_tests = 0
passed_tests = 0

def print_separator():
    """打印分隔符"""
    print("=" * 80)

def print_test_result(test_name, result, message=None):
    """打印测试结果"""
    global total_tests, passed_tests
    total_tests += 1
    if result:
        passed_tests += 1
        print(f"✅ 测试通过: {test_name}")
    else:
        print(f"❌ 测试失败: {test_name}")
    if message:
        print(f"   {message}")

def get_headers(is_admin=True):
    """获取请求头"""
    return {
        "Authorization": ADMIN_TOKEN if is_admin else USER_TOKEN,
        "Content-Type": "application/json"
    }

def check_db_tables():
    """检查数据库中的表"""
    try:
        conn = sqlite3.connect('sys.db')
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
        tables = cursor.fetchall()
        print("数据库中的表:")
        for table in tables:
            print(f" - {table[0]}")
        conn.close()
        return True
    except Exception as e:
        print(f"检查数据库表失败: {str(e)}")
        return False

# 1. 充电车位管理测试
def test_charging_spaces():
    """测试充电车位管理功能"""
    print_separator()
    print("开始测试充电车位管理功能...")
    
    # 1.1 获取充电车位列表
    def test_get_charging_spaces():
        """测试获取充电车位列表"""
        response = requests.get(f"{BASE_URL}/api/charging-spaces", headers=get_headers())
        if response.status_code == 200:
            data = response.json()
            print(f"获取到 {len(data['data']['items'])} 个充电车位")
            return True, data['data']
        else:
            return False, f"获取充电车位列表失败: {response.status_code}, {response.text}"
    
    success, result = test_get_charging_spaces()
    print_test_result("获取充电车位列表", success, result if not success else None)
    
    # 保存现有车位数量用于后续验证
    orig_spaces_count = len(result['items']) if success else 0
    
    # 1.2 创建充电车位
    def test_create_charging_space():
        """测试创建充电车位"""
        new_space = {
            "parking_lot_id": 1,  # 假设ID为1的停车场存在
            "space_number": f"CT-{int(time.time())}",  # 使用时间戳确保车位编号唯一
            "type": 2,  # 充电车位
            "power": 15,
            "status": 0,  # 空闲
            "remarks": "测试创建的充电车位"
        }
        
        response = requests.post(f"{BASE_URL}/api/charging-spaces", headers=get_headers(), data=json.dumps(new_space))
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"创建充电车位成功: {data['data']['id']}")
            return True, data['data']
        else:
            return False, f"创建充电车位失败: {response.status_code}, {response.text}"
    
    success, created_space = test_create_charging_space()
    print_test_result("创建充电车位", success, created_space if not success else None)
    
    # 1.3 检查车位是否成功创建
    if success:
        success, result = test_get_charging_spaces()
        if success:
            new_count = len(result['items'])
            success = new_count > orig_spaces_count
            message = f"创建后车位数: {new_count}, 创建前车位数: {orig_spaces_count}" if not success else None
            print_test_result("验证充电车位数量增加", success, message)
    
    # 1.4 更新充电车位
    if success and isinstance(created_space, dict) and 'id' in created_space:
        def test_update_charging_space():
            """测试更新充电车位"""
            update_data = {
                "power": 20,
                "remarks": "已更新的测试充电车位"
            }
            
            response = requests.put(f"{BASE_URL}/api/charging-spaces/{created_space['id']}", headers=get_headers(), data=json.dumps(update_data))
            if response.status_code == 200:
                data = response.json()
                print(f"更新充电车位成功: {data['data']['id']}")
                return True, data['data']
            else:
                return False, f"更新充电车位失败: {response.status_code}, {response.text}"
        
        success, updated_space = test_update_charging_space()
        print_test_result("更新充电车位", success, updated_space if not success else None)
    
    # 1.5 删除充电车位
    if success and isinstance(created_space, dict) and 'id' in created_space:
        def test_delete_charging_space():
            """测试删除充电车位"""
            response = requests.delete(f"{BASE_URL}/api/charging-spaces/{created_space['id']}", headers=get_headers())
            if response.status_code == 200:
                data = response.json()
                print(f"删除充电车位成功")
                return True, None
            else:
                return False, f"删除充电车位失败: {response.status_code}, {response.text}"
        
        success, _ = test_delete_charging_space()
        print_test_result("删除充电车位", success)
    
    # 1.6 验证删除是否成功
    if success:
        success, result = test_get_charging_spaces()
        if success:
            new_count = len(result['items'])
            success = new_count == orig_spaces_count
            message = f"删除后车位数: {new_count}, 初始车位数: {orig_spaces_count}" if not success else None
            print_test_result("验证充电车位删除成功", success, message)

# 2. 故障报修管理测试
def test_charging_faults():
    """测试故障报修管理功能"""
    print_separator()
    print("开始测试故障报修管理功能...")
    
    # 2.1 获取故障报修列表
    def test_get_charging_faults():
        """测试获取故障报修列表"""
        response = requests.get(f"{BASE_URL}/api/charging-faults", headers=get_headers())
        if response.status_code == 200:
            data = response.json()
            print(f"获取到 {len(data['data']['items'])} 个故障报修")
            return True, data['data']
        else:
            return False, f"获取故障报修列表失败: {response.status_code}, {response.text}"
    
    success, result = test_get_charging_faults()
    print_test_result("获取故障报修列表", success, result if not success else None)
    
    # 保存现有故障数量用于后续验证
    orig_faults_count = len(result['items']) if success else 0
    
    # 2.2 创建故障报修
    def test_create_charging_fault():
        """测试创建故障报修"""
        new_fault = {
            "parking_lot_id": 1,  # 假设ID为1的停车场存在
            "space_id": 1,  # 假设ID为1的车位存在
            "fault_type": "connector",  # 充电接口故障
            "severity": "medium",  # 一般
            "reporter_name": "测试用户",
            "reporter_phone": "13800138000",
            "fault_description": "测试创建的故障报修：充电接口松动"
        }
        
        response = requests.post(f"{BASE_URL}/api/charging-faults", headers=get_headers(), data=json.dumps(new_fault))
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"创建故障报修成功: {data['data']['id']}")
            return True, data['data']
        else:
            return False, f"创建故障报修失败: {response.status_code}, {response.text}"
    
    success, created_fault = test_create_charging_fault()
    print_test_result("创建故障报修", success, created_fault if not success else None)
    
    # 2.3 检查故障是否成功创建
    if success:
        success, result = test_get_charging_faults()
        if success:
            new_count = len(result['items'])
            success = new_count > orig_faults_count
            message = f"创建后故障数: {new_count}, 创建前故障数: {orig_faults_count}" if not success else None
            print_test_result("验证故障报修数量增加", success, message)
    
    # 2.4 处理故障报修
    if success and isinstance(created_fault, dict) and 'id' in created_fault:
        def test_process_charging_fault():
            """测试处理故障报修"""
            process_data = {
                "handler_name": "测试管理员",
                "status": 1,  # 处理中
                "solution": "更换充电接口连接器",
                "estimated_completion_time": (datetime.now() + timedelta(hours=2)).isoformat(),
                "result": "",
                "space_status": 3  # 维修中
            }
            
            response = requests.put(f"{BASE_URL}/api/charging-faults/{created_fault['id']}/process", headers=get_headers(), data=json.dumps(process_data))
            if response.status_code == 200:
                data = response.json()
                print(f"故障开始处理成功")
                return True, data['data']
            else:
                return False, f"故障开始处理失败: {response.status_code}, {response.text}"
        
        success, processed_fault = test_process_charging_fault()
        print_test_result("故障开始处理", success, processed_fault if not success else None)
    
    # 2.5 完成故障处理
    if success and isinstance(created_fault, dict) and 'id' in created_fault:
        def test_complete_charging_fault():
            """测试完成故障处理"""
            complete_data = {
                "handler_name": "测试管理员",
                "status": 2,  # 已完成
                "solution": "更换了充电接口连接器",
                "result": "故障已解决，充电功能恢复正常",
                "completion_time": datetime.now().isoformat(),
                "space_status": 0  # 空闲
            }
            
            response = requests.put(f"{BASE_URL}/api/charging-faults/{created_fault['id']}/process", headers=get_headers(), data=json.dumps(complete_data))
            if response.status_code == 200:
                data = response.json()
                print(f"故障处理完成成功")
                return True, data['data']
            else:
                return False, f"故障处理完成失败: {response.status_code}, {response.text}"
        
        success, completed_fault = test_complete_charging_fault()
        print_test_result("故障处理完成", success, completed_fault if not success else None)
    
    # 2.6 尝试关闭故障
    if not success and isinstance(created_fault, dict) and 'id' in created_fault:
        def test_close_charging_fault():
            """测试关闭故障报修"""
            response = requests.put(f"{BASE_URL}/api/charging-faults/{created_fault['id']}/close", headers=get_headers())
            if response.status_code == 200:
                data = response.json()
                print(f"故障关闭成功")
                return True, data['data']
            else:
                return False, f"故障关闭失败: {response.status_code}, {response.text}"
        
        success, _ = test_close_charging_fault()
        print_test_result("关闭故障", success)

# 3. 进行总体测试
def run_all_tests():
    """运行所有测试"""
    print_separator()
    print("开始充电管理相关功能测试...")
    
    # 检查数据库表
    check_db_tables()
    
    # 测试充电车位管理
    test_charging_spaces()
    
    # 测试故障报修管理
    test_charging_faults()
    
    # 打印测试结果
    print_separator()
    print(f"测试总结: 通过 {passed_tests}/{total_tests} 测试")
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    print(f"成功率: {success_rate:.2f}%")

if __name__ == "__main__":
    run_all_tests() 