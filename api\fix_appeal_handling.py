from app import create_app, db
from app.violations.models import VehicleDisableRecord, ViolationRecord, Appeal
from app.bikes.models import Bikes
from app.tasks.check_vehicle_disable import disable_vehicle
from flask import current_app
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = create_app()

with app.app_context():
    # 修复申诉处理时禁用车辆的功能
    try:
        print("开始修复申诉处理时禁用车辆的功能...")
        
        # 1. 查找车牌号为111的车辆
        bike = Bikes.query.filter_by(b_num='111').first()
        if bike:
            print(f'找到车辆: ID={bike.b_id}, 车牌号={bike.b_num}, 状态={bike.status}, 所属用户={bike.belong_to}')
            
            # 2. 查找与该车辆相关的违规记录
            violations = ViolationRecord.query.filter_by(bike_id=bike.b_id).all()
            print(f'找到相关违规记录: {len(violations)}条')
            
            for violation in violations:
                print(f'违规记录ID: {violation.id}, 状态: {violation.status}, 车辆ID: {violation.bike_id}')
                
                # 3. 查找与违规记录相关的申诉
                appeals = Appeal.query.filter_by(violation_id=violation.id).all()
                print(f'  相关申诉: {len(appeals)}条')
                
                for appeal in appeals:
                    print(f'  申诉ID: {appeal.id}, 状态: {appeal.status}')
                    
                    # 如果申诉状态为2(未通过)，则禁用车辆
                    if appeal.status == 2:
                        print(f'  申诉未通过，禁用车辆 {bike.b_num}')
                        
                        # 禁用车辆
                        result = disable_vehicle(bike.b_id, violation.id)
                        if result:
                            print(f'  成功禁用车辆 {bike.b_num}')
                        else:
                            print(f'  禁用车辆 {bike.b_num} 失败')
        else:
            print('未找到车牌号为111的车辆')
            
    except Exception as e:
        print(f"修复申诉处理时禁用车辆的功能失败: {str(e)}")
        db.session.rollback()
