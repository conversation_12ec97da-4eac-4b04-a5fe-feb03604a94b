<template>
  <div class="app-container">
    <div class="dashboard-header">
      <div class="dashboard-title">
        <h2>已占用车位查询</h2>
        <p class="subtitle">查看所有已被占用的车位及其车辆信息</p>
      </div>
      <div class="dashboard-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="fetchAllData">刷新数据</el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="box-card">
      <div slot="header" class="clearfix">
        <span>已占用车位列表</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="exportToExcel">导出Excel</el-button>
      </div>

      <el-table
        :data="occupiedSpaces"
        border
        style="width: 100%"
        :default-sort="{prop: 'parkingLotName', order: 'ascending'}"
      >
        <el-table-column
          prop="parkingLotName"
          label="停车场"
          sortable
          width="180"
        />
        <el-table-column
          prop="spaceNumber"
          label="车位号"
          sortable
          width="120"
        />
        <el-table-column
          prop="spaceType"
          label="车位类型"
          width="120"
        >
          <template slot-scope="scope">
            <el-tag :type="getSpaceTypeTag(scope.row.spaceType)">{{ scope.row.spaceTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="vehicleInfo"
          label="车辆信息"
          min-width="200"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.vehicleInfo">
              <el-tag type="warning" effect="plain">{{ getVehicleLicensePlate(scope.row) }}</el-tag>
              <div class="vehicle-details">{{ getVehicleDetails(scope.row) }}</div>
            </div>
            <div v-else>
              <el-tag type="info">加载中...</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="occupiedSince"
          label="占用时间"
          sortable
          width="180"
        />
        <el-table-column
          label="操作"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="showSpaceDetails(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 车位详情对话框 -->
    <el-dialog
      :title="`${currentSpace ? currentSpace.spaceNumber : ''} 车位详情`"
      :visible.sync="spaceDialogVisible"
      width="450px"
      custom-class="space-dialog"
    >
      <div v-if="currentSpace" class="space-details">
        <!-- 车位状态卡片 -->
        <div class="space-status-card status-occupied">
          <div class="status-icon">
            <i class="el-icon-warning-outline"></i>
          </div>
          <div class="status-info">
            <div class="status-title">已占用</div>
            <div class="status-desc">该车位已被占用，无法停车</div>
          </div>
        </div>

        <!-- 车位信息卡片 -->
        <div class="info-card">
          <div class="card-title">
            <i class="el-icon-info"></i>
            <span>车位信息</span>
          </div>
          <div class="info-content">
            <div class="info-row">
              <span class="info-label">停车场</span>
              <span class="info-value">{{ currentSpace.parkingLotName }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">车位类型</span>
              <span class="info-value">
                <el-tag :type="getSpaceTypeTag(currentSpace.spaceType)" size="small">
                  {{ currentSpace.spaceTypeName }}
                </el-tag>
              </span>
            </div>
            <div class="info-row">
              <span class="info-label">占用时间</span>
              <span class="info-value">{{ currentSpace.occupiedSince || '未知' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">当前车辆</span>
              <div class="info-value highlight vehicle-info-dialog">
                <el-tag size="medium" type="warning" effect="plain" class="license-plate-tag">
                  {{ getVehicleLicensePlate(currentSpace) }}
                </el-tag>
                <div class="vehicle-details" v-if="currentSpace.vehicleInfo && currentSpace.vehicleInfo.includes('(')">
                  {{ getVehicleDetails(currentSpace) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getParkingLots, getParkingLot, getParkingSpaces } from '@/api/parkinglot'
import request from '@/utils/request'

export default {
  name: 'OccupiedSpaces',
  data() {
    return {
      loading: true,
      parkingLots: [],
      occupiedSpaces: [],
      spaceDialogVisible: false,
      currentSpace: null
    }
  },
  created() {
    this.fetchAllData()
  },
  methods: {
    async fetchAllData() {
      this.loading = true
      try {
        // 获取所有停车场
        const response = await getParkingLots()
        let lotsData = response.data || response

        if (Array.isArray(lotsData)) {
          this.parkingLots = lotsData
        } else if (lotsData.items && Array.isArray(lotsData.items)) {
          this.parkingLots = lotsData.items
        } else {
          console.error('无法解析停车场数据:', lotsData)
          this.parkingLots = []
        }

        console.log(`获取到 ${this.parkingLots.length} 个停车场`)

        // 清空已占用车位列表
        this.occupiedSpaces = []

        // 获取每个停车场的车位信息
        for (const lot of this.parkingLots) {
          await this.fetchParkingSpaces(lot)
        }

        console.log(`总共找到 ${this.occupiedSpaces.length} 个已占用车位`)

      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    async fetchParkingSpaces(lot) {
      try {
        const response = await getParkingSpaces(lot.id, { status: 1, limit: 100 })
        let spacesData = response.data || response

        let spaces = []
        if (Array.isArray(spacesData)) {
          spaces = spacesData
        } else if (spacesData.items && Array.isArray(spacesData.items)) {
          spaces = spacesData.items
        } else if (spacesData.spaces && Array.isArray(spacesData.spaces)) {
          spaces = spacesData.spaces
        } else {
          console.error(`无法解析停车场 ${lot.id} 的车位数据:`, spacesData)
          return
        }

        // 过滤出已占用的车位
        const occupiedSpaces = spaces.filter(space => space.status === 1)
        console.log(`停车场 ${lot.name} 有 ${occupiedSpaces.length} 个已占用车位`)

        // 添加到已占用车位列表
        for (const space of occupiedSpaces) {
          const spaceInfo = {
            id: space.id,
            parkingLotId: lot.id,
            parkingLotName: lot.name,
            spaceNumber: space.space_number,
            spaceType: space.type,
            spaceTypeName: this.getSpaceTypeName(space.type),
            status: space.status,
            currentVehicleId: space.current_vehicle_id,
            occupiedSince: space.occupied_since || '未知',
            vehicleInfo: null
          }

          this.occupiedSpaces.push(spaceInfo)

          // 获取车辆信息
          if (space.current_vehicle_id) {
            this.fetchVehicleInfo(spaceInfo)
          }
        }
      } catch (error) {
        console.error(`获取停车场 ${lot.id} 的车位数据失败:`, error)
      }
    },

    async fetchVehicleInfo(spaceInfo) {
      if (!spaceInfo.currentVehicleId) return

      try {
        const response = await request({
          url: `/api/bikes/${spaceInfo.currentVehicleId}`,
          method: 'get'
        })

        let vehicleData = null

        if (response.data && response.data.bike) {
          vehicleData = response.data.bike
        } else if (response.data && response.data.data && response.data.data.bike) {
          vehicleData = response.data.data.bike
        } else if (response.data) {
          vehicleData = response.data
        } else {
          vehicleData = response
        }

        if (vehicleData) {
          const licensePlate = vehicleData.b_num || vehicleData.bike_number || `车辆#${vehicleData.id || vehicleData.b_id || spaceInfo.currentVehicleId}`
          const brand = vehicleData.brand || '未知品牌'
          const color = vehicleData.color || ''
          const owner = vehicleData.owner_name || vehicleData.owner || ''

          spaceInfo.vehicleInfo = `${licensePlate} (${brand} ${color}${owner ? ', 车主: ' + owner : ''})`.trim()
          console.log(`车位 ${spaceInfo.spaceNumber} 的车辆信息:`, spaceInfo.vehicleInfo)
        } else {
          spaceInfo.vehicleInfo = `车辆#${spaceInfo.currentVehicleId}`
        }
      } catch (error) {
        console.error(`获取车辆 ${spaceInfo.currentVehicleId} 信息失败:`, error)
        spaceInfo.vehicleInfo = `车辆#${spaceInfo.currentVehicleId} (获取信息失败)`
      }
    },

    getSpaceTypeName(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || '未知类型'
    },

    getSpaceTypeTag(type) {
      const typeMap = {
        1: '',
        2: 'success',
        3: 'primary'
      }
      return typeMap[type] || 'info'
    },

    getVehicleLicensePlate(space) {
      if (!space.vehicleInfo) return `车辆#${space.currentVehicleId || '未知'}`

      const match = space.vehicleInfo.match(/^([^(]+)/)
      return match ? match[1].trim() : `车辆#${space.currentVehicleId || '未知'}`
    },

    getVehicleDetails(space) {
      if (!space.vehicleInfo || !space.vehicleInfo.includes('(')) return ''

      const match = space.vehicleInfo.match(/\((.+)\)/)
      return match ? match[1] : ''
    },

    showSpaceDetails(space) {
      this.currentSpace = space
      this.spaceDialogVisible = true
    },

    exportToExcel() {
      // 导出Excel的逻辑
      this.$message.success('导出Excel功能尚未实现')
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .dashboard-title {
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
    }

    .subtitle {
      margin: 0;
      font-size: 14px;
      color: #909399;
    }
  }

  .dashboard-actions {
    display: flex;
    gap: 10px;
  }
}

.vehicle-details {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

// 对话框样式
::v-deep .space-dialog {
  .el-dialog__header {
    padding: 16px 20px;
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;

    .el-dialog__title {
      font-weight: 600;
      font-size: 18px;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 24px;
  }
}

.space-details {
  // 车位状态卡片
  .space-status-card {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 10px;
    margin-bottom: 20px;
    background-color: #ffffff;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);

    &.status-occupied {
      border-left: 4px solid #e6a23c;

      .status-icon i {
        color: #e6a23c;
      }

      .status-title {
        color: #e6a23c;
      }
    }

    .status-icon {
      font-size: 32px;
      margin-right: 16px;

      i {
        font-size: 32px;
      }
    }

    .status-info {
      flex: 1;

      .status-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 6px;
      }

      .status-desc {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  // 信息卡片
  .info-card {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }

    .info-content {
      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px dashed #ebeef5;

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }

        .info-label {
          color: #909399;
          font-size: 14px;
        }

        .info-value {
          color: #303133;
          font-size: 14px;
          font-weight: 500;

          &.highlight {
            color: #409EFF;
            font-weight: 600;
          }

          &.vehicle-info-dialog {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 5px;

            .license-plate-tag {
              font-weight: bold;
              font-size: 14px;
              padding: 4px 8px;
              border-radius: 4px;
            }

            .vehicle-details {
              font-size: 12px;
              color: #606266;
            }
          }
        }
      }
    }
  }
}
</style>
