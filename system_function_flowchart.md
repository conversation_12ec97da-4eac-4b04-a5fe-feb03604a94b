```mermaid
flowchart TD
    %% 主要用户角色
    User([普通用户]) --- Auth
    Security([保安]) --- Auth
    Admin([管理员]) --- Auth
    
    %% 认证与权限模块
    Auth[认证与权限管理] --> UserMgmt[用户管理模块]
    
    %% 个人中心模块
    Auth --> PersonalCenter[个人中心]
    PersonalCenter --> MyProfile[个人信息管理]
    PersonalCenter --> MyVehicles[我的车辆]
    PersonalCenter --> MyParking[我的停车]
    PersonalCenter --> MyViolations[我的违规]
    
    %% 车辆管理模块
    MyVehicles --> VehicleReg[车辆注册]
    MyVehicles --> VehicleInfo[车辆信息管理]
    
    %% 停车中心模块
    ParkingCenter[停车中心] --> ParkingLotInfo[停车场信息管理]
    ParkingCenter --> ParkingSpaceMgmt[停车位管理]
    ParkingCenter --> ParkingService[停车服务]
    ParkingCenter --> ParkingStats[停车统计]
    
    %% 停车服务子流程
    ParkingService --> QuickParking[快速停车]
    ParkingService --> SpaceSelection[选择车位停车]
    ParkingService --> EndParking[结束停车]
    
    %% 充电中心模块
    ChargingCenter[充电中心] --> ChargingStats[充电统计]
    ChargingCenter --> ChargingSpaceMgmt[充电车位管理]
    ChargingCenter --> ChargingService[充电服务]
    ChargingCenter --> ChargingRecords[充电记录管理]
    
    %% 充电服务子流程
    ChargingService --> StartCharging[开始充电]
    ChargingService --> EndCharging[结束充电]
    
    %% 违规中心模块
    ViolationCenter[违规中心] --> ViolationRecordMgmt[违规记录管理]
    ViolationCenter --> AppealMgmt[申诉管理]
    ViolationCenter --> ViolationStats[违规统计分析]
    
    %% 违规记录管理子流程
    ViolationRecordMgmt --> RecordViolation[记录违规]
    ViolationRecordMgmt --> UploadEvidence[上传证据]
    ViolationRecordMgmt --> ProcessViolation[处理违规]
    
    %% 申诉管理子流程
    AppealMgmt --> SubmitAppeal[提交申诉]
    AppealMgmt --> ProcessAppeal[处理申诉]
    
    %% 公告管理模块
    AnnouncementCenter[公告管理] --> PublishAnnouncement[发布公告]
    AnnouncementCenter --> ViewAnnouncement[查看公告]
    
    %% 管理控制台
    AdminConsole[管理控制台] --> UserAdmin[用户管理]
    AdminConsole --> VehicleAdmin[车辆管理]
    AdminConsole --> ParkingAdmin[停车场管理]
    AdminConsole --> ChargingAdmin[充电设施管理]
    AdminConsole --> ViolationAdmin[违规管理]
    AdminConsole --> AnnouncementAdmin[公告管理]
    
    %% 数据库
    Database[(数据库)] --- UserMgmt
    Database --- VehicleReg
    Database --- ParkingCenter
    Database --- ChargingCenter
    Database --- ViolationCenter
    Database --- AnnouncementCenter
    
    %% 模块间关系
    Auth --> ParkingCenter
    Auth --> ChargingCenter
    Auth --> ViolationCenter
    Auth --> AnnouncementCenter
    Auth --> AdminConsole
    MyVehicles --> ParkingService
    MyParking --> ParkingService
    MyParking --> ChargingService
    MyViolations --> ViolationCenter
    ParkingService --> ChargingService
    
    %% 用户权限关系
    User -.-> MyProfile
    User -.-> MyVehicles
    User -.-> MyParking
    User -.-> MyViolations
    User -.-> ParkingLotInfo
    User -.-> ParkingService
    User -.-> ChargingService
    User -.-> ViewAnnouncement
    
    Security -.-> RecordViolation
    Security -.-> UploadEvidence
    
    Admin -.-> AdminConsole
    Admin -.-> ProcessViolation
    Admin -.-> ProcessAppeal
    Admin -.-> PublishAnnouncement
    
    %% 样式定义
    classDef userModule fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef adminModule fill:#ffcccc,stroke:#333,stroke-width:1px
    classDef securityModule fill:#ffffcc,stroke:#333,stroke-width:1px
    classDef database fill:#f9f9f9,stroke:#333,stroke-width:1px
    classDef authModule fill:#e6e6fa,stroke:#333,stroke-width:1px
    
    class Auth,UserMgmt,PersonalCenter,MyProfile,MyVehicles,MyParking,MyViolations authModule
    class ParkingCenter,ParkingLotInfo,ParkingSpaceMgmt,ParkingService,ParkingStats,QuickParking,SpaceSelection,EndParking userModule
    class ChargingCenter,ChargingStats,ChargingSpaceMgmt,ChargingService,ChargingRecords,StartCharging,EndCharging userModule
    class ViolationCenter,ViolationRecordMgmt,AppealMgmt,ViolationStats,SubmitAppeal userModule
    class RecordViolation,UploadEvidence securityModule
    class AdminConsole,UserAdmin,VehicleAdmin,ParkingAdmin,ChargingAdmin,ViolationAdmin,AnnouncementAdmin,ProcessViolation,ProcessAppeal,PublishAnnouncement adminModule
    class AnnouncementCenter,ViewAnnouncement userModule
    class Database database
```
