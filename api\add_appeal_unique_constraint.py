"""
为Appeal表添加唯一约束

此脚本用于为Appeal表的violation_id字段添加唯一约束，
确保一个违规记录只能有一个申诉记录。
"""

from app import create_app, db
from app.violations.models import Appeal
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = create_app()

def check_constraint_exists(table_name, constraint_name):
    """检查约束是否存在"""
    with db.engine.connect() as conn:
        # 使用SQLite的PRAGMA查询表约束
        result = conn.execute(text(f"PRAGMA index_list({table_name})"))
        constraints = [row[1] for row in result]
        return constraint_name in constraints

def add_unique_constraint():
    """为Appeal表的violation_id字段添加唯一约束"""
    try:
        with app.app_context():
            # 检查是否有重复的violation_id
            duplicate_check = db.session.query(Appeal.violation_id)\
                .group_by(Appeal.violation_id)\
                .having(db.func.count(Appeal.id) > 1)\
                .all()
            
            if duplicate_check:
                logger.error(f"发现{len(duplicate_check)}个违规记录有多个申诉，无法添加唯一约束")
                logger.error(f"违规记录ID: {[item[0] for item in duplicate_check]}")
                logger.error("请先处理这些重复数据，保留每个违规记录的一个申诉")
                return False
            
            # 检查约束是否已存在
            constraint_name = "uq_appeal_violation_id"
            if check_constraint_exists("appeals", constraint_name):
                logger.info("唯一约束已存在，无需添加")
                return True
            
            # 添加唯一约束
            with db.engine.connect() as conn:
                conn.execute(text(f"CREATE UNIQUE INDEX {constraint_name} ON appeals (violation_id)"))
                logger.info("成功为Appeal表的violation_id字段添加唯一约束")
                return True
    except Exception as e:
        logger.error(f"添加唯一约束失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = add_unique_constraint()
    if success:
        print("唯一约束添加成功")
    else:
        print("唯一约束添加失败，请查看日志")
