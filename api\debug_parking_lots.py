from flask import Flask, jsonify, request
from app import create_app
from app.parkinglots.models import ParkingLot, ParkingSpace
import json

app = create_app()

# 在应用上下文中执行API调用
with app.app_context():
    # 查询所有停车场
    parking_lots = ParkingLot.query.all()
    
    print(f"停车场总数: {len(parking_lots)}")
    
    # 打印每个停车场的详细信息
    for lot in parking_lots:
        print(f"\n停车场ID: {lot.id}, 名称: {lot.name}")
        print(f"总车位数: {lot.total_spaces}, 已占用车位数: {lot.occupied_spaces}")
        
        # 计算实际的车位数量
        total_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id).count()
        occupied_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id, status=1).count()
        
        print(f"实际总车位数: {total_spaces}, 实际已占用车位数: {occupied_spaces}")
        
        # 检查是否有不一致
        if lot.total_spaces != total_spaces or lot.occupied_spaces != occupied_spaces:
            print(f"警告: 停车场 {lot.name} 的车位数据不一致!")
            print(f"  数据库记录: 总车位数={lot.total_spaces}, 已占用车位数={lot.occupied_spaces}")
            print(f"  实际统计: 总车位数={total_spaces}, 已占用车位数={occupied_spaces}")
        
        # 计算使用率
        utilization_rate = 0
        if total_spaces > 0:
            utilization_rate = round(occupied_spaces / total_spaces * 100, 2)
        
        print(f"使用率: {utilization_rate}%")
        
        # 查询该停车场的所有车位
        spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id).all()
        
        # 按状态分组统计
        status_counts = {}
        for space in spaces:
            status = space.status
            if status not in status_counts:
                status_counts[status] = 0
            status_counts[status] += 1
        
        print(f"车位状态统计: {status_counts}")
        
        # 0空闲，1已占用，2故障，3维修中，4禁用
        status_map = {0: '空闲', 1: '已占用', 2: '故障', 3: '维修中', 4: '禁用'}
        for status, count in status_counts.items():
            print(f"  {status_map.get(status, f'未知状态({status})')}: {count}个")
