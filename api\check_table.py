import sqlite3

# 连接到数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 获取所有表名
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = [table[0] for table in cursor.fetchall()]
print("数据库表:", tables)

# 检查是否存在 status_change_logs 表
if 'status_change_logs' in tables:
    print("status_change_logs 表已存在")
    
    # 获取表结构
    cursor.execute("PRAGMA table_info(status_change_logs)")
    columns = cursor.fetchall()
    print("\nstatus_change_logs 表结构:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
else:
    print("status_change_logs 表不存在")

# 关闭连接
cursor.close()
conn.close()
