import sqlite3

# 连接到数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 获取所有表名
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = [table[0] for table in cursor.fetchall()]
print("数据库表:", tables)

# 获取停车场表结构
if 'parking_lots' in tables:
    cursor.execute("PRAGMA table_info(parking_lots)")
    columns = cursor.fetchall()
    print("\n停车场表结构:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")

# 获取车位表结构
if 'parking_spaces' in tables:
    cursor.execute("PRAGMA table_info(parking_spaces)")
    columns = cursor.fetchall()
    print("\n车位表结构:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")

# 获取停车记录表结构
if 'parking_records' in tables:
    cursor.execute("PRAGMA table_info(parking_records)")
    columns = cursor.fetchall()
    print("\n停车记录表结构:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")

# 关闭连接
conn.close()
