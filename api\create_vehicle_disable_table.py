from app import create_app, db
from app.violations.models import VehicleDisableRecord
from app.bikes.models import Bikes
from flask import current_app
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = create_app()

with app.app_context():
    # 创建车辆禁用记录表
    try:
        print("开始创建车辆禁用记录表...")
        db.create_all()
        print("车辆禁用记录表创建成功")
        
        # 查询车牌号为111的车辆
        bike = Bikes.query.filter_by(b_num='111').first()
        if bike:
            print(f'找到车辆: ID={bike.b_id}, 车牌号={bike.b_num}, 状态={bike.status}, 所属用户={bike.belong_to}')
            
            # 更新车辆状态为"废弃"
            bike.status = "废弃"
            db.session.commit()
            print(f'已将车辆状态更新为: {bike.status}')
        else:
            print('未找到车牌号为111的车辆')
            
    except Exception as e:
        print(f"创建表或更新车辆状态失败: {str(e)}")
        db.session.rollback()
