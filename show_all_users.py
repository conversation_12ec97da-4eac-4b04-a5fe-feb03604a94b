#!/usr/bin/env python
import sqlite3

def main():
    """显示所有用户信息和密码"""
    # 连接数据库
    conn = sqlite3.connect('api/sys.db')
    cursor = conn.cursor()
    
    # 查询所有用户
    cursor.execute("SELECT u_id, u_name, u_role, u_belong, u_phone FROM users ORDER BY u_id")
    users = cursor.fetchall()
    
    # 关闭连接
    conn.close()
    
    # 用户密码映射
    passwords = {
        'admin': 'admin123',
        'user1': 'user123',
        'user2': 'user123',
        'security': 'security123',
        'manager': 'manager123'
    }
    
    # 显示用户信息
    print("\n系统中的所有用户信息：")
    print("=" * 80)
    print("ID | 用户名 | 角色 | 所属部门 | 电话 | 密码")
    print("-" * 80)
    
    for user in users:
        user_id, username, role, belong, phone = user
        password = passwords.get(username, "未知")
        print(f"{user_id} | {username} | {role} | {belong or '无'} | {phone or '无'} | {password}")
    
    print("=" * 80)
    print("\n注意：admin用户的密码已被重置为 admin123")

if __name__ == "__main__":
    main()
