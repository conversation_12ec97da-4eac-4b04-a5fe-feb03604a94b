#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建Windows服务的脚本
需要安装pywin32: pip install pywin32
"""

import sys
import os
import win32service
import win32serviceutil
import win32event
import servicemanager
import socket
import time
import eventlet

# 在导入任何其他模块之前执行eventlet的monkey patching
eventlet.monkey_patch()

class FlaskService(win32serviceutil.ServiceFramework):
    _svc_name_ = "CampusEVManagementSystem"
    _svc_display_name_ = "校园电动车管理系统"
    _svc_description_ = "校园电动车管理系统后端服务"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        self.is_running = True

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False

    def SvcDoRun(self):
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        self.main()

    def main(self):
        # 设置环境变量
        os.environ['FLASK_APP'] = 'wsgi.py'
        os.environ['FLASK_ENV'] = 'production'
        
        # 导入应用
        from waitress import serve
        from wsgi import app
        
        # 启动服务器
        serve(app, host='0.0.0.0', port=5000, threads=4)
        
        # 等待服务停止信号
        while self.is_running:
            time.sleep(1)

if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(FlaskService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(FlaskService)
