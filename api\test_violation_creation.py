#!/usr/bin/env python
"""
测试违规录入功能
模拟前端发送请求，测试违规记录创建API
"""

import os
import sys
import json
import requests
from datetime import datetime

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# API基础URL
BASE_URL = "http://127.0.0.1:5000"

# 测试数据
TEST_USERNAME = "test_security"
TEST_PASSWORD = "password123"

# 登录并获取令牌
def login():
    print("登录测试账号...")
    login_url = f"{BASE_URL}/api/users/login"
    login_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(login_url, json=login_data)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 20000 and data.get("data", {}).get("token"):
            token = data["data"]["token"]
            print(f"登录成功，获取到令牌: {token[:20]}...")
            return token
    
    print(f"登录失败: {response.text}")
    return None

# 获取车辆列表
def get_bikes(token):
    print("获取车辆列表...")
    bikes_url = f"{BASE_URL}/api/bikes"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(bikes_url, headers=headers)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 20000 and data.get("data", {}).get("bikes"):
            bikes = data["data"]["bikes"]
            print(f"获取到 {len(bikes)} 辆车")
            return bikes
    
    print(f"获取车辆列表失败: {response.text}")
    return []

# 创建违规记录 - 有效数据
def create_violation_valid(token, bike):
    print("\n测试用例1: 创建违规记录 - 有效数据")
    violation_url = f"{BASE_URL}/api/violations/records"
    headers = {"Authorization": f"Bearer {token}"}
    
    # 准备有效的违规记录数据
    violation_data = {
        "bike_number": bike["bike_number"],
        "user_id": bike["user_id"],
        "violation_type": "违规停车",
        "violation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "location": "校园南门",
        "description": "这是一条测试违规记录，使用有效数据创建。"
    }
    
    print(f"发送数据: {json.dumps(violation_data, ensure_ascii=False)}")
    response = requests.post(violation_url, json=violation_data, headers=headers)
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    return response.json() if response.status_code == 200 else None

# 创建违规记录 - 缺少必填字段
def create_violation_missing_fields(token, bike):
    print("\n测试用例2: 创建违规记录 - 缺少必填字段")
    violation_url = f"{BASE_URL}/api/violations/records"
    headers = {"Authorization": f"Bearer {token}"}
    
    # 准备缺少必填字段的违规记录数据
    violation_data = {
        "bike_number": bike["bike_number"],
        # 缺少 user_id
        "violation_type": "违规停车",
        # 缺少 violation_time
        "location": "校园南门",
        "description": "这是一条测试违规记录，缺少必填字段。"
    }
    
    print(f"发送数据: {json.dumps(violation_data, ensure_ascii=False)}")
    response = requests.post(violation_url, json=violation_data, headers=headers)
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    return response.json() if response.status_code == 200 else None

# 创建违规记录 - 无效的用户ID
def create_violation_invalid_user_id(token, bike):
    print("\n测试用例3: 创建违规记录 - 无效的用户ID")
    violation_url = f"{BASE_URL}/api/violations/records"
    headers = {"Authorization": f"Bearer {token}"}
    
    # 准备无效用户ID的违规记录数据
    violation_data = {
        "bike_number": bike["bike_number"],
        "user_id": "invalid_id",  # 无效的用户ID
        "violation_type": "违规停车",
        "violation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "location": "校园南门",
        "description": "这是一条测试违规记录，使用无效的用户ID。"
    }
    
    print(f"发送数据: {json.dumps(violation_data, ensure_ascii=False)}")
    response = requests.post(violation_url, json=violation_data, headers=headers)
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    return response.json() if response.status_code == 200 else None

# 主函数
def main():
    # 登录
    token = login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 获取车辆列表
    bikes = get_bikes(token)
    if not bikes:
        print("获取车辆列表失败，无法继续测试")
        return
    
    # 选择第一辆车进行测试
    test_bike = bikes[0]
    print(f"选择车辆进行测试: {test_bike['bike_number']}, 车主ID: {test_bike['user_id']}")
    
    # 测试用例1: 创建违规记录 - 有效数据
    result1 = create_violation_valid(token, test_bike)
    
    # 测试用例2: 创建违规记录 - 缺少必填字段
    result2 = create_violation_missing_fields(token, test_bike)
    
    # 测试用例3: 创建违规记录 - 无效的用户ID
    result3 = create_violation_invalid_user_id(token, test_bike)
    
    # 总结测试结果
    print("\n测试结果总结:")
    print(f"测试用例1 (有效数据): {'成功' if result1 and result1.get('code') == 20000 else '失败'}")
    print(f"测试用例2 (缺少必填字段): {'预期失败' if not result2 or result2.get('code') != 20000 else '意外成功'}")
    print(f"测试用例3 (无效的用户ID): {'预期失败' if not result3 or result3.get('code') != 20000 else '意外成功'}")

if __name__ == "__main__":
    main()
