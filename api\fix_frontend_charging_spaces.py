#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复前端显示的充电车位编号格式
将所有充电车位编号统一为 C-{lot_id}-{i} 格式
"""

import sqlite3
import os
import sys
import logging
import json
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_frontend_charging_spaces():
    """修复前端显示的充电车位编号格式"""
    # 连接数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    try:
        # 查询所有停车场
        cursor.execute("SELECT id, name FROM parking_lots")
        parking_lots = cursor.fetchall()
        
        logger.info(f"找到 {len(parking_lots)} 个停车场")
        
        # 遍历每个停车场
        for lot_id, lot_name in parking_lots:
            logger.info(f"处理停车场 {lot_name} (ID: {lot_id})")
            
            # 查询该停车场的所有充电车位
            cursor.execute("SELECT id, space_number FROM parking_spaces WHERE parking_lot_id = ? AND type = 3", (lot_id,))
            charging_spaces = cursor.fetchall()
            
            logger.info(f"  找到 {len(charging_spaces)} 个充电车位")
            
            # 如果没有充电车位，跳过
            if not charging_spaces:
                continue
            
            # 按ID排序充电车位
            charging_spaces.sort(key=lambda s: s[0])
            
            # 生成前端显示的充电车位数据
            frontend_spaces = []
            for i, (space_id, space_number) in enumerate(charging_spaces, 1):
                frontend_spaces.append({
                    'id': space_id,
                    'parking_lot_id': lot_id,
                    'space_number': space_number,
                    'type': 3,
                    'type_text': '充电车位',
                    'status': 0,
                    'status_text': '空闲',
                    'power': 7.0,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                })
            
            # 将数据保存到JSON文件
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'frontend_data')
            os.makedirs(output_dir, exist_ok=True)
            
            output_file = os.path.join(output_dir, f'charging_spaces_lot_{lot_id}.json')
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(frontend_spaces, f, ensure_ascii=False, indent=2)
            
            logger.info(f"  已生成前端充电车位数据: {output_file}")
        
        logger.info("前端充电车位数据生成完成")
        
    except Exception as e:
        logger.error(f"生成前端充电车位数据失败: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    # 切换到包含数据库的目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    fix_frontend_charging_spaces()
