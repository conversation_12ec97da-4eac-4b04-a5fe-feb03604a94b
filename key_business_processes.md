```mermaid
sequenceDiagram
    %% 参与者定义
    actor 用户
    actor 管理员
    actor 保安
    participant 前端界面
    participant 认证服务
    participant 用户服务
    participant 车辆服务
    participant 停车服务
    participant 充电服务
    participant 违规服务
    participant 数据库
    
    %% 电动车注册流程
    rect rgb(240, 248, 255)
    Note over 用户,数据库: 电动车注册流程
    用户->>前端界面: 访问车辆管理页面
    前端界面->>认证服务: 验证用户身份
    认证服务->>用户服务: 获取用户信息
    用户服务->>数据库: 查询用户数据
    数据库-->>用户服务: 返回用户数据
    用户服务-->>认证服务: 确认用户身份
    认证服务-->>前端界面: 授权访问
    前端界面->>用户: 显示车辆管理界面
    用户->>前端界面: 提交车辆信息(车牌号、品牌、颜色等)
    前端界面->>车辆服务: 发送车辆注册请求
    车辆服务->>数据库: 检查车牌号是否已存在
    数据库-->>车辆服务: 返回检查结果
    alt 车牌号已存在
        车辆服务-->>前端界面: 返回错误信息
        前端界面-->>用户: 显示错误提示
    else 车牌号可用
        车辆服务->>数据库: 保存车辆信息
        数据库-->>车辆服务: 确认保存成功
        车辆服务-->>前端界面: 返回注册成功
        前端界面-->>用户: 显示注册成功提示
    end
    end
    
    %% 停车流程
    rect rgb(255, 248, 240)
    Note over 用户,数据库: 停车流程
    用户->>前端界面: 访问停车中心
    前端界面->>认证服务: 验证用户身份
    认证服务-->>前端界面: 授权访问
    前端界面->>停车服务: 请求停车场列表
    停车服务->>数据库: 查询停车场信息
    数据库-->>停车服务: 返回停车场数据
    停车服务-->>前端界面: 返回停车场列表
    前端界面-->>用户: 显示停车场卡片
    用户->>前端界面: 选择停车场查看详情
    前端界面->>停车服务: 请求停车场详情和车位状态
    停车服务->>数据库: 查询车位信息
    数据库-->>停车服务: 返回车位数据
    停车服务-->>前端界面: 返回车位网格图
    前端界面-->>用户: 显示车位网格和状态
    用户->>前端界面: 选择车位并点击"停车"
    前端界面->>车辆服务: 获取用户车辆列表
    车辆服务->>数据库: 查询用户车辆
    数据库-->>车辆服务: 返回车辆数据
    车辆服务-->>前端界面: 返回车辆列表
    前端界面-->>用户: 显示车辆选择对话框
    用户->>前端界面: 选择车辆并确认停车
    前端界面->>停车服务: 发送停车请求
    停车服务->>数据库: 创建停车记录
    停车服务->>数据库: 更新车位状态为占用
    数据库-->>停车服务: 确认更新成功
    停车服务-->>前端界面: 返回停车成功
    前端界面-->>用户: 显示停车成功提示
    end
    
    %% 充电流程
    rect rgb(240, 255, 240)
    Note over 用户,数据库: 充电流程
    用户->>前端界面: 访问个人中心-我的停车
    前端界面->>停车服务: 获取进行中的停车记录
    停车服务->>数据库: 查询用户停车记录
    数据库-->>停车服务: 返回停车数据
    停车服务-->>前端界面: 返回停车记录列表
    前端界面-->>用户: 显示进行中的停车记录
    用户->>前端界面: 点击"开始充电"按钮
    前端界面->>停车服务: 验证停车记录状态
    停车服务->>数据库: 查询车位类型
    数据库-->>停车服务: 返回车位信息
    alt 非充电车位
        停车服务-->>前端界面: 返回错误信息
        前端界面-->>用户: 提示"非充电车位"
    else 充电车位
        前端界面->>充电服务: 发送充电请求
        充电服务->>数据库: 创建充电记录
        充电服务->>数据库: 更新车位充电状态
        数据库-->>充电服务: 确认更新成功
        充电服务-->>前端界面: 返回充电开始成功
        前端界面-->>用户: 显示充电已开始提示
    end
    end
    
    %% 违规处理流程
    rect rgb(255, 240, 240)
    Note over 保安,数据库: 违规记录与处理流程
    保安->>前端界面: 访问违规记录页面
    前端界面->>认证服务: 验证保安身份
    认证服务-->>前端界面: 授权访问
    保安->>前端界面: 填写违规信息并提交
    前端界面->>违规服务: 发送违规记录请求
    违规服务->>车辆服务: 验证车辆信息
    车辆服务->>数据库: 查询车辆所有者
    数据库-->>车辆服务: 返回车辆和所有者数据
    车辆服务-->>违规服务: 返回车辆验证结果
    违规服务->>数据库: 保存违规记录
    数据库-->>违规服务: 确认保存成功
    违规服务-->>前端界面: 返回记录成功
    前端界面-->>保安: 显示记录成功提示
    
    Note over 用户,数据库: 用户申诉流程
    用户->>前端界面: 访问违规中心
    前端界面->>违规服务: 获取用户违规记录
    违规服务->>数据库: 查询违规数据
    数据库-->>违规服务: 返回违规记录
    违规服务-->>前端界面: 返回违规列表
    前端界面-->>用户: 显示违规记录
    用户->>前端界面: 点击"申诉"按钮
    前端界面-->>用户: 显示申诉表单
    用户->>前端界面: 填写申诉理由并提交
    前端界面->>违规服务: 发送申诉请求
    违规服务->>数据库: 保存申诉记录
    违规服务->>数据库: 更新违规状态为"申诉中"
    数据库-->>违规服务: 确认更新成功
    违规服务-->>前端界面: 返回申诉成功
    前端界面-->>用户: 显示申诉已提交提示
    
    Note over 管理员,数据库: 管理员处理申诉流程
    管理员->>前端界面: 访问违规管理页面
    前端界面->>认证服务: 验证管理员身份
    认证服务-->>前端界面: 授权访问
    前端界面->>违规服务: 获取申诉中的记录
    违规服务->>数据库: 查询申诉数据
    数据库-->>违规服务: 返回申诉记录
    违规服务-->>前端界面: 返回申诉列表
    前端界面-->>管理员: 显示申诉记录
    管理员->>前端界面: 选择申诉并处理
    前端界面-->>管理员: 显示申诉详情和处理选项
    管理员->>前端界面: 选择处理结果并提交
    前端界面->>违规服务: 发送申诉处理请求
    违规服务->>数据库: 更新申诉状态和结果
    违规服务->>数据库: 更新违规记录状态
    数据库-->>违规服务: 确认更新成功
    违规服务-->>前端界面: 返回处理成功
    前端界面-->>管理员: 显示处理成功提示
    end
```
