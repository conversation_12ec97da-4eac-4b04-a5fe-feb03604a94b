import sqlite3
import datetime

# 连接数据库
conn = sqlite3.connect('sys.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# 查询充电记录总数
cursor.execute('SELECT COUNT(*) FROM charging_records')
total = cursor.fetchone()[0]
print(f'充电记录总数: {total}')

# 查询充电记录详情
print('\n充电记录详情:')
cursor.execute('SELECT id, vehicle_id, user_id, parking_lot_id, parking_space_id, start_time, end_time, status FROM charging_records')
records = cursor.fetchall()
for record in records:
    print(f"ID: {record['id']}, 车辆ID: {record['vehicle_id']}, 用户ID: {record['user_id']}, 停车场ID: {record['parking_lot_id']}, 车位ID: {record['parking_space_id']}, 开始时间: {record['start_time']}, 结束时间: {record['end_time']}, 状态: {record['status']}")

# 检查过去7天的记录
print('\n过去7天的充电记录:')
now = datetime.datetime.now()
seven_days_ago = (now - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
cursor.execute('SELECT COUNT(*) FROM charging_records WHERE start_time >= ?', (seven_days_ago,))
recent_count = cursor.fetchone()[0]
print(f'过去7天的充电记录数: {recent_count}')

# 查看表结构
print('\n充电记录表结构:')
cursor.execute('PRAGMA table_info(charging_records)')
for column in cursor.fetchall():
    print(f"列名: {column['name']}, 类型: {column['type']}, 是否可空: {column['notnull']}")

# 关闭连接
conn.close()
