from flask import Flask
from app import create_app
import json

app = create_app()

# 在应用上下文中执行API调用
with app.app_context():
    # 使用Flask的测试客户端发送请求
    client = app.test_client()
    response = client.get('/api/parking-records/stats?date_range=week')
    
    # 打印响应
    print("API响应状态码:", response.status_code)
    
    # 解析响应数据
    data = json.loads(response.data.decode('utf-8'))
    
    # 检查parking_lots数据
    if 'data' in data and 'parking_lots' in data['data']:
        parking_lots = data['data']['parking_lots']
        print("\nparking_lots数据:")
        print(json.dumps(parking_lots, indent=2, ensure_ascii=False))
        
        # 检查是否有非零数据
        has_non_zero_utilization = False
        for lot in parking_lots:
            if lot.get('utilization_rate', 0) > 0:
                has_non_zero_utilization = True
                break
        
        print("\n是否有非零使用率:", "是" if has_non_zero_utilization else "否")
        
        # 检查每个停车场的数据
        for lot in parking_lots:
            print(f"\n停车场: {lot.get('name')}")
            print(f"  ID: {lot.get('id')}")
            print(f"  总车位数: {lot.get('total_spaces')}")
            print(f"  已占用车位数: {lot.get('occupied_spaces')}")
            print(f"  活跃记录数: {lot.get('active_records')}")
            print(f"  使用率: {lot.get('utilization_rate')}%")
    else:
        print("\n响应中没有parking_lots数据")
