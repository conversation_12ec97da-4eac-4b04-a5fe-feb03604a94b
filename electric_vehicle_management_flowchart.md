```mermaid
flowchart TD
    A[电动车管理模块] --> B[用户信息管理]
    A --> C[车辆管理]
    A --> D[数据可视化]
    A --> E[筛选功能]
    
    %% 用户信息管理子功能
    B --> B1[用户列表]
    B --> B2[用户详情]
    B --> B3[用户表单]
    
    B1 --> B1a[显示用户ID、用户名、角色等]
    B1 --> B1b[分页、排序和筛选]
    B1 --> B1c[操作按钮：查看、编辑、删除]
    
    B2 --> B2a[基本信息]
    B2 --> B2b[账号信息]
    B2 --> B2c[联系信息]
    B2 --> B2d[关联车辆信息]
    
    B3 --> B3a[添加/编辑用户表单]
    B3 --> B3b[表单验证]
    
    %% 车辆管理子功能
    C --> C1[车辆列表]
    C --> C2[车辆统计卡片]
    C --> C3[车辆表单]
    C --> C4[数据可视化区域]
    
    C1 --> C1a[显示车牌号、品牌、颜色等]
    C1 --> C1b[分页、排序和筛选]
    C1 --> C1c[操作按钮：查看、编辑、删除]
    
    C2 --> C2a[车辆总数]
    C2 --> C2b[正常使用车辆数量]
    C2 --> C2c[停用状态车辆数量]
    C2 --> C2d[各类型车辆数量分布]
    
    C3 --> C3a[添加/编辑车辆表单]
    C3 --> C3b[表单验证]
    
    C4 --> C4a[车辆品牌分布图表]
    C4 --> C4b[车辆类型分布图表]
    C4 --> C4c[图表控制按钮]
    
    %% 数据可视化子功能
    D --> D1[用户角色分布图表]
    D --> D2[部门分布图表]
    D --> D3[车辆品牌分布图表]
    D --> D4[车辆类型分布图表]
    D --> D5[图表控制区域]
    
    D1 --> D1a[饼图展示角色分布]
    D1 --> D1b[图例说明]
    D1 --> D1c[悬停提示]
    
    D2 --> D2a[柱状图展示部门分布]
    D2 --> D2b[悬停提示]
    
    D3 --> D3a[饼图/柱状图展示品牌分布]
    D3 --> D3b[图例说明]
    D3 --> D3c[悬停提示]
    
    D4 --> D4a[饼图/柱状图展示类型分布]
    D4 --> D4b[图例说明]
    D4 --> D4c[悬停提示]
    
    D5 --> D5a[图表类型切换按钮]
    D5 --> D5b[导出图表按钮]
    D5 --> D5c[显示/隐藏图表按钮]
    
    %% 筛选功能子功能
    E --> E1[用户筛选表单]
    E --> E2[车辆筛选表单]
    
    E1 --> E1a[用户名输入框]
    E1 --> E1b[角色选择下拉框]
    E1 --> E1c[部门输入框]
    E1 --> E1d[账号状态选择框]
    E1 --> E1e[搜索和重置按钮]
    
    E2 --> E2a[车牌号输入框]
    E2 --> E2b[所属用户输入框]
    E2 --> E2c[品牌选择下拉框]
    E2 --> E2d[车辆类型选择下拉框]
    E2 --> E2e[状态选择下拉框]
    E2 --> E2f[搜索和重置按钮]
    
    %% 管理员权限说明
    classDef adminOnly fill:#f9f,stroke:#333,stroke-width:2px
    class A adminOnly
    
    %% 添加说明
    subgraph 说明
        Z[管理员专属功能]:::adminOnly
    end
```
