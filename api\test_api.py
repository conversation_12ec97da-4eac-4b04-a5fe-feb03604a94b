import requests
import json
from datetime import datetime

# 基础URL
base_url = 'http://localhost:5000'

# 登录获取token
def get_token():
    login_url = f'{base_url}/api/login'
    login_data = {
        'username': 'admin',
        'password': '111111'
    }
    response = requests.post(login_url, json=login_data)
    try:
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            if token:
                print(f'登录成功，获取到token: {token[:20]}...')
                return token
            else:
                print(f'登录成功但未获取到token: {data}')
                return None
        else:
            print(f'登录失败: {response.text}')
            return None
    except Exception as e:
        print(f'登录过程中发生错误: {str(e)}')
        return None

# 测试充电每日统计API
def test_charging_daily_stats(token):
    url = f'{base_url}/api/charging-daily-stats'
    headers = {'Authorization': f'Bearer {token}'}
    params = {
        'date_range': 'week',
        '_t': int(datetime.now().timestamp() * 1000)
    }

    print(f'\n测试充电每日统计API: {url}')
    print(f'请求参数: {params}')

    response = requests.get(url, headers=headers, params=params)
    print(f'状态码: {response.status_code}')

    if response.status_code == 200:
        data = response.json()
        print(f'响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}')
        return data
    else:
        print(f'请求失败: {response.text}')
        return None

# 测试充电小时统计API
def test_charging_hourly_stats(token):
    url = f'{base_url}/api/charging-hourly-stats'
    headers = {'Authorization': f'Bearer {token}'}
    params = {
        'date_range': 'week',
        '_t': int(datetime.now().timestamp() * 1000)
    }

    print(f'\n测试充电小时统计API: {url}')
    print(f'请求参数: {params}')

    response = requests.get(url, headers=headers, params=params)
    print(f'状态码: {response.status_code}')

    if response.status_code == 200:
        data = response.json()
        print(f'响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}')
        return data
    else:
        print(f'请求失败: {response.text}')
        return None

# 主函数
if __name__ == '__main__':
    token = get_token()
    if token:
        daily_stats = test_charging_daily_stats(token)
        hourly_stats = test_charging_hourly_stats(token)

        # 检查数据是否为空
        if daily_stats and 'data' in daily_stats and 'data' in daily_stats['data']:
            data = daily_stats['data']['data']
            if not data:
                print('\n每日统计数据为空!')
            else:
                print(f'\n每日统计数据条数: {len(data)}')
                # 打印有数据的日期
                data_days = [item for item in data if item['count'] > 0]
                if data_days:
                    print('有数据的日期:')
                    for day in data_days:
                        print(f"  {day['date']}: {day['count']}条记录")

        if hourly_stats and 'data' in hourly_stats and 'data' in hourly_stats['data']:
            data = hourly_stats['data']['data']
            if not data:
                print('\n小时统计数据为空!')
            else:
                print(f'\n小时统计数据条数: {len(data)}')
                # 检查是否所有count都为0
                non_zero_hours = [item for item in data if item['count'] > 0]
                if not non_zero_hours:
                    print('警告: 所有小时统计数据的count都为0!')
                else:
                    print('有数据的小时:')
                    for hour in non_zero_hours:
                        print(f"  {hour['hour']}点: {hour['count']}条记录")
