import sqlite3
import hashlib
import uuid
import random
from datetime import datetime

def main():
    """修改admin用户密码并添加测试用户和车辆"""
    print("开始更新用户和车辆信息...")

    # 连接到数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()

    try:
        # 1. 修改admin用户密码
        update_admin_password(cursor, conn)

        # 2. 添加测试用户
        test_users = create_test_users(cursor, conn)

        # 3. 为每个用户添加车辆
        create_vehicles_for_users(cursor, conn, test_users)

        # 4. 输出所有用户和车辆信息
        print_all_users_and_vehicles(cursor)

        print("更新完成!")

    except Exception as e:
        print(f"更新过程中出错: {e}")
        conn.rollback()
    finally:
        conn.close()

def update_admin_password(cursor, conn):
    """修改admin用户密码为至少6位数"""
    print("\n1. 修改admin用户密码")

    # 查找admin用户
    cursor.execute("SELECT * FROM users WHERE u_name = 'admin'")
    admin_user = cursor.fetchone()

    if admin_user:
        user_id = admin_user[0]
        salt = admin_user[4]  # 假设salt在第5列

        # 如果没有salt，生成一个新的
        if not salt:
            salt = str(uuid.uuid4())

        # 新密码
        new_password = "admin123"
        password_hash = hashlib.sha256(f"{new_password}{salt}".encode()).hexdigest()

        # 更新users表中的密码
        cursor.execute("UPDATE users SET u_pwd = ?, salt = ? WHERE u_id = ?",
                      (password_hash, salt, user_id))

        # 更新players表中的密码
        cursor.execute("SELECT * FROM players WHERE username = 'admin'")
        admin_player = cursor.fetchone()

        if admin_player:
            player_id = admin_player[0]
            player_password = hashlib.sha256(new_password.encode()).hexdigest()
            cursor.execute("UPDATE players SET password = ? WHERE id = ?",
                          (player_password, player_id))

        conn.commit()
        print(f"已更新admin用户密码为: {new_password}")
    else:
        print("未找到admin用户")

def create_test_users(cursor, conn):
    """创建测试用户"""
    print("\n2. 创建测试用户")

    test_users = [
        {"username": "security1", "password": "security123", "role": "security", "phone": "13811111111", "belong": "保安部门"},
        {"username": "security2", "password": "security456", "role": "security", "phone": "13822222222", "belong": "保安部门"},
        {"username": "teacher1", "password": "teacher123", "role": "regular", "phone": "13833333333", "belong": "教师"},
        {"username": "teacher2", "password": "teacher456", "role": "regular", "phone": "13844444444", "belong": "教师"},
        {"username": "student1", "password": "student123", "role": "regular", "phone": "13855555555", "belong": "学生"},
        {"username": "student2", "password": "student456", "role": "regular", "phone": "13866666666", "belong": "学生"},
        {"username": "admin2", "password": "admin456", "role": "admin", "phone": "13877777777", "belong": "管理员"}
    ]

    created_users = []

    for user in test_users:
        # 检查用户是否已存在
        cursor.execute("SELECT * FROM users WHERE u_name = ?", (user["username"],))
        existing_user = cursor.fetchone()

        if existing_user:
            print(f"用户 {user['username']} 已存在，跳过创建")
            created_users.append({"id": existing_user[0], **user})
            continue

        # 获取最大用户ID
        cursor.execute("SELECT MAX(u_id) FROM users")
        max_id = cursor.fetchone()[0] or 0
        user_id = max_id + 1

        # 生成盐值
        salt = str(uuid.uuid4())

        # 哈希密码
        password_hash = hashlib.sha256(f"{user['password']}{salt}".encode()).hexdigest()

        # 创建users表记录
        cursor.execute("""
        INSERT INTO users (u_id, u_name, u_pwd, u_role, salt, u_belong, u_phone)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (user_id, user["username"], password_hash, user["role"],
              salt, user["belong"], user["phone"]))

        # 创建players表记录
        player_password = hashlib.sha256(user["password"].encode()).hexdigest()
        cursor.execute("""
        INSERT INTO players (username, password, user_id)
        VALUES (?, ?, ?)
        """, (user["username"], player_password, user_id))

        created_users.append({"id": user_id, **user})
        print(f"已创建用户: {user['username']}, 角色: {user['role']}, 密码: {user['password']}")

    conn.commit()
    return created_users

def create_vehicles_for_users(cursor, conn, users):
    """为每个用户创建车辆"""
    print("\n3. 为用户创建车辆")

    # 电动车品牌列表
    brands = ["雅迈哈", "爱玛", "小牛", "立马", "七匹狼", "新日", "台铁", "维利", "爱尚卡", "绿源"]

    # 车辆类型
    types = ["电动车", "电动摩托车", "电动自行车", "电动三轮车"]

    # 颜色
    colors = ["红色", "蓝色", "黑色", "白色", "灰色", "绿色", "黄色", "紫色", "橙色", "银色"]

    # 获取当前时间
    now = datetime.now()

    # 为每个用户创建1-3辆车
    for user in users:
        # 获取最大车辆ID
        cursor.execute("SELECT MAX(b_id) FROM bikes")
        max_id = cursor.fetchone()[0] or 0

        # 为用户创建1-3辆车
        num_bikes = random.randint(1, 3)

        for i in range(num_bikes):
            bike_id = max_id + i + 1
            brand = random.choice(brands)
            bike_type = random.choice(types)
            color = random.choice(colors)

            # 生成车牌号
            bike_number = f"{user['username'].upper()}-{now.strftime('%Y%m%d')}{random.randint(100, 999)}"

            # 创建车辆记录
            cursor.execute("""
            INSERT INTO bikes (b_id, b_num, brand, color, b_type, status, belong_to, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (bike_id, bike_number, brand, color, bike_type, "可用", user["id"], now, now))

            print(f"为用户 {user['username']} 创建车辆: {bike_number}, 品牌: {brand}, 类型: {bike_type}, 颜色: {color}")

    conn.commit()

def print_all_users_and_vehicles(cursor):
    """输出所有用户和车辆信息"""
    print("\n4. 系统中所有用户和车辆信息")

    # 获取所有用户
    cursor.execute("""
    SELECT u.u_id, u.u_name, u.u_role, u.u_belong, u.u_phone, p.password
    FROM users u
    LEFT JOIN players p ON u.u_id = p.user_id
    """)
    users = cursor.fetchall()

    print("\n=== 用户信息 ===")
    print("ID | 用户名 | 角色 | 所属 | 电话 | 密码(哈希)")
    print("-" * 80)

    for user in users:
        user_id, username, role, belong, phone, password = user
        print(f"{user_id} | {username} | {role} | {belong} | {phone} | {password[:10]}...")

    # 获取所有车辆
    cursor.execute("""
    SELECT b.b_id, b.b_num, b.brand, b.color, b.b_type, b.status, b.belong_to, u.u_name
    FROM bikes b
    LEFT JOIN users u ON b.belong_to = u.u_id
    """)
    bikes = cursor.fetchall()

    print("\n=== 车辆信息 ===")
    print("ID | 车牌号 | 品牌 | 颜色 | 类型 | 状态 | 所属用户ID | 所属用户名")
    print("-" * 100)

    for bike in bikes:
        bike_id, bike_number, brand, color, bike_type, status, belong_to, username = bike
        print(f"{bike_id} | {bike_number} | {brand} | {color} | {bike_type} | {status} | {belong_to} | {username}")

    # 输出用户登录信息
    print("\n=== 用户登录信息 ===")
    print("用户名 | 密码 | 角色")
    print("-" * 40)

    # 预设密码映射
    password_map = {
        "admin": "admin123",
        "jyj": "123456"  # 假设jyj用户密码
    }

    # 添加测试用户的密码
    for user in users:
        username = user[1]
        role = user[2]

        if username in password_map:
            password = password_map[username]
        elif "security" in username:
            password = username.replace("security", "security") + "123"
        elif "teacher" in username:
            password = username.replace("teacher", "teacher") + "123"
        elif "student" in username:
            password = username.replace("student", "student") + "123"
        elif "admin" in username and username != "admin":
            password = username.replace("admin", "admin") + "456"
        else:
            password = "未知"

        print(f"{username} | {password} | {role}")

if __name__ == "__main__":
    main()
