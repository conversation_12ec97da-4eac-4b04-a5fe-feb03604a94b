#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复充电车位编号格式
将所有充电车位编号统一为 C-{lot_id}-{i} 格式
"""

import sqlite3
import os
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_charging_spaces():
    """修复充电车位编号格式"""
    # 连接数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    try:
        # 查询所有停车场
        cursor.execute("SELECT id, name FROM parking_lots")
        parking_lots = cursor.fetchall()
        
        logger.info(f"找到 {len(parking_lots)} 个停车场")
        
        total_updated = 0
        
        # 遍历每个停车场
        for lot_id, lot_name in parking_lots:
            logger.info(f"处理停车场 {lot_name} (ID: {lot_id})")
            
            # 查询该停车场的所有充电车位
            cursor.execute("SELECT id, space_number FROM parking_spaces WHERE parking_lot_id = ? AND type = 3", (lot_id,))
            charging_spaces = cursor.fetchall()
            
            logger.info(f"  找到 {len(charging_spaces)} 个充电车位")
            
            # 如果没有充电车位，跳过
            if not charging_spaces:
                continue
            
            # 按ID排序充电车位
            charging_spaces.sort(key=lambda s: s[0])
            
            # 遍历每个充电车位
            for i, (space_id, space_number) in enumerate(charging_spaces, 1):
                # 生成新的车位编号
                new_space_number = f"C-{lot_id}-{i}"
                
                # 如果车位编号已经是正确格式，跳过
                if space_number == new_space_number:
                    logger.info(f"    车位 {space_id} 编号已经是正确格式: {space_number}")
                    continue
                
                # 更新车位编号
                logger.info(f"    更新车位 {space_id} 编号: {space_number} -> {new_space_number}")
                cursor.execute("UPDATE parking_spaces SET space_number = ? WHERE id = ?", (new_space_number, space_id))
                total_updated += 1
        
        # 提交更改
        conn.commit()
        logger.info(f"成功更新 {total_updated} 个充电车位编号")
        
    except Exception as e:
        conn.rollback()
        logger.error(f"修复充电车位编号格式失败: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    # 切换到包含数据库的目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    fix_charging_spaces()
