#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Flask应用
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return 'Hello World! Flask is working!'

@app.route('/test')
def test():
    return {'message': 'API is working', 'status': 'success'}

if __name__ == '__main__':
    print("启动测试Flask服务器在 http://127.0.0.1:5004")
    app.run(host='127.0.0.1', port=5004, debug=False)
