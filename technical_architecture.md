# 校园电动车管理系统技术架构图

以下是校园电动车管理系统的技术架构图，展示了系统的前端、后端和数据库结构。

## 系统整体架构

```mermaid
flowchart LR
    %% 主要层次定义
    subgraph FE["前端 (Vue.js + Element UI)"]
        direction TB
        UI[用户界面层] --> Router[路由层] --> Store[状态管理层] --> Service[服务调用层]
    end

    subgraph BE["后端 (Flask)"]
        direction TB
        API[API层] --> Logic[业务逻辑层] --> ORM[数据访问层]
    end

    DB[(SQLite数据库)]

    %% 连接关系
    Service -- HTTP请求 --> API
    ORM -- SQL --> DB

    %% WebSocket连接
    Service -- WebSocket --> WS[WebSocket服务] --> Logic
```

## 前端架构详情

```mermaid
flowchart LR
    %% 前端主要组件
    subgraph FE["前端架构"]
        direction TB
        VueApp[Vue应用]

        subgraph Core["核心组件"]
            direction LR
            Router[Vue Router] --- Vuex[Vuex] --- ElementUI[Element UI]
        end

        subgraph Layout["布局组件"]
            direction LR
            Container[布局容器] --- Sidebar[侧边栏] --- Navbar[导航栏] --- AppMain[主内容区]
        end

        subgraph Modules["功能模块"]
            direction LR
            UserModule[用户] --- BikeModule[车辆] --- ParkingModule[停车] --- ChargingModule[充电] --- ViolationModule[违规]
        end

        subgraph Services["服务层"]
            direction LR
            APIService[API服务] --- AuthService[认证] --- SocketService[WebSocket] --- Utils[工具]
        end
    end

    %% 连接关系
    VueApp --> Core
    VueApp --> Layout
    AppMain --> Modules
    Modules --> Services
```

## 后端架构详情

```mermaid
flowchart LR
    %% 后端主要组件
    subgraph BE["后端架构"]
        direction TB
        Flask[Flask应用]

        subgraph Core["核心组件"]
            direction LR
            SQLAlchemy[SQLAlchemy] --- JWT[JWT] --- CORS[CORS] --- SocketIO[Socket.IO]
        end

        subgraph Blueprints["蓝图模块"]
            direction TB
            UsersBP[用户] --- BikesBP[车辆]
            ParkingLotsBP[停车场] --- ParkingRecordsBP[停车记录]
            ChargingBP[充电] --- ViolationsBP[违规]
            AnnouncementsBP[公告] --- WebSocketBP[WebSocket]
        end

        subgraph Models["模型层"]
            direction TB
            UsersModel[用户] --- BikesModel[车辆]
            ParkingModel[停车] --- ChargingModel[充电]
            ViolationModel[违规] --- AnnouncementModel[公告]
        end

        subgraph Utils["工具服务"]
            direction LR
            ResponseUtil[响应] --- AuthUtil[认证] --- LogUtil[日志] --- FileUtil[文件]
        end
    end

    %% 连接关系
    Flask --> Core
    Flask --> Blueprints
    Blueprints --> Models
    Blueprints --> Utils
```

## 数据库模型关系

```mermaid
erDiagram
    Users ||--o{ Bikes : "拥有"
    Users ||--o{ ParkingRecord : "创建"
    Users ||--o{ ChargingRecord : "创建"
    Users ||--o{ ViolationRecord : "接收"
    Bikes ||--o{ ParkingRecord : "参与"
    Bikes ||--o{ ChargingRecord : "参与"
    Bikes ||--o{ ViolationRecord : "涉及"
    ParkingLot ||--o{ ParkingSpace : "包含"
    ParkingLot ||--o{ ParkingRecord : "关联"
    ParkingSpace ||--o{ ParkingRecord : "关联"
    ParkingSpace ||--o{ ChargingRecord : "关联"
    ParkingRecord ||--o{ ChargingRecord : "关联"
    ViolationRecord ||--o{ Appeal : "关联"
    ViolationRecord ||--o{ Evidence : "包含"
    ViolationType ||--o{ ViolationRecord : "分类"
```

## 数据流向图

```mermaid
flowchart LR
    %% 核心组件
    User((用户)) <--> FE[前端应用]
    FE <--> BE[后端API]
    BE <--> DB[(数据库)]

    %% 主要流程
    subgraph Auth["认证流程"]
        direction LR
        A1[登录请求] --> A2[验证凭据] --> A3[返回JWT] --> A4[存储令牌]
    end

    subgraph Parking["停车流程"]
        direction LR
        P1[选择停车场] --> P2[获取车位] --> P3[选择车位] --> P4[创建记录] --> P5[更新状态]
    end

    %% 连接关系
    User --> Auth
    User --> Parking
    Auth --> FE
    Parking --> BE
```

## 技术栈总结

### 前端技术栈
- **框架**: Vue.js 2.x
- **UI组件库**: Element UI
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **实时通信**: Socket.IO Client
- **构建工具**: Vue CLI

### 后端技术栈
- **框架**: Flask
- **ORM**: SQLAlchemy
- **认证**: JWT (JSON Web Token)
- **跨域**: CORS
- **实时通信**: Socket.IO
- **API风格**: RESTful

### 数据库
- **数据库系统**: SQLite
- **主要数据模型**:
  - 用户 (Users)
  - 车辆 (Bikes)
  - 停车场 (ParkingLot)
  - 车位 (ParkingSpace)
  - 停车记录 (ParkingRecord)
  - 充电记录 (ChargingRecord)
  - 违规记录 (ViolationRecord)
  - 申诉 (Appeal)
