#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用Waitress启动Flask应用的脚本
适用于Windows生产环境
"""

# 在导入任何其他模块之前执行eventlet的monkey patching
import eventlet
eventlet.monkey_patch()

import os
import sys
from waitress import serve
from wsgi import app

# 设置环境变量
os.environ['FLASK_APP'] = 'wsgi.py'
os.environ['FLASK_ENV'] = 'production'

if __name__ == "__main__":
    print("正在使用Waitress启动服务器...")
    print("服务器地址: http://127.0.0.1:5000")
    
    # 启动Waitress服务器
    serve(app, host='0.0.0.0', port=5000, threads=4)
