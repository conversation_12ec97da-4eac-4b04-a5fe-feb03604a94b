#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场数据一致性修复脚本
用于修复停车场的车位利用率信息与数据库不一致的问题
"""

import os
import sys
import logging
from datetime import datetime

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('fix_parking_stats')

# 添加当前目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入Flask应用
from app import create_app, db
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.parking_records.models import ParkingRecord
from sqlalchemy.exc import SQLAlchemyError

def fix_parking_lot_stats():
    """修复所有停车场的统计数据"""
    logger.info("开始修复停车场统计数据...")
    
    try:
        # 获取所有停车场
        parking_lots = ParkingLot.query.all()
        fixed_count = 0
        
        logger.info(f"找到 {len(parking_lots)} 个停车场")
        
        for lot in parking_lots:
            # 查询实际占用车位数
            actual_occupied = ParkingSpace.query.filter_by(
                parking_lot_id=lot.id,
                status=1  # 已占用状态
            ).count()
            
            # 查询实际总车位数
            actual_total = ParkingSpace.query.filter_by(
                parking_lot_id=lot.id
            ).count()
            
            # 检查是否需要修复
            needs_fix = False
            
            if lot.occupied_spaces != actual_occupied:
                logger.warning(f"停车场 {lot.name} (ID: {lot.id}) 占用数不一致: 记录值={lot.occupied_spaces}, 实际值={actual_occupied}")
                lot.occupied_spaces = actual_occupied
                needs_fix = True
                
            if lot.total_spaces != actual_total:
                logger.warning(f"停车场 {lot.name} (ID: {lot.id}) 总车位数不一致: 记录值={lot.total_spaces}, 实际值={actual_total}")
                lot.total_spaces = actual_total
                needs_fix = True
                
            if needs_fix:
                db.session.add(lot)
                fixed_count += 1
                logger.info(f"已修复停车场 {lot.name} (ID: {lot.id}) 的统计数据")
        
        # 提交所有修复
        if fixed_count > 0:
            db.session.commit()
            logger.info(f"成功修复 {fixed_count} 个停车场的数据一致性问题")
        else:
            logger.info("所有停车场数据一致性检查通过，无需修复")
            
        return fixed_count
        
    except SQLAlchemyError as e:
        db.session.rollback()
        logger.error(f"停车场数据一致性修复失败: {str(e)}")
        return -1

def check_parking_records_consistency():
    """检查停车记录与车位状态的一致性"""
    logger.info("开始检查停车记录与车位状态的一致性...")
    
    try:
        # 查找所有进行中的停车记录
        active_records = ParkingRecord.query.filter_by(status=0).all()
        logger.info(f"找到 {len(active_records)} 条进行中的停车记录")
        
        fixed_count = 0
        
        for record in active_records:
            # 获取对应的车位
            space = ParkingSpace.query.get(record.parking_space_id)
            
            if not space:
                logger.warning(f"停车记录 ID: {record.id} 关联的车位 ID: {record.parking_space_id} 不存在")
                continue
                
            # 检查车位状态是否与记录一致
            if space.status != 1 or space.current_vehicle_id != record.vehicle_id:
                logger.warning(f"车位状态与停车记录不一致: 记录ID={record.id}, 车位ID={space.id}, 车位状态={space.status}, 车位车辆ID={space.current_vehicle_id}, 记录车辆ID={record.vehicle_id}")
                
                # 修复车位状态
                space.status = 1  # 已占用
                space.current_vehicle_id = record.vehicle_id
                db.session.add(space)
                fixed_count += 1
                
        if fixed_count > 0:
            db.session.commit()
            logger.info(f"成功修复 {fixed_count} 个车位状态")
            
            # 更新所有停车场的占用数
            for lot in ParkingLot.query.all():
                lot.update_occupied_spaces()
                
            db.session.commit()
            logger.info("已更新所有停车场的占用数")
        else:
            logger.info("所有车位状态与停车记录一致，无需修复")
            
        return fixed_count
        
    except SQLAlchemyError as e:
        db.session.rollback()
        logger.error(f"停车记录一致性检查失败: {str(e)}")
        return -1

if __name__ == "__main__":
    # 创建应用上下文
    app = create_app()
    
    with app.app_context():
        logger.info("=== 开始执行停车场数据修复脚本 ===")
        
        # 修复停车场统计数据
        fixed_lots = fix_parking_lot_stats()
        if fixed_lots >= 0:
            logger.info(f"停车场统计数据修复完成，修复了 {fixed_lots} 个停车场")
        else:
            logger.error("停车场统计数据修复失败")
            
        # 检查停车记录与车位状态的一致性
        fixed_spaces = check_parking_records_consistency()
        if fixed_spaces >= 0:
            logger.info(f"车位状态一致性检查完成，修复了 {fixed_spaces} 个车位")
        else:
            logger.error("车位状态一致性检查失败")
            
        logger.info("=== 停车场数据修复脚本执行完成 ===")
