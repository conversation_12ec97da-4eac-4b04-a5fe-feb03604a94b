```mermaid
flowchart TD
    %% 简化的车辆信息管理流程图
    Start([开始]) --> AdminLogin[管理员登录]
    AdminLogin --> AccessVehicleMgmt[访问车辆管理页面]
    
    %% 主要功能分支
    AccessVehicleMgmt --> ViewVehicleList[查看车辆列表]
    AccessVehicleMgmt --> ManageVehicleTypes[管理车辆类型]
    
    %% 车辆查询功能
    ViewVehicleList --> SearchVehicles[搜索/筛选车辆]
    
    %% 车辆详情与编辑
    ViewVehicleList --> ViewVehicleDetail[查看车辆详情]
    ViewVehicleDetail --> ViewRelatedInfo[查看关联信息]
    ViewVehicleDetail --> EditVehicle[编辑车辆信息]
    EditVehicle --> SaveVehicle[保存车辆信息]
    
    %% 车辆所有权管理
    ViewVehicleDetail --> ManageOwnership[管理车辆所有权]
    ManageOwnership --> UpdateOwnership[更新车辆所有权]
    
    %% 注册新车辆
    ViewVehicleList --> RegisterNewVehicle[注册新车辆]
    RegisterNewVehicle --> SelectOwner[选择所有者]
    SelectOwner --> SaveNewVehicle[保存新车辆]
    
    %% 删除车辆
    ViewVehicleDetail --> DeleteVehicle[删除车辆]
    DeleteVehicle --> ConfirmDeletion{确认删除}
    ConfirmDeletion -->|确认| RemoveVehicle[移除车辆]
    ConfirmDeletion -->|取消| ViewVehicleDetail
    
    %% 批量操作
    ViewVehicleList --> BatchOperations[批量操作]
    BatchOperations --> BatchDelete[批量删除]
    BatchOperations --> BatchUpdate[批量更新]
    
    %% 数据导出
    ViewVehicleList --> ExportVehicleData[导出车辆数据]
    
    %% 车辆类型管理
    ManageVehicleTypes --> AddType[添加类型]
    ManageVehicleTypes --> EditType[编辑类型]
    ManageVehicleTypes --> DeleteType[删除类型]
    
    %% 样式定义
    classDef start fill:#f9d5e5,stroke:#333,stroke-width:2px
    classDef process fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef decision fill:#ffffcc,stroke:#333,stroke-width:1px
    
    class Start start
    class AdminLogin,AccessVehicleMgmt,ViewVehicleList,ManageVehicleTypes,SearchVehicles,ViewVehicleDetail,ViewRelatedInfo,EditVehicle,SaveVehicle,ManageOwnership,UpdateOwnership,RegisterNewVehicle,SelectOwner,SaveNewVehicle,RemoveVehicle,BatchOperations,BatchDelete,BatchUpdate,ExportVehicleData,AddType,EditType,DeleteType process
    class ConfirmDeletion decision
```
