```mermaid
flowchart TD
    %% 用户角色定义
    User([普通用户])
    Security([保安])
    Admin([管理员])
    
    %% 功能模块
    Auth[认证与权限管理]
    PersonalCenter[个人中心]
    VehicleMgmt[车辆管理]
    ParkingCenter[停车中心]
    ChargingCenter[充电中心]
    ViolationCenter[违规中心]
    AnnouncementCenter[公告管理]
    AdminConsole[管理控制台]
    
    %% 普通用户权限
    User --> Auth
    User --> PersonalCenter
    User --> VehicleMgmt
    User --> ParkingCenter
    User --> ChargingCenter
    User --> ViewViolations[查看个人违规]
    User --> SubmitAppeal[提交申诉]
    User --> ViewAnnouncements[查看公告]
    
    %% 保安权限
    Security --> Auth
    Security --> PersonalCenter
    Security --> VehicleMgmt
    Security --> ParkingCenter
    Security --> ChargingCenter
    Security --> ViewViolations
    Security --> SubmitAppeal
    Security --> ViewAnnouncements
    Security --> RecordViolation[记录违规]
    Security --> UploadEvidence[上传违规证据]
    
    %% 管理员权限
    Admin --> Auth
    Admin --> PersonalCenter
    Admin --> VehicleMgmt
    Admin --> ParkingCenter
    Admin --> ChargingCenter
    Admin --> ViewViolations
    Admin --> SubmitAppeal
    Admin --> ViewAnnouncements
    Admin --> AdminConsole
    
    %% 管理控制台功能
    AdminConsole --> UserAdmin[用户管理]
    AdminConsole --> VehicleAdmin[车辆管理]
    AdminConsole --> ParkingAdmin[停车场管理]
    AdminConsole --> ChargingAdmin[充电设施管理]
    AdminConsole --> ViolationAdmin[违规管理]
    AdminConsole --> AppealAdmin[申诉处理]
    AdminConsole --> AnnouncementAdmin[公告发布]
    AdminConsole --> SystemStats[系统统计]
    
    %% 个人中心功能
    PersonalCenter --> ProfileInfo[个人信息]
    PersonalCenter --> MyVehicles[我的车辆]
    PersonalCenter --> MyParking[我的停车]
    PersonalCenter --> MyCharging[我的充电]
    PersonalCenter --> MyViolations[我的违规]
    
    %% 样式定义
    classDef userAccess fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef securityAccess fill:#ffffcc,stroke:#333,stroke-width:1px
    classDef adminAccess fill:#ffcccc,stroke:#333,stroke-width:1px
    classDef commonAccess fill:#e6e6fa,stroke:#333,stroke-width:1px
    
    class Auth,PersonalCenter,VehicleMgmt,ParkingCenter,ChargingCenter,ViewViolations,SubmitAppeal,ViewAnnouncements,ProfileInfo,MyVehicles,MyParking,MyCharging,MyViolations commonAccess
    class RecordViolation,UploadEvidence securityAccess
    class AdminConsole,UserAdmin,VehicleAdmin,ParkingAdmin,ChargingAdmin,ViolationAdmin,AppealAdmin,AnnouncementAdmin,SystemStats adminAccess
```
