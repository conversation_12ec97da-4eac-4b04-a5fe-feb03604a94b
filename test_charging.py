import os
import sys
import sqlite3
import json
from datetime import datetime, timedelta

# 打印数据库文件路径
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sys.db')
print(f"数据库文件路径: {db_path}")

# 检查是否存在app模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app'))

# 从app模块导入必要的类
from app.charging.models import ChargingRecord, ChargingReservation
# 移除对不存在的ChargingPriceStrategy的导入 