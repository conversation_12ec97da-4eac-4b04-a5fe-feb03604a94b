#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的后端启动脚本
"""

import os
import sys

# 切换到api目录
api_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'api')
os.chdir(api_dir)
sys.path.insert(0, api_dir)

print(f"当前工作目录: {os.getcwd()}")
print("正在启动后端服务...")

try:
    # 导入Flask应用
    from flask_app import app, socketio
    print("Flask应用导入成功")

    # 启动服务器
    print("启动SocketIO服务器在 http://0.0.0.0:5000")
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)

except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    traceback.print_exc()
