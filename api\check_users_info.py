import sqlite3

# 连接数据库
conn = sqlite3.connect('sys.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# 查询用户表结构
print('用户表结构:')
cursor.execute('PRAGMA table_info(users)')
columns = cursor.fetchall()
for column in columns:
    print(f"列名: {column['name']}, 类型: {column['type']}, 是否可空: {column['notnull']}")

# 查询用户信息
print('\n用户信息:')
cursor.execute('SELECT * FROM users')
users = cursor.fetchall()
for user in users:
    user_dict = dict(user)
    print(f"用户ID: {user_dict.get('u_id')}")
    print(f"用户名: {user_dict.get('u_name')}")
    print(f"密码: {user_dict.get('u_pwd')}")
    print(f"盐值: {user_dict.get('salt')}")
    print(f"角色: {user_dict.get('u_role')}")
    print(f"所属: {user_dict.get('u_belong')}")
    print(f"电话: {user_dict.get('u_phone')}")
    print(f"邮箱: {user_dict.get('u_email')}")
    print(f"头像: {user_dict.get('avatar')}")
    print(f"创建时间: {user_dict.get('created_at')}")
    print(f"更新时间: {user_dict.get('updated_at')}")
    print(f"版本: {user_dict.get('version')}")
    print('-' * 50)

# 关闭连接
conn.close()

print("\n注意: 密码通常是加密存储的，上面显示的是加密后的密码哈希值，而不是明文密码。")
