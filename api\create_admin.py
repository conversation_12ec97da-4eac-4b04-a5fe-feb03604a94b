#!/usr/bin/env python

import os
import sys
import hashlib
import uuid

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app import create_app, db
from app.users.models import Users

# 创建Flask应用上下文
app = create_app()

def create_admin_user():
    """
    创建管理员用户
    用户名: admin
    密码: 111111
    角色: admin
    """
    print("正在创建管理员用户...")
    
    # 检查是否已存在
    existing_user = Users.query.filter_by(u_name='admin').first()
    if existing_user:
        print(f"管理员用户已存在 (ID: {existing_user.u_id})，更新密码和角色")
        # 更新角色确保是admin
        existing_user.u_role = 'admin'
        # 更新密码
        salt = str(uuid.uuid4())
        existing_user.salt = salt
        existing_user.u_pwd = hashlib.sha256(f"111111{salt}".encode()).hexdigest()
        db.session.commit()
        print("管理员密码已更新")
        return existing_user
        
    # 创建新用户
    salt = str(uuid.uuid4())
    password = "111111"
    hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
    
    new_user = Users(
        u_name='admin',
        u_pwd=hashed_password,
        u_role='admin',
        u_belong='系统管理',
        u_phone='13800000000',
        u_email='<EMAIL>'
    )
    # 手动设置盐值
    new_user.salt = salt
    
    # 添加到数据库
    db.session.add(new_user)
    db.session.commit()
    
    print(f"管理员用户创建成功 (ID: {new_user.u_id})")
    return new_user

def create_test_user():
    """
    创建测试普通用户
    用户名: user1
    密码: 111111
    角色: user
    """
    print("\n正在创建测试用户...")
    
    # 检查是否已存在
    existing_user = Users.query.filter_by(u_name='user1').first()
    if existing_user:
        print(f"测试用户已存在 (ID: {existing_user.u_id})，更新密码")
        # 更新密码
        salt = str(uuid.uuid4())
        existing_user.salt = salt
        existing_user.u_pwd = hashlib.sha256(f"111111{salt}".encode()).hexdigest()
        db.session.commit()
        print("测试用户密码已更新")
        return existing_user
        
    # 创建新用户
    salt = str(uuid.uuid4())
    password = "111111"
    hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
    
    new_user = Users(
        u_name='user1',
        u_pwd=hashed_password,
        u_role='user',
        u_belong='计算机学院',
        u_phone='13800000001',
        u_email='<EMAIL>'
    )
    # 手动设置盐值
    new_user.salt = salt
    
    # 添加到数据库
    db.session.add(new_user)
    db.session.commit()
    
    print(f"测试用户创建成功 (ID: {new_user.u_id})")
    return new_user

def create_security_user():
    """
    创建安保人员用户
    用户名: security1
    密码: 111111
    角色: security
    """
    print("\n正在创建安保人员用户...")
    
    # 检查是否已存在
    existing_user = Users.query.filter_by(u_name='security1').first()
    if existing_user:
        print(f"安保人员用户已存在 (ID: {existing_user.u_id})，更新密码和角色")
        # 设置角色
        existing_user.u_role = 'security'
        # 更新密码
        salt = str(uuid.uuid4())
        existing_user.salt = salt
        existing_user.u_pwd = hashlib.sha256(f"111111{salt}".encode()).hexdigest()
        db.session.commit()
        print("安保人员密码已更新")
        return existing_user
        
    # 创建新用户
    salt = str(uuid.uuid4())
    password = "111111"
    hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
    
    new_user = Users(
        u_name='security1',
        u_pwd=hashed_password,
        u_role='security',
        u_belong='保卫处',
        u_phone='13800000002',
        u_email='<EMAIL>'
    )
    # 手动设置盐值
    new_user.salt = salt
    
    # 添加到数据库
    db.session.add(new_user)
    db.session.commit()
    
    print(f"安保人员用户创建成功 (ID: {new_user.u_id})")
    return new_user

if __name__ == "__main__":
    print("创建测试用户...")
    
    with app.app_context():
        admin = create_admin_user()
        test_user = create_test_user()
        security = create_security_user()
        
        # 查询并显示所有用户
        all_users = Users.query.all()
        
        print("\n创建完成! 可用的账号:")
        print(f"1. 管理员: admin / 111111 (ID: {admin.u_id})")
        print(f"2. 普通用户: user1 / 111111 (ID: {test_user.u_id})")
        print(f"3. 安保人员: security1 / 111111 (ID: {security.u_id})")
        
        print(f"\n数据库中当前存在 {len(all_users)} 个用户:")
        for user in all_users:
            print(f"- {user.u_id}: {user.u_name} ({user.u_role})") 