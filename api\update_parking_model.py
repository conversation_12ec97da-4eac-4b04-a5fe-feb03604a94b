import sqlite3
from datetime import datetime

# 连接到数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 添加校区和区域字段到停车场表
try:
    # 检查字段是否已存在
    cursor.execute("PRAGMA table_info(parking_lots)")
    columns = [col[1] for col in cursor.fetchall()]

    # 添加校区字段
    if 'campus' not in columns:
        cursor.execute("ALTER TABLE parking_lots ADD COLUMN campus VARCHAR(50)")
        print("已添加校区字段")

    # 添加区域字段
    if 'area' not in columns:
        cursor.execute("ALTER TABLE parking_lots ADD COLUMN area VARCHAR(50)")
        print("已添加区域字段")



    # 更新现有停车场数据，添加校区和区域信息
    cursor.execute("SELECT id, name FROM parking_lots")
    parking_lots = cursor.fetchall()

    for lot_id, name in parking_lots:
        # 根据名称推断校区和区域
        campus = "主校区"  # 默认校区
        area = "未分类"    # 默认区域

        if "东" in name:
            area = "东区"
        elif "西" in name:
            area = "西区"
        elif "南" in name:
            area = "南区"
        elif "北" in name:
            area = "北区"

        # 更新停车场信息
        cursor.execute(
            "UPDATE parking_lots SET campus = ?, area = ?, updated_at = ? WHERE id = ?",
            (campus, area, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), lot_id)
        )

    # 提交更改
    conn.commit()
    print(f"已更新 {len(parking_lots)} 个停车场的校区和区域信息")

    # 添加示例校园停车场数据
    campus_parking_lots = [
        ("主校区", "教学楼区", "教学楼停车场", "教学楼北侧", 30, 0, 116.3, 39.9, "7:00-23:00", 1),
        ("主校区", "图书馆区", "图书馆停车场", "图书馆东侧", 25, 0, 116.31, 39.91, "7:00-23:00", 1),
        ("主校区", "宿舍区", "宿舍区停车场", "学生宿舍区中心", 50, 0, 116.32, 39.92, "全天开放", 1),
        ("主校区", "食堂区", "食堂停车场", "中心食堂旁", 20, 0, 116.33, 39.93, "6:00-22:00", 1),
        ("东校区", "体育馆区", "体育馆停车场", "体育馆南侧", 40, 0, 116.34, 39.94, "8:00-22:00", 1)
    ]

    # 检查是否已存在这些停车场
    for campus, area, name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status in campus_parking_lots:
        cursor.execute("SELECT id FROM parking_lots WHERE name = ?", (name,))
        if not cursor.fetchone():
            cursor.execute(
                """
                INSERT INTO parking_lots
                (name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, created_at, updated_at, campus, area)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    campus, area
                )
            )

    # 提交更改
    conn.commit()
    print("已添加示例校园停车场数据")

    # 为新添加的停车场创建车位
    cursor.execute("SELECT id, name, total_spaces FROM parking_lots WHERE id NOT IN (SELECT DISTINCT parking_lot_id FROM parking_spaces)")
    new_lots = cursor.fetchall()

    for lot_id, lot_name, total_spaces in new_lots:
        spaces_to_add = []
        for i in range(1, total_spaces + 1):
            # 确定车位类型：1=普通车位，2=残疾人专用车位，3=大型车位
            if i <= 5:
                space_type = 2  # 前5个为残疾人专用车位
            elif i <= 5 + total_spaces * 0.2:
                space_type = 3  # 约20%为大型车位
            else:
                space_type = 1  # 其余为普通车位

            # 所有车位初始状态为空闲(0)
            status = 0

            # 创建车位记录
            space = (
                lot_id,  # parking_lot_id
                f"{lot_name[:2]}{i:03d}",  # space_number
                space_type,  # type
                status,  # status
                None,  # current_vehicle_id
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # created_at
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')   # updated_at
            )
            spaces_to_add.append(space)

        # 批量插入车位
        cursor.executemany(
            """
            INSERT INTO parking_spaces
            (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
            spaces_to_add
        )

        print(f"已为停车场 '{lot_name}' (ID: {lot_id}) 创建 {len(spaces_to_add)} 个车位")

    # 提交更改
    conn.commit()

except Exception as e:
    conn.rollback()
    print(f"错误: {e}")
finally:
    # 关闭连接
    conn.close()
