import requests
import json
import sys

def test_parking_records_api():
    """测试停车记录API的实际返回格式"""
    print("测试停车记录API的实际返回格式...")
    
    # 获取 admin 用户的 token
    login_url = "http://127.0.0.1:5000/api/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        login_json = login_response.json()
        
        if login_response.status_code == 200 and login_json.get("access_token"):
            token = login_json.get("access_token")
            print(f"获取 token 成功: {token[:10]}...")
            
            # 测试获取停车记录API
            headers = {
                "Authorization": f"Bearer {token}"
            }
            
            # 测试不同的API端点
            test_endpoints = [
                {
                    "name": "获取所有停车记录",
                    "url": "http://127.0.0.1:5000/api/parking-records",
                    "params": {"status": 1}  # 已完成的记录
                },
                {
                    "name": "获取用户停车记录",
                    "url": "http://127.0.0.1:5000/api/parking-records/user",
                    "params": {"status": 1, "user_id": 3}  # admin用户ID为3
                },
                {
                    "name": "获取进行中的停车记录",
                    "url": "http://127.0.0.1:5000/api/parking-records",
                    "params": {"status": 0}  # 进行中的记录
                }
            ]
            
            for endpoint in test_endpoints:
                print(f"\n测试 {endpoint['name']} API...")
                try:
                    response = requests.get(endpoint["url"], headers=headers, params=endpoint["params"])
                    
                    print(f"状态码: {response.status_code}")
                    print(f"URL: {response.url}")
                    
                    try:
                        response_json = response.json()
                        print(f"响应类型: {type(response_json)}")
                        print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)[:1000]}...")
                        
                        # 分析响应结构
                        analyze_response_structure(response_json)
                        
                    except json.JSONDecodeError:
                        print(f"响应不是有效的JSON: {response.text[:500]}...")
                except Exception as e:
                    print(f"请求失败: {e}")
        else:
            print(f"登录失败: {login_json}")
    except Exception as e:
        print(f"测试过程中出错: {e}")

def analyze_response_structure(response):
    """分析响应结构"""
    if isinstance(response, dict):
        print("响应是一个字典")
        
        # 检查常见的响应字段
        if "code" in response:
            print(f"- 包含 'code' 字段: {response['code']}")
        
        if "data" in response:
            print("- 包含 'data' 字段")
            data = response["data"]
            
            if isinstance(data, dict):
                print("  - data 是一个字典")
                for key in data:
                    print(f"    - 包含键: '{key}'")
                    
                if "items" in data:
                    items = data["items"]
                    print(f"    - items 是一个 {type(items).__name__}")
                    if isinstance(items, list):
                        print(f"    - items 包含 {len(items)} 个元素")
                        if items:
                            print(f"    - 第一个元素类型: {type(items[0]).__name__}")
                            if isinstance(items[0], dict):
                                print(f"    - 第一个元素键: {', '.join(items[0].keys())}")
                
                if "records" in data:
                    records = data["records"]
                    print(f"    - records 是一个 {type(records).__name__}")
                    if isinstance(records, list):
                        print(f"    - records 包含 {len(records)} 个元素")
                        if records:
                            print(f"    - 第一个元素类型: {type(records[0]).__name__}")
                            if isinstance(records[0], dict):
                                print(f"    - 第一个元素键: {', '.join(records[0].keys())}")
            
            elif isinstance(data, list):
                print(f"  - data 是一个列表，包含 {len(data)} 个元素")
                if data:
                    print(f"  - 第一个元素类型: {type(data[0]).__name__}")
                    if isinstance(data[0], dict):
                        print(f"  - 第一个元素键: {', '.join(data[0].keys())}")
        
        if "message" in response:
            print(f"- 包含 'message' 字段: {response['message']}")
        
        if "total" in response:
            print(f"- 包含 'total' 字段: {response['total']}")
        
        # 打印所有顶级键
        print(f"- 所有顶级键: {', '.join(response.keys())}")
    
    elif isinstance(response, list):
        print(f"响应是一个列表，包含 {len(response)} 个元素")
        if response:
            print(f"- 第一个元素类型: {type(response[0]).__name__}")
            if isinstance(response[0], dict):
                print(f"- 第一个元素键: {', '.join(response[0].keys())}")
    
    else:
        print(f"响应是一个 {type(response).__name__}")

if __name__ == "__main__":
    test_parking_records_api()
