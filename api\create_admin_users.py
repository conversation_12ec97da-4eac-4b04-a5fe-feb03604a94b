#!/usr/bin/env python
import os
import sys
import hashlib
import uuid
from datetime import datetime
from passlib.hash import pbkdf2_sha256 as sha256
import sqlite3

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def create_admin_user(db_path, username, password, role, belong, phone):
    """创建管理员用户"""
    try:
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查用户是否已存在
        cursor.execute("SELECT u_id FROM users WHERE u_name = ?", (username,))
        user = cursor.fetchone()
        
        if user:
            print(f"用户 {username} 已存在，跳过创建")
            user_id = user[0]
        else:
            # 生成盐值
            salt = str(uuid.uuid4())
            
            # 生成密码哈希
            password_hash = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
            
            # 获取当前时间
            now = datetime.now().isoformat()
            
            # 插入用户
            cursor.execute(
                "INSERT INTO users (u_name, u_pwd, salt, u_role, u_belong, u_phone, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                (username, password_hash, salt, role, belong, phone, now, now)
            )
            
            # 获取用户ID
            user_id = cursor.lastrowid
            print(f"用户 {username} 创建成功，ID: {user_id}")
        
        # 检查players表中是否已存在该用户
        cursor.execute("SELECT id FROM players WHERE username = ?", (username,))
        player = cursor.fetchone()
        
        if player:
            print(f"玩家 {username} 已存在，跳过创建")
        else:
            # 使用passlib生成密码哈希
            hashed_password = sha256.hash(password)
            
            # 插入到players表
            cursor.execute(
                "INSERT INTO players (username, password, user_id) VALUES (?, ?, ?)",
                (username, hashed_password, user_id)
            )
            
            print(f"玩家 {username} 创建成功")
        
        # 提交更改
        conn.commit()
        
        # 关闭连接
        conn.close()
        
        return True
    except Exception as e:
        print(f"创建用户出错: {e}")
        return False

def main():
    # 获取数据库路径
    db_path = os.path.join(project_root, 'sys.db')
    
    # 创建管理员用户
    print("\n创建管理员用户...")
    create_admin_user(
        db_path=db_path,
        username="admin",
        password="111111",
        role="admin",
        belong="系统管理",
        phone="13800000000"
    )
    
    # 创建超级管理员用户
    print("\n创建超级管理员用户...")
    create_admin_user(
        db_path=db_path,
        username="superadmin",
        password="111111",
        role="admin",
        belong="系统管理",
        phone="13900000000"
    )
    
    # 创建普通用户
    print("\n创建普通用户...")
    create_admin_user(
        db_path=db_path,
        username="testuser",
        password="111111",
        role="user",
        belong="测试部门",
        phone="13700000000"
    )
    
    # 创建普通用户
    print("\n创建普通用户...")
    create_admin_user(
        db_path=db_path,
        username="qwe",
        password="111111",
        role="user",
        belong="测试部门",
        phone="13600000000"
    )
    
    print("\n用户创建完成")

if __name__ == "__main__":
    main()
