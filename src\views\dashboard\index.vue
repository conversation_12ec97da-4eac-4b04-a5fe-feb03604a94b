<template>
  <div class="dashboard-container">
    <div class="dashboard-text">name: {{ name }}</div>
    <div class="dashboard-text">roles: <span v-for="role in roles" :key="role">{{ role }}</span></div>
    <div class="dashboard-text">userId: {{ userId }}</div>

    <div class="action-buttons">
      <el-button type="primary" @click="testApi">测试API连接</el-button>
      <el-button type="danger" @click="clearCache">清除缓存并重新登录</el-button>
    </div>

    <div v-if="apiResult" class="api-result">
      <pre>{{ apiResult }}</pre>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import request from '@/utils/request'

export default {
  name: 'Dashboard',
  data() {
    return {
      apiResult: null
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'roles',
      'userId'
    ])
  },
  methods: {
    async testApi() {
      try {
        // 记录用户ID
        console.log('当前用户ID:', this.userId)

        this.apiResult = '正在测试API连接...'

        // 如果用户ID不存在，尝试恢复
        if (!this.userId) {
          await this.$store.dispatch('user/getInfo')
          console.log('恢复后的用户ID:', this.$store.getters.userId)
        }

        const currentUserId = this.userId || this.$store.getters.userId || 1

        const result = {
          userId: currentUserId
        }

        // 安全地测试API
        try {
          // 测试用户API - 使用当前用户ID
          const userResponse = await request({
            url: `/api/users/${currentUserId}`,
            method: 'get'
          })
          result.userInfo = userResponse.data
        } catch (userError) {
          result.userError = userError.message
          if (userError.response) {
            result.userErrorStatus = userError.response.status
            result.userErrorData = userError.response.data
          }
        }

        try {
          // 测试车辆API
          const bikeResponse = await request({
            url: '/api/bikes',
            method: 'get',
            params: { belong_to: currentUserId }
          })
          result.myBikes = bikeResponse.data
        } catch (bikeError) {
          result.bikeError = bikeError.message
          if (bikeError.response) {
            result.bikeErrorStatus = bikeError.response.status
            result.bikeErrorData = bikeError.response.data
          }
        }

        this.apiResult = JSON.stringify(result, null, 2)
      } catch (error) {
        this.apiResult = JSON.stringify({
          error: error.message,
          response: error.response ? error.response.data : null
        }, null, 2)
      }
    },

    clearCache() {
      this.$confirm('此操作将清除您的登录状态和本地缓存，需要重新登录，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除localStorage中的缓存
        localStorage.removeItem('userId')

        // 重置用户状态和令牌
        this.$store.dispatch('user/resetToken').then(() => {
          // 刷新页面，确保重置生效
          location.reload()
        })

        this.$message({
          type: 'success',
          message: '清除缓存成功，正在重定向到登录页面'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清除操作'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
.action-buttons {
  margin: 20px 0;

  .el-button {
    margin-right: 10px;
  }
}
.api-result {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: auto;

  pre {
    margin: 0;
    white-space: pre-wrap;
  }
}
</style>
