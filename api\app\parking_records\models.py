from app import db
from datetime import datetime
import math
import logging
from sqlalchemy.exc import SQLAlchemyError

# 停车记录模型
class ParkingRecord(db.Model):
    __tablename__ = 'parking_records'
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 车辆ID
    vehicle_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('bikes.b_id'), nullable=False)
    # 用户ID
    user_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 停车场ID
    parking_lot_id = db.Column(db.Integer, db.<PERSON>ey('parking_lots.id'), nullable=False)
    # 车位ID
    parking_space_id = db.Column(db.Integer, db.<PERSON>ey('parking_spaces.id'), nullable=False)
    # 入场时间
    entry_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 出场时间
    exit_time = db.Column(db.DateTime, nullable=True)
    # 记录状态：0进行中，1已完成，2异常
    status = db.Column(db.Inte<PERSON>, nullable=False, default=0)
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    # 备注
    remarks = db.Column(db.Text)

    def __init__(self, vehicle_id, user_id, parking_lot_id, parking_space_id):
        self.vehicle_id = vehicle_id
        self.user_id = user_id
        self.parking_lot_id = parking_lot_id
        self.parking_space_id = parking_space_id
        self.entry_time = datetime.now()
        self.exit_time = None
        self.status = 0
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.remarks = None

    def __repr__(self):
        return f'<ParkingRecord {self.id}: {self.vehicle_id}>'

    # 计算停车时长（小时）
    def calculate_duration(self):
        """计算停车时长（小时）"""
        end_time = self.exit_time or datetime.now()
        delta = end_time - self.entry_time
        hours = delta.total_seconds() / 3600
        return hours

    # 更新停车记录结束信息
    def end_parking(self, remarks=None):
        """结束停车记录 - 使用事务确保数据一致性"""
        try:
            # 更新停车记录状态
            self.exit_time = datetime.now()
            self.status = 1  # 已完成

            if remarks:
                self.remarks = remarks

            self.updated_at = datetime.now()

            # 更新车位状态为空闲
            from app.parkinglots.models import ParkingSpace, ParkingLot
            space = ParkingSpace.query.get(self.parking_space_id)
            if not space:
                raise ValueError(f"无法找到车位ID: {self.parking_space_id}")

            # 记录更新前的状态（用于日志）
            old_status = space.status
            old_vehicle_id = space.current_vehicle_id

            # 更新车位状态
            space.status = 0  # 空闲
            space.current_vehicle_id = None

            # 获取停车场
            parking_lot = ParkingLot.query.get(self.parking_lot_id)
            if not parking_lot:
                raise ValueError(f"无法找到停车场ID: {self.parking_lot_id}")

            # 记录更新前的停车场状态（用于日志）
            old_occupied = parking_lot.occupied_spaces

            # 提交车位状态变更
            db.session.flush()

            # 使用update_occupied_spaces方法更新停车场占用数
            # 这会根据实际车位状态重新计算占用数
            parking_lot.update_occupied_spaces()

            # 提交所有更改
            db.session.commit()

            # 详细日志记录
            logging.info(f"成功结束停车记录 ID: {self.id}, 车辆ID: {self.vehicle_id}, 车位ID: {self.parking_space_id}")
            logging.info(f"车位状态更新: {space.space_number} - 状态: {old_status} -> {space.status}, 车辆: {old_vehicle_id} -> {space.current_vehicle_id}")
            logging.info(f"停车场占用数更新: {parking_lot.name} - {old_occupied} -> {parking_lot.occupied_spaces}")

            return self

        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"结束停车记录事务失败: {str(e)}")
            raise ValueError(f"结束停车记录失败: {str(e)}")

    # 获取停车记录详情
    def get_details(self, include_relations=False):
        """获取停车记录详情"""
        status_map = {0: '进行中', 1: '已完成', 2: '异常'}

        # 计算当前时长
        duration = self.calculate_duration()

        data = {
            'id': self.id,
            'vehicle_id': self.vehicle_id,
            'user_id': self.user_id,
            'parking_lot_id': self.parking_lot_id,
            'parking_space_id': self.parking_space_id,
            'entry_time': self.entry_time.isoformat(),
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'duration': round(duration, 2),  # 停车时长（小时）
            'duration_formatted': self.format_duration(duration),  # 格式化的停车时长
            'status': self.status,
            'status_text': status_map.get(self.status, '未知'),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'remarks': self.remarks
        }

        if include_relations:
            # 获取关联的车辆信息
            from app.bikes.models import Bikes
            vehicle = Bikes.query.get(self.vehicle_id)
            if vehicle:
                data['vehicle'] = {
                    'id': vehicle.b_id,
                    'number': vehicle.b_num,
                    'brand': vehicle.brand,
                    'color': vehicle.color,
                    'type': vehicle.b_type
                }

            # 获取关联的用户信息
            from app.users.models import Users
            user = Users.query.get(self.user_id)
            if user:
                data['user'] = {
                    'id': user.u_id,
                    'name': user.u_name,
                    'phone': user.u_phone
                }

            # 获取关联的停车场信息
            from app.parkinglots.models import ParkingLot
            parking_lot = ParkingLot.query.get(self.parking_lot_id)
            if parking_lot:
                data['parking_lot'] = {
                    'id': parking_lot.id,
                    'name': parking_lot.name,
                    'address': parking_lot.address
                }

            # 获取关联的车位信息
            from app.parkinglots.models import ParkingSpace
            space = ParkingSpace.query.get(self.parking_space_id)
            if space:
                data['parking_space'] = {
                    'id': space.id,
                    'number': space.space_number,
                    'space_number': space.space_number,  # 添加与前端兼容的字段名
                    'type': space.type,
                    'type_text': {1: '普通车位', 2: '充电车位', 3: '残疾人车位', 4: 'VIP车位'}.get(space.type, '未知')
                }

                # 同时在根级添加车位编号，方便前端直接访问
                data['parking_space_number'] = space.space_number

        return data

    # 格式化停车时长
    @staticmethod
    def format_duration(hours):
        """将小时数格式化为x天y小时z分钟"""
        total_minutes = int(hours * 60)
        days = total_minutes // (24 * 60)
        remaining_minutes = total_minutes % (24 * 60)
        hours_part = remaining_minutes // 60
        minutes_part = remaining_minutes % 60

        if days > 0:
            return f"{days}天{hours_part}小时{minutes_part}分钟"
        elif hours_part > 0:
            return f"{hours_part}小时{minutes_part}分钟"
        else:
            return f"{minutes_part}分钟"

    # 创建停车记录
    def create(self):
        """创建停车记录 - 使用事务确保数据一致性"""
        try:
            # 开始事务
            db.session.add(self)

            # 更新车位状态为已占用
            from app.parkinglots.models import ParkingSpace, ParkingLot
            space = ParkingSpace.query.get(self.parking_space_id)
            if not space:
                raise ValueError(f"无法找到车位ID: {self.parking_space_id}")

            # 再次检查车位状态，防止并发问题
            if space.status != 0:
                raise ValueError(f"车位 {space.space_number} 已被占用或维护中")

            # 记录更新前的状态（用于日志）
            old_status = space.status
            old_vehicle_id = space.current_vehicle_id

            # 更新车位状态
            space.status = 1  # 已占用
            space.current_vehicle_id = self.vehicle_id

            # 获取停车场
            parking_lot = ParkingLot.query.get(self.parking_lot_id)
            if not parking_lot:
                raise ValueError(f"无法找到停车场ID: {self.parking_lot_id}")

            # 记录更新前的停车场状态（用于日志）
            old_occupied = parking_lot.occupied_spaces

            # 提交车位状态变更
            db.session.flush()

            # 使用update_occupied_spaces方法更新停车场占用数
            # 这会根据实际车位状态重新计算占用数
            parking_lot.update_occupied_spaces()

            # 提交所有更改
            db.session.commit()

            # 详细日志记录
            logging.info(f"成功创建停车记录 ID: {self.id}, 车辆ID: {self.vehicle_id}, 车位ID: {self.parking_space_id}")
            logging.info(f"车位状态更新: {space.space_number} - 状态: {old_status} -> {space.status}, 车辆: {old_vehicle_id} -> {space.current_vehicle_id}")
            logging.info(f"停车场占用数更新: {parking_lot.name} - {old_occupied} -> {parking_lot.occupied_spaces}")

            return self

        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"创建停车记录事务失败: {str(e)}")
            raise ValueError(f"创建停车记录失败: {str(e)}")