#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的Flask服务启动脚本
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print(f"当前工作目录: {os.getcwd()}")
print("正在启动Flask服务...")

try:
    # 导入Flask应用
    from flask_app import app
    print("Flask应用导入成功")
    
    # 启动服务器
    print("启动Flask服务器在 http://127.0.0.1:5002")
    app.run(host='127.0.0.1', port=5002, debug=True)
    
except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    traceback.print_exc()
