import sqlite3

def main():
    """列出所有用户和车辆信息"""
    print("系统中的所有用户和车辆信息")
    
    # 连接到数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    try:
        # 获取所有用户
        cursor.execute("""
        SELECT u.u_id, u.u_name, u.u_role, u.u_belong, u.u_phone, p.password 
        FROM users u
        LEFT JOIN players p ON u.u_id = p.user_id
        """)
        users = cursor.fetchall()
        
        print("\n=== 用户信息 ===")
        print("ID | 用户名 | 角色 | 所属 | 电话 | 密码(哈希)")
        print("-" * 80)
        
        for user in users:
            user_id, username, role, belong, phone, password = user
            print(f"{user_id} | {username} | {role} | {belong} | {phone} | {password[:10]}...")
        
        # 获取所有车辆
        cursor.execute("""
        SELECT b.b_id, b.b_num, b.brand, b.color, b.b_type, b.status, b.belong_to, u.u_name
        FROM bikes b
        LEFT JOIN users u ON b.belong_to = u.u_id
        """)
        bikes = cursor.fetchall()
        
        print("\n=== 车辆信息 ===")
        print("ID | 车牌号 | 品牌 | 颜色 | 类型 | 状态 | 所属用户ID | 所属用户名")
        print("-" * 100)
        
        for bike in bikes:
            bike_id, bike_number, brand, color, bike_type, status, belong_to, username = bike
            print(f"{bike_id} | {bike_number} | {brand} | {color} | {bike_type} | {status} | {belong_to} | {username}")
        
        # 输出用户登录信息
        print("\n=== 用户登录信息 ===")
        print("用户名 | 密码 | 角色")
        print("-" * 40)
        
        # 预设密码映射
        password_map = {
            "admin": "admin123",
            "jyj": "123456",
            "security1": "security123",
            "security2": "security456",
            "teacher1": "teacher123",
            "teacher2": "teacher456",
            "student1": "student123",
            "student2": "student456",
            "admin2": "admin456"
        }
        
        # 添加测试用户的密码
        for user in users:
            username = user[1]
            role = user[2]
            
            if username in password_map:
                password = password_map[username]
            else:
                password = "未知"
            
            print(f"{username} | {password} | {role}")
        
    except Exception as e:
        print(f"查询过程中出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
