import request from '@/utils/request'
// import store from '@/store' // 未使用的导入

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @returns {Promise} - 返回Promise
 */
export function login(data) {
  console.log('发送登录请求，数据:', data)
  return request({
    url: '/api/login',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    },
    transformResponse: [function(data) {
      // 打印原始响应数据
      console.log('登录原始响应数据:', data)
      try {
        return JSON.parse(data)
      } catch (e) {
        console.error('解析响应数据失败:', e)
        return data
      }
    }]
  })
}

export function getInfo(userId) {
  if (!userId) {
    console.warn('getInfo调用时缺少userId参数')
    return Promise.reject(new Error('缺少userId参数'))
  }

  return request({
    url: `/api/users/${userId}`,
    method: 'get'
  }).then(response => {
    console.log('获取用户信息成功，响应:', response)
    return response
  }).catch(error => {
    console.error('获取用户信息失败:', error)
    return Promise.reject(error)
  })
}

export function logout() {
  return request({
    url: '/api/logout',
    method: 'post'
  })
}

// 通过用户名获取用户ID
export function getUserIdByUsername(username) {
  if (!username) {
    console.warn('未提供用户名，无法获取用户ID')
    return Promise.resolve(null)
  }

  return request({
    url: '/api/users/public',
    method: 'get',
    params: { username: username }
  }).then(response => {
    if (response.data && response.data.users) {
      const user = response.data.users.find(u => u.username === username)
      return user ? user.id : null
    }
    return null
  }).catch(error => {
    console.error('获取用户ID时出错:', error)
    return null
  })
}

/**
 * 获取用户信息
 * @param {String} token - 身份验证令牌
 * @returns {Promise} - 返回用户信息的Promise
 */
export function getUserInfo(token) {
  if (!token) {
    console.warn('getInfo调用时缺少token参数')
    return Promise.reject(new Error('缺少token参数'))
  }

  console.log('获取用户信息 - token:', token)

  // 尝试使用/me端点
  return request({
    url: '/api/users/me',
    method: 'get',
    headers: {
      'Cache-Control': 'no-cache'
    }
  }).then(response => {
    console.log('获取用户信息成功，响应:', response)

    // 确保响应中包含用户ID
    if (response.data && response.data.user) {
      const user = response.data.user
      if (!user.id && user.user_id) {
        user.id = user.user_id
      }
    }

    return response
  }).catch(error => {
    console.error('获取用户信息失败，尝试备用方法:', error)

    // 从token中提取用户ID（如果可能）
    let userId = null
    if (token.includes('-token-')) {
      // 提取用户ID
      const parts = token.split('-')
      if (parts.length > 2) {
        userId = parseInt(parts[2])
      }
    }

    // 使用用户ID请求用户信息
    if (userId && !isNaN(userId)) {
      console.log(`使用用户ID(${userId})获取用户信息`)
      return request({
        url: `/api/users/${userId}`,
        method: 'get'
      })
    }

    // 回退到标准接口
    return request({
      url: '/api/users/me',
      method: 'get',
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @returns {Promise} - 返回Promise
 */
export function register(data) {
  return request({
    url: '/api/register',
    method: 'post',
    data
  })
}

/**
 * 更新用户信息
 * @param {number} id - 用户ID
 * @param {object} data - 更新的用户数据
 * @returns {Promise} - 返回更新结果的Promise
 */
export function updateUserInfo(id, data) {
  const normalizedData = {
    // 确保字段名与后端API预期一致
    u_name: data.name,
    u_belong: data.department,
    u_phone: data.phone,
    u_email: data.email,
    avatar: data.avatar // 添加头像字段
  }

  console.log('前端准备提交的数据:', normalizedData)

  return request({
    url: '/api/users/me',
    method: 'put',
    data: normalizedData,
    headers: {
      'Cache-Control': 'no-cache'
    }
  })
}

/**
 * 获取所有用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回用户列表的Promise
 */
export function getUsers(params) {
  return request({
    url: '/api/users',
    method: 'get',
    params
  })
}

/**
 * 更新用户角色
 * @param {Number} userId - 用户ID
 * @param {String} role - 角色名称
 * @returns {Promise} - 返回更新结果的Promise
 */
export function updateUserRole(userId, role) {
  return request({
    url: `/api/users/${userId}/role`,
    method: 'put',
    data: { role }
  })
}

export function checkUsername(username) {
  return request({
    url: '/api/check-username',
    method: 'post',
    data: { username }
  })
}

export function getUserById(id) {
  return request({
    url: `/api/users/${id}`,
    method: 'get'
  })
}

export function updateUser(id, data) {
  return request({
    url: `/api/users/${id}`,
    method: 'put',
    data
  })
}

export function deleteUser(id) {
  return request({
    url: `/api/users/${id}`,
    method: 'delete'
  })
}

/**
 * 修改密码
 * @param {Object} data - 包含旧密码和新密码的对象
 * @returns {Promise} - 返回修改结果的Promise
 */
export function changePassword(data) {
  return request({
    url: '/api/users/me/password',
    method: 'put',
    data: {
      old_password: data.oldPassword,
      new_password: data.newPassword
    }
  })
}
