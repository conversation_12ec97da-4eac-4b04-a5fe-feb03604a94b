# 校园电动车管理系统数据库实体关系图

```mermaid
erDiagram
    %% 核心实体
    Users ||--o{ Bikes : "拥有"
    Users ||--o{ ParkingRecord : "创建"
    Users ||--o{ ChargingRecord : "创建"
    Users ||--o{ ViolationRecord : "接收"
    Users ||--o{ ViolationRecord : "记录"
    Users ||--o{ Appeal : "提交"
    Users ||--o{ Evidence : "上传"
    Users ||--o{ Announcement : "创建"
    Users ||--o| Players : "关联"
    
    %% 车辆关系
    Bikes ||--o{ ParkingRecord : "使用"
    Bikes ||--o{ ChargingRecord : "使用"
    Bikes ||--o{ ViolationRecord : "涉及"
    
    %% 停车场关系
    ParkingLot ||--o{ ParkingSpace : "包含"
    ParkingLot ||--o{ ParkingRecord : "关联"
    ParkingLot ||--o{ ChargingRecord : "关联"
    ParkingLot ||--o{ ChargingReservation : "关联"
    
    %% 车位关系
    ParkingSpace ||--o{ ParkingRecord : "使用"
    ParkingSpace ||--o{ ChargingRecord : "使用"
    ParkingSpace ||--o| Bikes : "当前停放"
    ParkingSpace ||--o{ ChargingFault : "发生"
    
    %% 停车记录关系
    ParkingRecord ||--o{ ChargingRecord : "关联"
    
    %% 充电关系
    ChargingRecord ||--o{ ChargingException : "发生"
    ChargingFault ||--o{ ChargingException : "关联"
    
    %% 违规关系
    ViolationType ||--o{ ViolationRecord : "分类"
    ViolationRecord ||--o| Appeal : "申诉"
    ViolationRecord ||--o{ Evidence : "包含"
    Appeal ||--o{ Evidence : "包含"
```

## 实体说明

1. **Users (用户)**: 系统用户，包括普通用户、保安和管理员
2. **Bikes (车辆)**: 用户注册的电动车信息
3. **ParkingLot (停车场)**: 校园内的停车场信息
4. **ParkingSpace (停车位)**: 停车场内的具体车位
5. **ParkingRecord (停车记录)**: 记录车辆的停车信息
6. **ChargingRecord (充电记录)**: 记录车辆的充电信息
7. **ChargingReservation (充电预约)**: 用户的充电预约记录
8. **ChargingFault (充电故障)**: 充电设备故障记录
9. **ChargingException (充电异常)**: 充电过程中的异常情况
10. **ViolationType (违规类型)**: 定义各种违规类型
11. **ViolationRecord (违规记录)**: 记录车辆的违规信息
12. **Appeal (申诉)**: 用户对违规记录的申诉
13. **Evidence (证据)**: 违规或申诉的证据材料
14. **Announcement (公告)**: 系统公告信息
15. **Players (玩家)**: 关联到用户的游戏账号

## 关系说明

1. **用户与车辆**: 一个用户可以拥有多辆车辆
2. **用户与停车记录**: 一个用户可以创建多个停车记录
3. **用户与充电记录**: 一个用户可以创建多个充电记录
4. **用户与违规记录**: 一个用户可以接收多个违规记录，也可以作为记录者记录多个违规
5. **用户与申诉**: 一个用户可以提交多个申诉
6. **用户与证据**: 一个用户可以上传多个证据
7. **用户与公告**: 一个用户(管理员)可以创建多个公告
8. **用户与玩家**: 一个用户可以关联一个玩家账号

9. **车辆与停车记录**: 一辆车可以有多个停车记录
10. **车辆与充电记录**: 一辆车可以有多个充电记录
11. **车辆与违规记录**: 一辆车可以有多个违规记录

12. **停车场与车位**: 一个停车场包含多个车位
13. **停车场与停车记录**: 一个停车场关联多个停车记录
14. **停车场与充电记录**: 一个停车场关联多个充电记录
15. **停车场与充电预约**: 一个停车场关联多个充电预约

16. **车位与停车记录**: 一个车位关联多个停车记录
17. **车位与充电记录**: 一个车位关联多个充电记录
18. **车位与车辆**: 一个车位当前可以停放一辆车
19. **车位与充电故障**: 一个车位可以发生多个充电故障

20. **停车记录与充电记录**: 一个停车记录可以关联多个充电记录

21. **充电记录与充电异常**: 一个充电记录可以发生多个充电异常
22. **充电故障与充电异常**: 一个充电故障可以关联多个充电异常

23. **违规类型与违规记录**: 一个违规类型可以关联多个违规记录
24. **违规记录与申诉**: 一个违规记录可以关联一个申诉
25. **违规记录与证据**: 一个违规记录可以包含多个证据
26. **申诉与证据**: 一个申诉可以包含多个证据
