from app import create_app, db
from app.violations.models import ViolationRecord, Appeal, Evidence
from app.users.models import Users
from app.bikes.models import Bikes
from datetime import datetime, timedelta
import random

app = create_app()

# 违规类型列表
violation_types = [
    '违规停车',
    '占用消防通道',
    '占用无障碍通道',
    '超时停车',
    '车辆损坏公物',
    '无证驾驶',
    '其他违规'
]

# 违规地点列表
locations = [
    '教学楼A区停车场',
    '图书馆前广场',
    '学生宿舍区',
    '食堂周边',
    '体育馆停车区',
    '校门口',
    '行政楼前',
    '实验楼周边'
]

# 违规描述列表
descriptions = [
    '车辆停放在禁止停车区域',
    '车辆占用消防通道，影响消防安全',
    '车辆占用无障碍通道，影响残障人士通行',
    '车辆超时停放，超过规定时间',
    '车辆撞击公共设施造成损坏',
    '驾驶人无有效驾驶证件',
    '其他违规行为，详见照片'
]

# 处理结果列表
results = [
    '已处理，罚款并教育',
    '已处理，口头警告',
    '已处理，记录在案',
    '已撤销，证据不足',
    '已撤销，情况特殊'
]

# 申诉理由列表
appeal_reasons = [
    '我没有违规停车，这是误判',
    '当时是紧急情况，不得已而为之',
    '我已经获得了相关部门的临时许可',
    '车辆不是我停放的，可能被他人挪动',
    '我有合法的停车证明，请核实'
]

# 申诉处理意见列表
appeal_comments = [
    '经核实，申诉理由成立，撤销违规记录',
    '提供的证据充分，同意申诉',
    '情况属实，予以撤销',
    '申诉理由不充分，维持原处理决定',
    '提供的证据不足以支持申诉，驳回'
]

def create_test_data():
    with app.app_context():
        # 获取用户和车辆数据
        users = Users.query.all()
        bikes = Bikes.query.all()

        if not users or not bikes:
            print("没有找到用户或车辆数据，请先添加用户和车辆")
            return

        # 确保每个用户都有车辆
        users_with_bikes = []
        for user in users:
            user_bikes = [b for b in bikes if b.belong_to == user.u_id]
            if user_bikes:
                users_with_bikes.append(user)

        if not users_with_bikes:
            print("没有找到有车辆的用户，请先为用户添加车辆")
            return

        users = users_with_bikes

        # 获取保安和管理员用户
        security_users = [user for user in users if user.u_role == 'security']
        admin_users = [user for user in users if user.u_role == 'admin']

        # 如果没有保安用户，将管理员用户作为保安用户
        if not security_users:
            print("没有找到保安用户，使用管理员用户代替")
            security_users = [user for user in users if user.u_role == 'admin']

        if not admin_users:
            print("没有找到管理员用户，请先添加管理员用户")
            return

        # 如果仍然没有保安用户，使用普通用户
        if not security_users:
            print("使用普通用户作为保安用户")
            security_users = users

        # 创建违规记录
        violation_records = []
        for i in range(20):
            # 随机选择用户和车辆
            user = random.choice(users)
            bike = random.choice([b for b in bikes if b.belong_to == user.u_id]) if random.random() < 0.7 else random.choice(bikes)

            # 随机选择保安
            recorder = random.choice(security_users)

            # 随机生成违规时间（过去30天内）
            violation_time = datetime.now() - timedelta(days=random.randint(0, 30),
                                                       hours=random.randint(0, 23),
                                                       minutes=random.randint(0, 59))

            # 随机选择违规类型和地点
            violation_type = random.choice(violation_types)
            location = random.choice(locations)
            description = random.choice(descriptions)

            # 随机设置状态
            status = random.choice([0, 1, 2, 3])

            # 如果状态为已处理或已撤销，设置处理人和结果
            handler_id = None
            result = None
            fine_amount = None

            if status in [1, 3]:  # 已处理或已撤销
                handler = random.choice(admin_users)
                handler_id = handler.u_id
                result = random.choice(results)
                if status == 1:  # 已处理可能有罚款
                    fine_amount = round(random.uniform(50, 500), 2) if random.random() < 0.7 else 0

            # 创建违规记录
            violation = ViolationRecord(
                bike_number=bike.b_num,
                bike_id=bike.b_id,
                user_id=user.u_id,
                violation_time=violation_time,
                location=location,
                violation_type=violation_type,
                description=description,
                status=status,
                result=result,
                fine_amount=fine_amount,
                recorder_id=recorder.u_id,
                handler_id=handler_id,
                created_at=violation_time,
                updated_at=datetime.now() if status in [1, 3] else violation_time
            )

            db.session.add(violation)
            db.session.flush()  # 获取ID
            violation_records.append(violation)

            # 如果状态为申诉中，创建申诉记录
            if status == 2:
                appeal_time = violation_time + timedelta(days=random.randint(1, 5))
                appeal = Appeal(
                    violation_id=violation.id,
                    user_id=user.u_id,
                    reason=random.choice(appeal_reasons),
                    status=0,  # 待处理
                    created_at=appeal_time,
                    updated_at=appeal_time
                )
                db.session.add(appeal)

            # 随机为一些违规记录添加证据
            if random.random() < 0.7:
                evidence_count = random.randint(1, 3)
                for j in range(evidence_count):
                    evidence_type = 'image' if random.random() < 0.8 else 'video'
                    file_path = f"/uploads/violations/{'img' if evidence_type == 'image' else 'video'}{random.randint(1, 10)}.{'jpg' if evidence_type == 'image' else 'mp4'}"

                    evidence = Evidence(
                        related_id=violation.id,
                        related_type='violation',
                        evidence_type=evidence_type,
                        file_path=file_path,
                        uploader_id=recorder.u_id,
                        created_at=violation_time + timedelta(minutes=random.randint(5, 30))
                    )
                    db.session.add(evidence)

        # 为一些已处理的申诉添加处理结果
        appeals = Appeal.query.filter_by(status=0).all()
        for appeal in appeals:
            if random.random() < 0.6:  # 60%的申诉已处理
                appeal_status = random.choice([1, 2])  # 1: 通过, 2: 拒绝
                appeal.status = appeal_status
                appeal.comment = random.choice(appeal_comments)
                appeal.handler_id = random.choice(admin_users).u_id
                appeal.updated_at = datetime.now() - timedelta(days=random.randint(0, 10))

                # 如果申诉通过，更新违规记录状态为已撤销
                if appeal_status == 1:
                    violation = ViolationRecord.query.get(appeal.violation_id)
                    if violation:
                        violation.status = 3  # 已撤销
                        violation.result = "申诉通过，撤销违规记录"
                        violation.handler_id = appeal.handler_id
                        violation.updated_at = appeal.updated_at

                # 随机为申诉添加证据
                if random.random() < 0.5:
                    evidence_count = random.randint(1, 2)
                    for j in range(evidence_count):
                        evidence_type = 'image'  # 申诉证据主要是图片
                        file_path = f"/uploads/appeals/img{random.randint(1, 10)}.jpg"

                        evidence = Evidence(
                            related_id=appeal.id,
                            related_type='appeal',
                            evidence_type=evidence_type,
                            file_path=file_path,
                            uploader_id=appeal.user_id,
                            created_at=appeal.created_at + timedelta(hours=random.randint(1, 12))
                        )
                        db.session.add(evidence)

        # 提交所有更改
        db.session.commit()
        print(f"成功创建 {len(violation_records)} 条违规记录和相关数据")

if __name__ == "__main__":
    create_test_data()
