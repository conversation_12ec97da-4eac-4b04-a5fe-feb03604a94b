import sqlite3
import datetime

# 连接数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 查询停车记录总数
cursor.execute('SELECT COUNT(*) FROM parking_records')
total_count = cursor.fetchone()[0]
print(f"停车记录总数: {total_count}")

# 查询最近7天的停车记录
today = datetime.datetime.now().date()
seven_days_ago = today - datetime.timedelta(days=6)
seven_days_ago_str = seven_days_ago.strftime('%Y-%m-%d')
today_str = today.strftime('%Y-%m-%d')

print(f"\n查询时间范围: {seven_days_ago_str} 至 {today_str}")
cursor.execute('''
    SELECT COUNT(*) FROM parking_records 
    WHERE date(entry_time) >= ? AND date(entry_time) <= ?
''', (seven_days_ago_str, today_str))
recent_count = cursor.fetchone()[0]
print(f"最近7天的停车记录数: {recent_count}")

# 查询最近7天每天的停车记录数
print("\n最近7天每天的停车记录数:")
for i in range(7):
    day = seven_days_ago + datetime.timedelta(days=i)
    day_str = day.strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT COUNT(*) FROM parking_records 
        WHERE date(entry_time) = ?
    ''', (day_str,))
    day_count = cursor.fetchone()[0]
    print(f"{day_str}: {day_count}条记录")

# 查询停车记录详情
print("\n停车记录详情:")
cursor.execute('''
    SELECT id, vehicle_id, user_id, parking_lot_id, entry_time, exit_time, status 
    FROM parking_records 
    ORDER BY entry_time DESC
    LIMIT 10
''')
print("ID | 车辆ID | 用户ID | 停车场ID | 入场时间 | 出场时间 | 状态")
for row in cursor.fetchall():
    print(" | ".join(str(x) if x is not None else "None" for x in row))

# 关闭连接
conn.close()
