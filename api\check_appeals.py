from app import create_app
app = create_app()
from app.violations.models import Appeal, ViolationRecord
from flask import current_app

with app.app_context():
    print('查询申诉记录:')
    appeals = Appeal.query.all()
    print(f'总共有 {len(appeals)} 条申诉记录')
    for appeal in appeals[:5]:
        print(f'ID: {appeal.id}, 违规ID: {appeal.violation_id}, 用户ID: {appeal.user_id}, 状态: {appeal.status}, 理由: {appeal.reason[:30]}...')
    
    print('\n查询违规记录ID为3的记录:')
    violation = ViolationRecord.query.get(3)
    if violation:
        print(f'违规ID: {violation.id}, 用户ID: {violation.user_id}, 状态: {violation.status}, 类型: {violation.violation_type}')
        
        # 查询该违规记录的申诉
        related_appeals = Appeal.query.filter_by(violation_id=3).all()
        print(f'相关申诉数量: {len(related_appeals)}')
        for appeal in related_appeals:
            print(f'申诉ID: {appeal.id}, 状态: {appeal.status}, 理由: {appeal.reason[:30]}...')
    else:
        print('未找到ID为3的违规记录')
