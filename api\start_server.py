#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
后端服务启动脚本
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print(f"当前工作目录: {os.getcwd()}")
print("正在启动后端服务...")

try:
    # 导入Flask应用
    from flask_app import app, socketio
    print("Flask应用导入成功")

    # 启动服务器
    print("启动SocketIO服务器在 http://127.0.0.1:5001")
    socketio.run(app, host='127.0.0.1', port=5001, debug=False)

except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    traceback.print_exc()
