```mermaid
flowchart TD
    %% 用户信息管理流程图
    Start([开始]) --> AdminLogin[管理员登录]
    AdminLogin --> AccessUserMgmt[访问用户管理页面]
    
    %% 用户列表与搜索
    AccessUserMgmt --> ViewUserList[查看用户列表]
    ViewUserList --> SearchFilter[搜索/筛选用户]
    SearchFilter --> SortUsers[排序用户列表]
    
    %% 用户详情查看
    ViewUserList --> ViewUserDetail[查看用户详情]
    ViewUserDetail --> ViewUserVehicles[查看用户关联车辆]
    ViewUserDetail --> ViewUserParking[查看用户停车记录]
    ViewUserDetail --> ViewUserViolations[查看用户违规记录]
    
    %% 用户信息编辑
    ViewUserDetail --> EditUser[编辑用户信息]
    EditUser --> ValidateUserData{验证数据}
    ValidateUserData -->|数据有效| SaveUserChanges[保存用户信息]
    ValidateUserData -->|数据无效| ShowValidationError[显示验证错误]
    ShowValidationError --> EditUser
    
    %% 用户角色管理
    ViewUserDetail --> ManageUserRole[管理用户角色]
    ManageUserRole --> SelectRole[选择新角色]
    SelectRole --> ConfirmRoleChange[确认角色变更]
    ConfirmRoleChange --> UpdateUserRole[更新用户角色]
    
    %% 创建新用户
    ViewUserList --> CreateNewUser[创建新用户]
    CreateNewUser --> FillUserForm[填写用户表单]
    FillUserForm --> ValidateNewUser{验证数据}
    ValidateNewUser -->|数据有效| SaveNewUser[保存新用户]
    ValidateNewUser -->|数据无效| ShowNewUserError[显示验证错误]
    ShowNewUserError --> FillUserForm
    
    %% 删除用户
    ViewUserDetail --> DeleteUser[删除用户]
    DeleteUser --> ConfirmDeletion{确认删除}
    ConfirmDeletion -->|确认| ProcessDeletion[处理删除]
    ConfirmDeletion -->|取消| CancelDeletion[取消操作]
    ProcessDeletion --> CheckRelatedData{检查关联数据}
    CheckRelatedData -->|有关联数据| HandleRelatedData[处理关联数据]
    HandleRelatedData --> RemoveUser[移除用户]
    CheckRelatedData -->|无关联数据| RemoveUser
    
    %% 批量操作
    ViewUserList --> SelectMultipleUsers[选择多个用户]
    SelectMultipleUsers --> BatchOperation[批量操作]
    BatchOperation --> BatchDelete[批量删除]
    BatchOperation --> BatchRoleUpdate[批量角色更新]
    BatchOperation --> BatchExport[批量导出]
    
    %% 导出用户数据
    ViewUserList --> ExportUserData[导出用户数据]
    ExportUserData --> SelectExportFormat[选择导出格式]
    SelectExportFormat --> GenerateExportFile[生成导出文件]
    GenerateExportFile --> DownloadFile[下载文件]
    
    %% 样式定义
    classDef start fill:#f9d5e5,stroke:#333,stroke-width:2px
    classDef process fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef decision fill:#ffffcc,stroke:#333,stroke-width:1px
    classDef operation fill:#e6e6fa,stroke:#333,stroke-width:1px
    
    class Start start
    class AdminLogin,AccessUserMgmt,ViewUserList,SearchFilter,SortUsers,ViewUserDetail,ViewUserVehicles,ViewUserParking,ViewUserViolations,EditUser,SaveUserChanges,ShowValidationError,ManageUserRole,SelectRole,ConfirmRoleChange,UpdateUserRole,CreateNewUser,FillUserForm,SaveNewUser,ShowNewUserError,DeleteUser,ProcessDeletion,CancelDeletion,HandleRelatedData,RemoveUser,SelectMultipleUsers,BatchOperation,BatchDelete,BatchRoleUpdate,BatchExport,ExportUserData,SelectExportFormat,GenerateExportFile,DownloadFile process
    class ValidateUserData,ValidateNewUser,ConfirmDeletion,CheckRelatedData decision
```
