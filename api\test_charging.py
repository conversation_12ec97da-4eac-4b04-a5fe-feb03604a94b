"""
测试充电功能的脚本
"""
import os
import sys
import json
from datetime import datetime, timedelta

# 添加当前目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入应用和模型
from app import create_app, db
from app.charging.models import ChargingRecord, ChargingPriceStrategy, ChargingReservation
from app.parkinglots.models import ParkingSpace, ParkingLot
from app.users.models import Users
from app.bikes.models import Bikes
from app.parking_records.models import ParkingRecord

# 创建应用实例
app = create_app()

def test_charging_records():
    """测试充电记录功能"""
    with app.app_context():
        print("===== 测试充电记录功能 =====")

        # 获取所有充电记录
        records = ChargingRecord.query.all()
        print(f"共找到 {len(records)} 个充电记录")

        # 显示充电记录详情
        for record in records:
            details = record.get_details(include_relations=True)
            print(f"充电记录 #{details['id']}:")
            print(f"  车辆: {details.get('vehicle', {}).get('number', '未知')} ({details.get('vehicle', {}).get('brand', '未知')})")
            print(f"  用户: {details.get('user', {}).get('name', '未知')}")
            print(f"  停车场: {details.get('parking_lot', {}).get('name', '未知')}")
            print(f"  车位: {details.get('parking_space', {}).get('space_number', '未知')}")
            print(f"  开始时间: {details['start_time']}")
            print(f"  结束时间: {details['end_time'] or '进行中'}")
            print(f"  时长: {details['duration_formatted']}")
            print(f"  费用: {details['fee'] or '未计算'}")
            print(f"  状态: {details['status_text']}")
            print()

def test_charging_reservations():
    """测试充电预约功能"""
    with app.app_context():
        print("===== 测试充电预约功能 =====")

        # 获取所有充电预约
        reservations = ChargingReservation.query.all()
        print(f"共找到 {len(reservations)} 个充电预约")

        # 显示充电预约详情
        for reservation in reservations:
            details = reservation.get_details(include_relations=True)
            print(f"充电预约 #{details['id']}:")
            print(f"  车辆: {details.get('vehicle', {}).get('number', '未知')} ({details.get('vehicle', {}).get('brand', '未知')})")
            print(f"  用户: {details.get('user', {}).get('name', '未知')}")
            print(f"  停车场: {details.get('parking_lot', {}).get('name', '未知')}")
            print(f"  开始时间: {details['start_time']}")
            print(f"  结束时间: {details['end_time']}")
            print(f"  状态: {details['status_text']}")
            print(f"  备注: {details['remarks'] or '无'}")
            print()

def test_charging_price_strategies():
    """测试充电价格策略功能"""
    with app.app_context():
        print("===== 测试充电价格策略功能 =====")

        # 获取所有充电价格策略
        strategies = ChargingPriceStrategy.query.all()
        print(f"共找到 {len(strategies)} 个充电价格策略")

        # 显示充电价格策略详情
        for strategy in strategies:
            details = strategy.get_details()
            print(f"充电价格策略 #{details['id']}:")
            print(f"  名称: {details['name']}")
            print(f"  基础价格: {details['base_price']}元/小时")
            print(f"  高峰价格: {details['peak_price']}元/小时")
            print(f"  高峰时段: {details['peak_start_hour']}:00 - {details['peak_end_hour']}:00")
            print(f"  最低收费: {details['min_fee']}元")
            print(f"  状态: {details['status_text']}")
            print()

            # 测试费用计算
            now = datetime.now()
            start_time = now - timedelta(hours=2)
            end_time = now
            fee = strategy.calculate_fee(start_time, end_time)
            print(f"  测试费用计算:")
            print(f"    开始时间: {start_time}")
            print(f"    结束时间: {end_time}")
            print(f"    充电时长: 2小时")
            print(f"    计算费用: {fee}元")
            print()

def test_charging_spaces():
    """测试充电车位功能"""
    with app.app_context():
        print("===== 测试充电车位功能 =====")

        # 获取所有充电车位
        spaces = ParkingSpace.query.filter_by(type=3).all()
        print(f"共找到 {len(spaces)} 个充电车位")

        # 显示充电车位详情
        for space in spaces:
            print(f"充电车位 #{space.id}:")
            print(f"  车位编号: {space.space_number}")

            # 获取停车场信息
            parking_lot = ParkingLot.query.get(space.parking_lot_id)
            if parking_lot:
                print(f"  停车场: {parking_lot.name}")

            print(f"  状态: {'占用' if space.status == 1 else '空闲'}")

            # 如果有车辆占用，显示车辆信息
            if space.current_vehicle_id:
                vehicle = Bikes.query.get(space.current_vehicle_id)
                if vehicle:
                    print(f"  占用车辆: {vehicle.b_num} ({vehicle.brand})")

            print()

def run_all_tests():
    """运行所有测试"""
    test_charging_spaces()
    print("\n" + "="*50 + "\n")
    test_charging_price_strategies()
    print("\n" + "="*50 + "\n")
    test_charging_reservations()
    print("\n" + "="*50 + "\n")
    test_charging_records()

if __name__ == '__main__':
    # 根据命令行参数执行不同的测试
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        if test_name == 'spaces':
            test_charging_spaces()
        elif test_name == 'strategies':
            test_charging_price_strategies()
        elif test_name == 'reservations':
            test_charging_reservations()
        elif test_name == 'records':
            test_charging_records()
        else:
            print(f"未知的测试名称: {test_name}")
            print("可用的测试名称: spaces, strategies, reservations, records")
    else:
        # 默认运行所有测试
        run_all_tests()
