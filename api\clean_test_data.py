import sqlite3
import datetime

# 连接数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 查询当前记录总数
cursor.execute('SELECT COUNT(*) FROM charging_records')
total_before = cursor.fetchone()[0]
print(f'清理前充电记录总数: {total_before}')

# 查询测试脚本添加的记录数量
cursor.execute('SELECT COUNT(*) FROM charging_records WHERE id > 7')
test_records_count = cursor.fetchone()[0]
print(f'测试脚本添加的记录数: {test_records_count}')

# 删除ID大于7的记录（测试脚本添加的记录）
cursor.execute('DELETE FROM charging_records WHERE id > 7')
conn.commit()

# 查询清理后的记录总数
cursor.execute('SELECT COUNT(*) FROM charging_records')
total_after = cursor.fetchone()[0]
print(f'清理后充电记录总数: {total_after}')

# 查询剩余记录的详细信息
print('\n剩余充电记录详情:')
cursor.execute('''
    SELECT id, vehicle_id, user_id, parking_lot_id, parking_space_id, 
           start_time, end_time, status
    FROM charging_records
    ORDER BY id
''')
records = cursor.fetchall()
for record in records:
    print(f"ID: {record[0]}, 车辆ID: {record[1]}, 用户ID: {record[2]}, " +
          f"停车场ID: {record[3]}, 车位ID: {record[4]}, " +
          f"开始时间: {record[5]}, 结束时间: {record[6]}, 状态: {record[7]}")

# 关闭连接
conn.close()

print("\n测试数据清理完成！")
