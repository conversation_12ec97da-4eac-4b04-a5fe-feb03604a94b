from app import create_app, db
from app.charging.models import ChargingRecord, ChargingPriceStrategy, ChargingReservation
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.users.models import Users
from app.bikes.models import Bikes
from datetime import datetime, timedelta
import random

app = create_app()

def add_charging_test_data():
    with app.app_context():
        print("开始添加充电测试数据...")

        # 1. 添加充电价格策略
        print("添加充电价格策略...")
        strategies = [
            {
                'name': '校园标准充电',
                'base_price': 1.0,  # 基础价格，元/小时
                'peak_price': 1.5,  # 高峰价格，元/小时
                'peak_start_hour': 8,  # 高峰开始时间
                'peak_end_hour': 22,  # 高峰结束时间
                'min_fee': 0.5,  # 最低收费，元
                'status': 1,  # 启用
                'remarks': '校园电动车标准充电价格，高峰时段8:00-22:00收费1.5元/小时，其他时段1元/小时，最低收费0.5元。'
            },
            {
                'name': '教职工优惠充电',
                'base_price': 0.8,
                'peak_price': 1.2,
                'peak_start_hour': 8,
                'peak_end_hour': 22,
                'min_fee': 0.5,
                'status': 1,  # 启用
                'remarks': '教职工优惠充电价格，高峰时段8:00-22:00收费1.2元/小时，其他时段0.8元/小时，最低收费0.5元。'
            },
            {
                'name': '夜间优惠充电',
                'base_price': 0.5,
                'peak_price': 1.0,
                'peak_start_hour': 8,
                'peak_end_hour': 22,
                'min_fee': 0.5,
                'status': 1,  # 启用
                'remarks': '夜间优惠充电价格，22:00-次日8:00期间充电享受优惠价格，高峰时段1元/小时，其他时段0.5元/小时。'
            }
        ]

        for strategy_data in strategies:
            strategy = ChargingPriceStrategy.query.filter_by(name=strategy_data['name']).first()
            if not strategy:
                strategy = ChargingPriceStrategy(
                    name=strategy_data['name'],
                    base_price=strategy_data['base_price'],
                    peak_price=strategy_data['peak_price'],
                    peak_start_hour=strategy_data['peak_start_hour'],
                    peak_end_hour=strategy_data['peak_end_hour'],
                    min_fee=strategy_data['min_fee'],
                    status=strategy_data['status'],
                    remarks=strategy_data['remarks']
                )
                db.session.add(strategy)

        # 2. 添加充电车位
        print("添加充电车位...")
        # 获取所有停车场
        parking_lots = ParkingLot.query.all()
        if not parking_lots:
            print("没有找到停车场，请先添加停车场数据")
            return

        # 为每个停车场添加充电车位
        for lot in parking_lots:
            # 检查是否已有充电车位
            existing_charging_spaces = ParkingSpace.query.filter_by(
                parking_lot_id=lot.id,
                type=3  # 充电车位
            ).count()

            if existing_charging_spaces < 5:  # 每个停车场至少添加5个充电车位
                spaces_to_add = 5 - existing_charging_spaces
                print(f"为停车场 {lot.name} 添加 {spaces_to_add} 个充电车位")

                for i in range(spaces_to_add):
                    # 生成车位编号
                    area_codes = ['A', 'B', 'C', 'D', 'E']
                    area_code = area_codes[lot.id % len(area_codes)]
                    space_number = f"充-{area_code}{existing_charging_spaces + i + 1:02d}"

                    # 随机选择充电类型
                    charging_types = [
                        {'power': 2.2, 'remarks': '慢充桩-电动自行车专用'},
                        {'power': 3.3, 'remarks': '标准充-电动自行车/电动车'},
                        {'power': 7.0, 'remarks': '快充桩-电动车/电动摩托车'}
                    ]
                    charging_type = random.choice(charging_types)

                    # 创建充电车位
                    space = ParkingSpace(
                        parking_lot_id=lot.id,
                        space_number=space_number,
                        type=3,  # 充电车位
                        status=0,  # 空闲
                        power=charging_type['power'],
                        remarks=f"{charging_type['remarks']} - {lot.name}{space_number}"
                    )
                    db.session.add(space)

                # 更新停车场总车位数
                lot.total_spaces += spaces_to_add

        # 3. 添加充电记录
        print("添加充电记录...")
        # 获取所有用户和车辆
        users = Users.query.all()
        if not users:
            print("没有找到用户，请先添加用户数据")
            return

        # 获取所有充电车位
        charging_spaces = ParkingSpace.query.filter_by(type=2).all()
        if not charging_spaces:
            print("没有找到充电车位")
            return

        # 为每个用户添加充电记录
        for user in users:
            # 获取用户的车辆
            vehicles = Bikes.query.filter_by(belong_to=user.u_id).all()
            if not vehicles:
                continue

            # 为每个车辆添加1-3条充电记录
            for vehicle in vehicles:
                num_records = random.randint(1, 3)
                for i in range(num_records):
                    # 随机选择一个充电车位
                    space = random.choice(charging_spaces)

                    # 随机生成开始时间（过去30天内）
                    days_ago = random.randint(0, 30)
                    hours_ago = random.randint(0, 23)
                    minutes_ago = random.randint(0, 59)
                    start_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)

                    # 随机生成结束时间（开始时间后1-5小时）
                    duration_hours = random.randint(1, 5)
                    end_time = start_time + timedelta(hours=duration_hours)

                    # 如果结束时间在未来，则设为None（表示正在充电）
                    if end_time > datetime.now():
                        end_time = None
                        status = 0  # 进行中
                    else:
                        status = 1  # 已完成

                    # 计算费用
                    fee = None
                    if status == 1:  # 已完成
                        strategy = ChargingPriceStrategy.query.filter_by(status=1).first()
                        if strategy:
                            fee = strategy.calculate_fee(start_time, end_time)

                    # 创建充电记录
                    record = ChargingRecord(
                        parking_record_id=random.randint(1, 100),  # 模拟停车记录ID
                        user_id=user.u_id,
                        vehicle_id=vehicle.b_id,
                        parking_lot_id=space.parking_lot_id,
                        parking_space_id=space.id
                    )

                    # 手动设置其他字段
                    record.start_time = start_time
                    if end_time:
                        record.end_time = end_time
                    record.status = status
                    if fee:
                        record.fee = fee
                    db.session.add(record)

        # 4. 添加充电预约
        print("添加充电预约...")
        # 为每个用户添加0-2条充电预约
        for user in users:
            # 获取用户的车辆
            vehicles = Bikes.query.filter_by(belong_to=user.u_id).all()
            if not vehicles:
                continue

            num_reservations = random.randint(0, 2)
            for i in range(num_reservations):
                # 随机选择一个车辆
                vehicle = random.choice(vehicles)

                # 随机选择一个停车场
                lot = random.choice(parking_lots)

                # 随机生成开始时间（未来7天内）
                days_future = random.randint(0, 7)
                hours_future = random.randint(0, 23)
                start_time = datetime.now() + timedelta(days=days_future, hours=hours_future)

                # 随机生成结束时间（开始时间后1-3小时）
                duration_hours = random.randint(1, 3)
                end_time = start_time + timedelta(hours=duration_hours)

                # 随机状态
                status = random.choice([0, 1, 2])  # 0待使用，1已使用，2已取消

                # 创建充电预约
                reservation = ChargingReservation(
                    user_id=user.u_id,
                    vehicle_id=vehicle.b_id,
                    parking_lot_id=lot.id,
                    start_time=start_time,
                    end_time=end_time,
                    remarks=f"充电预约 {i+1} for {vehicle.b_num}"
                )

                # 手动设置状态
                reservation.status = status
                db.session.add(reservation)

        # 提交所有更改
        db.session.commit()
        print("充电测试数据添加完成！")

if __name__ == '__main__':
    add_charging_test_data()
