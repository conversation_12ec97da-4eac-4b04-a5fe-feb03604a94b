from flask import Flask
from app import create_app
import json

app = create_app()

# 在应用上下文中执行API调用
with app.app_context():
    # 使用Flask的测试客户端发送请求
    client = app.test_client()
    response = client.get('/api/parking-records/stats?date_range=week')

    # 打印响应
    print("API响应状态码:", response.status_code)

    # 解析响应数据
    data = json.loads(response.data.decode('utf-8'))

    # 检查peak_hour数据
    if 'data' in data and 'peak_hour' in data['data']:
        peak_hour = data['data']['peak_hour']
        print("\npeak_hour数据:", peak_hour)

        # 检查peak_hour是否为空
        if peak_hour is None:
            print("peak_hour为空")
        else:
            print("peak_hour不为空，值为:", peak_hour)

            # 检查peak_hour格式
            if isinstance(peak_hour, str):
                import re
                hour_match = re.match(r'^(\d+):', peak_hour)
                if hour_match and hour_match.group(1):
                    peak_hour_num = int(hour_match.group(1))
                    print(f"peak_hour对应的小时数: {peak_hour_num}")
                else:
                    print("peak_hour格式不正确")
            else:
                print("peak_hour不是字符串类型")
    else:
        print("\n响应中没有peak_hour数据")

    # 检查停车记录数据
    if 'data' in data and 'daily_stats' in data['data']:
        daily_stats = data['data']['daily_stats']
        print("\ndaily_stats数据:")
        print(json.dumps(daily_stats, indent=2, ensure_ascii=False))

        # 统计总记录数
        total_records = sum(day.get('count', 0) for day in daily_stats)
        print(f"\n总记录数: {total_records}")

        # 统计已完成记录数
        completed_records = sum(day.get('completed', 0) for day in daily_stats)
        print(f"已完成记录数: {completed_records}")

        # 统计进行中记录数
        active_records = sum(day.get('active', 0) for day in daily_stats)
        print(f"进行中记录数: {active_records}")
    else:
        print("\n响应中没有daily_stats数据")

    # 检查后端查询逻辑
    print("\n检查后端查询逻辑:")

    # 导入必要的模块
    from app.parking_records.models import ParkingRecord
    from sqlalchemy import func, desc
    from app import db

    # 构建高峰时段查询
    peak_hour_query = db.session.query(
        func.extract('hour', ParkingRecord.entry_time).label('hour'),
        func.count(ParkingRecord.id).label('count')
    ).filter(ParkingRecord.status == 1)  # 只考虑已完成的停车记录

    # 按小时分组并获取结果
    peak_hour_stats = peak_hour_query.group_by('hour').order_by(desc('count')).limit(1).all()

    # 打印查询结果
    print("高峰时段查询结果:", peak_hour_stats)

    # 如果查询结果为空，检查是否有已完成的停车记录
    if not peak_hour_stats:
        completed_records_count = db.session.query(ParkingRecord).filter(ParkingRecord.status == 1).count()
        print(f"已完成的停车记录数: {completed_records_count}")

        # 如果有已完成的记录，检查entry_time是否为空
        if completed_records_count > 0:
            records_with_entry_time = db.session.query(ParkingRecord).filter(
                ParkingRecord.status == 1,
                ParkingRecord.entry_time.isnot(None)
            ).count()
            print(f"有entry_time的已完成记录数: {records_with_entry_time}")

            # 如果有entry_time的记录，检查func.extract是否工作正常
            if records_with_entry_time > 0:
                print("检查func.extract是否工作正常:")
                test_query = db.session.query(
                    ParkingRecord.id,
                    ParkingRecord.entry_time,
                    func.extract('hour', ParkingRecord.entry_time).label('hour')
                ).filter(
                    ParkingRecord.status == 1,
                    ParkingRecord.entry_time.isnot(None)
                ).limit(5).all()

                for record in test_query:
                    print(f"记录ID: {record.id}, 入场时间: {record.entry_time}, 提取的小时: {record.hour}")
