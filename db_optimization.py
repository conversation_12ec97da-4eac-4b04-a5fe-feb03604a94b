import sqlite3
import os

def optimize_database():
    """优化数据库结构，添加索引和约束"""
    print("开始优化数据库结构...")
    
    # 数据库路径
    db_path = os.path.join('api', 'sys.db')
    print(f"使用数据库: {db_path}")
    
    # 连接到数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 添加索引
        print("\n添加索引...")
        
        # 停车记录表索引
        indexes = [
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_status ON parking_records(status)", "停车记录状态索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_user_id ON parking_records(user_id)", "停车记录用户ID索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_vehicle_id ON parking_records(vehicle_id)", "停车记录车辆ID索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_parking_lot_id ON parking_records(parking_lot_id)", "停车记录停车场ID索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_parking_space_id ON parking_records(parking_space_id)", "停车记录车位ID索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_entry_time ON parking_records(entry_time)", "停车记录入场时间索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_exit_time ON parking_records(exit_time)", "停车记录出场时间索引"),
            
            # 复合索引
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_vehicle_status ON parking_records(vehicle_id, status)", "停车记录车辆状态复合索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_records_user_status ON parking_records(user_id, status)", "停车记录用户状态复合索引"),
            
            # 停车场表索引
            ("CREATE INDEX IF NOT EXISTS idx_parking_lots_status ON parking_lots(status)", "停车场状态索引"),
            
            # 车位表索引
            ("CREATE INDEX IF NOT EXISTS idx_parking_spaces_lot_status ON parking_spaces(parking_lot_id, status)", "车位停车场状态复合索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_spaces_status ON parking_spaces(status)", "车位状态索引"),
            ("CREATE INDEX IF NOT EXISTS idx_parking_spaces_type ON parking_spaces(type)", "车位类型索引"),
            
            # 车辆表索引
            ("CREATE INDEX IF NOT EXISTS idx_bikes_status ON bikes(status)", "车辆状态索引"),
            ("CREATE INDEX IF NOT EXISTS idx_bikes_type ON bikes(b_type)", "车辆类型索引")
        ]
        
        for index_sql, description in indexes:
            try:
                cursor.execute(index_sql)
                print(f"  创建成功: {description}")
            except sqlite3.Error as e:
                print(f"  创建失败: {description}, 错误: {e}")
        
        # 添加触发器
        print("\n添加触发器...")
        
        # 停车记录状态变更时更新车位状态的触发器
        triggers = [
            ("""
            CREATE TRIGGER IF NOT EXISTS trg_parking_record_insert
            AFTER INSERT ON parking_records
            FOR EACH ROW
            WHEN NEW.status = 0
            BEGIN
                UPDATE parking_spaces
                SET status = 1, current_vehicle_id = NEW.vehicle_id
                WHERE id = NEW.parking_space_id;
            END
            """, "停车记录创建时更新车位状态"),
            
            ("""
            CREATE TRIGGER IF NOT EXISTS trg_parking_record_update
            AFTER UPDATE ON parking_records
            FOR EACH ROW
            WHEN OLD.status = 0 AND NEW.status = 1
            BEGIN
                UPDATE parking_spaces
                SET status = 0, current_vehicle_id = NULL
                WHERE id = OLD.parking_space_id;
            END
            """, "停车记录结束时更新车位状态"),
            
            ("""
            CREATE TRIGGER IF NOT EXISTS trg_parking_record_delete
            AFTER DELETE ON parking_records
            FOR EACH ROW
            WHEN OLD.status = 0
            BEGIN
                UPDATE parking_spaces
                SET status = 0, current_vehicle_id = NULL
                WHERE id = OLD.parking_space_id;
            END
            """, "停车记录删除时更新车位状态")
        ]
        
        for trigger_sql, description in triggers:
            try:
                cursor.execute(trigger_sql)
                print(f"  创建成功: {description}")
            except sqlite3.Error as e:
                print(f"  创建失败: {description}, 错误: {e}")
        
        # 添加视图
        print("\n添加视图...")
        
        views = [
            ("""
            CREATE VIEW IF NOT EXISTS view_active_parking AS
            SELECT 
                pr.id, pr.vehicle_id, pr.user_id, pr.parking_lot_id, pr.parking_space_id,
                pr.entry_time, pr.status, pr.created_at, pr.updated_at, pr.remarks,
                b.b_num as vehicle_number, b.brand as vehicle_brand, b.color as vehicle_color,
                u.u_name as user_name, u.u_phone as user_phone,
                pl.name as parking_lot_name, pl.address as parking_lot_address,
                ps.space_number
            FROM parking_records pr
            JOIN bikes b ON pr.vehicle_id = b.b_id
            JOIN users u ON pr.user_id = u.u_id
            JOIN parking_lots pl ON pr.parking_lot_id = pl.id
            JOIN parking_spaces ps ON pr.parking_space_id = ps.id
            WHERE pr.status = 0
            """, "进行中的停车记录视图"),
            
            ("""
            CREATE VIEW IF NOT EXISTS view_parking_stats AS
            SELECT
                pl.id as parking_lot_id, pl.name as parking_lot_name,
                pl.total_spaces, pl.occupied_spaces,
                COUNT(CASE WHEN ps.status = 0 THEN 1 END) as available_spaces,
                COUNT(CASE WHEN ps.status = 1 THEN 1 END) as occupied_spaces,
                COUNT(CASE WHEN ps.status = 2 THEN 1 END) as maintenance_spaces,
                CAST(pl.occupied_spaces AS REAL) / CASE WHEN pl.total_spaces > 0 THEN pl.total_spaces ELSE 1 END * 100 as utilization_rate
            FROM parking_lots pl
            LEFT JOIN parking_spaces ps ON pl.id = ps.parking_lot_id
            GROUP BY pl.id
            """, "停车场统计视图"),
            
            ("""
            CREATE VIEW IF NOT EXISTS view_user_vehicles AS
            SELECT
                u.u_id as user_id, u.u_name as user_name,
                b.b_id as vehicle_id, b.b_num as vehicle_number,
                b.brand as vehicle_brand, b.color as vehicle_color,
                b.b_type as vehicle_type, b.status as vehicle_status,
                CASE WHEN EXISTS (
                    SELECT 1 FROM parking_records pr 
                    WHERE pr.vehicle_id = b.b_id AND pr.status = 0
                ) THEN 1 ELSE 0 END as is_parking
            FROM users u
            JOIN bikes b ON u.u_id = b.belong_to
            """, "用户车辆视图")
        ]
        
        for view_sql, description in views:
            try:
                cursor.execute(view_sql)
                print(f"  创建成功: {description}")
            except sqlite3.Error as e:
                print(f"  创建失败: {description}, 错误: {e}")
        
        # 提交更改
        conn.commit()
        print("\n数据库优化完成")
        
    except Exception as e:
        print(f"数据库优化过程中出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    optimize_database()
