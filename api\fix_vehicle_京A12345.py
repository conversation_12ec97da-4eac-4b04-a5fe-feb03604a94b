"""
修复京A12345车辆的违规处置问题

问题：管理员对京A12345车辆的违规记录做了禁用车辆的违规处置，但是在数据库中车辆仍处于可用状态，
说明禁用操作未成功执行。

分析：
1. 数据库中存在车牌号为京A12345的车辆(ID=1)，状态为"可用"
2. 存在两条关于京A12345的违规记录(ID=1和ID=4)
3. 违规记录中的bike_id字段为None，没有关联到车辆表中的记录
4. 由于违规记录中没有正确关联车辆ID，禁用操作无法成功执行

修复方案：
1. 更新违规记录，设置正确的bike_id
2. 手动执行车辆禁用操作
"""
import sqlite3
import sys
import os
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_violation_records():
    """修复违规记录中的车辆ID"""
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    try:
        # 开始事务
        conn.execute("BEGIN TRANSACTION")
        
        # 1. 查找京A12345车辆的ID
        cursor.execute('SELECT b_id FROM bikes WHERE b_num = ?', ('京A12345',))
        vehicle = cursor.fetchone()
        
        if not vehicle:
            print("未找到车牌号为京A12345的车辆，无法修复")
            conn.rollback()
            return False
            
        vehicle_id = vehicle[0]
        print(f"找到车辆ID: {vehicle_id}")
        
        # 2. 更新违规记录中的车辆ID
        cursor.execute(
            'UPDATE violation_records SET bike_id = ? WHERE bike_number = ? AND bike_id IS NULL',
            (vehicle_id, '京A12345')
        )
        updated_count = cursor.rowcount
        print(f"更新了 {updated_count} 条违规记录")
        
        # 3. 查询更新后的违规记录
        cursor.execute(
            'SELECT id, bike_number, bike_id, status, violation_type FROM violation_records WHERE bike_number = ?',
            ('京A12345',)
        )
        records = cursor.fetchall()
        
        print("更新后的违规记录:")
        for record in records:
            print(f"ID={record[0]}, 车牌号={record[1]}, 车辆ID={record[2]}, 状态={record[3]}, 类型={record[4]}")
        
        # 提交事务
        conn.commit()
        print("违规记录修复成功")
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"修复违规记录失败: {e}")
        return False
    finally:
        conn.close()

def disable_vehicle():
    """手动禁用车辆"""
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    try:
        # 开始事务
        conn.execute("BEGIN TRANSACTION")
        
        # 1. 查找京A12345车辆
        cursor.execute('SELECT b_id, status FROM bikes WHERE b_num = ?', ('京A12345',))
        vehicle = cursor.fetchone()
        
        if not vehicle:
            print("未找到车牌号为京A12345的车辆，无法禁用")
            conn.rollback()
            return False
            
        vehicle_id = vehicle[0]
        current_status = vehicle[1]
        print(f"找到车辆ID: {vehicle_id}, 当前状态: {current_status}")
        
        if current_status == "废弃":
            print("车辆已经处于禁用状态，无需再次禁用")
            conn.rollback()
            return False
        
        # 2. 查找状态为1(已处理)的违规记录
        cursor.execute(
            'SELECT id FROM violation_records WHERE bike_id = ? AND status = 1 ORDER BY id DESC LIMIT 1',
            (vehicle_id,)
        )
        violation = cursor.fetchone()
        
        if not violation:
            print("未找到已处理的违规记录，无法关联禁用记录")
            conn.rollback()
            return False
            
        violation_id = violation[0]
        print(f"找到违规记录ID: {violation_id}")
        
        # 3. 更新车辆状态为禁用
        cursor.execute(
            'UPDATE bikes SET status = ? WHERE b_id = ?',
            ("废弃", vehicle_id)
        )
        print(f"已将车辆状态更新为: 废弃")
        
        # 4. 创建禁用记录
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cursor.execute(
            '''INSERT INTO vehicle_disable_records 
               (bike_id, violation_id, disable_start_time, is_active, created_at, updated_at, reason) 
               VALUES (?, ?, ?, ?, ?, ?, ?)''',
            (vehicle_id, violation_id, now, 1, now, now, "违规停车，管理员处置")
        )
        disable_record_id = cursor.lastrowid
        print(f"已创建禁用记录，ID: {disable_record_id}")
        
        # 提交事务
        conn.commit()
        print("车辆禁用成功")
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"禁用车辆失败: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("开始修复京A12345车辆的违规处置问题...")
    
    # 1. 修复违规记录
    if fix_violation_records():
        # 2. 禁用车辆
        disable_vehicle()
    
    print("修复完成")
