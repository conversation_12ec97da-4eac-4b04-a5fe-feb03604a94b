import sqlite3

def update_vehicle_status():
    """更新所有车辆状态为 available"""
    print("更新所有车辆状态为 available...")
    
    # 连接到数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    try:
        # 查询所有车辆
        cursor.execute("SELECT * FROM bikes")
        bikes = cursor.fetchall()
        
        if bikes:
            print(f"找到 {len(bikes)} 辆车:")
            for bike in bikes:
                bike_id = bike[0]
                bike_num = bike[2]
                current_status = bike[6]
                
                print(f"车辆 ID: {bike_id}, 车牌号: {bike_num}, 当前状态: {current_status}")
                
                # 更新状态为 available
                cursor.execute("UPDATE bikes SET status = ? WHERE b_id = ?", ('available', bike_id))
                
                print(f"已更新车辆 ID: {bike_id}, 车牌号: {bike_num} 的状态为 available")
            
            # 提交更改
            conn.commit()
            print("所有车辆状态已更新为 available")
        else:
            print("没有找到车辆")
        
    except Exception as e:
        print(f"更新车辆状态时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    update_vehicle_status()
