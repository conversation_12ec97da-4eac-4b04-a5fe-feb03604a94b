"""
检查充电故障和充电异常之间的关联
"""
import os
import sys
from datetime import datetime

# 添加父目录到系统路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
sys.path.insert(0, parent_dir)

# 导入应用和模型
from app import create_app, db
from app.charging.models import ChargingFault, ChargingException, ChargingRecord
from app.parkinglots.models import ParkingSpace

# 创建应用实例
app = create_app()

def check_relations():
    """检查充电故障和充电异常之间的关联"""
    with app.app_context():
        print("\n===== 充电故障记录 =====")
        faults = ChargingFault.query.all()
        print(f"充电故障记录总数: {len(faults)}")
        
        for fault in faults:
            space = ParkingSpace.query.get(fault.space_id)
            space_number = space.space_number if space else "未知"
            status_map = {0: '待处理', 1: '处理中', 2: '已完成', 3: '已关闭'}
            status_text = status_map.get(fault.status, '未知')
            
            print(f"ID: {fault.id}, 车位: {space_number}(ID:{fault.space_id}), 类型: {fault.fault_type}, 状态: {status_text}")
            
            # 查找关联的充电异常
            exceptions = ChargingException.query.filter_by(fault_id=fault.id).all()
            if exceptions:
                print(f"  关联的充电异常: {len(exceptions)}条")
                for exception in exceptions:
                    status_map = {0: '未处理', 1: '已处理'}
                    status_text = status_map.get(exception.status, '未知')
                    print(f"  - 异常ID: {exception.id}, 充电记录ID: {exception.charging_record_id}, 状态: {status_text}")
            else:
                print("  没有关联的充电异常")
            
            print("")
        
        print("\n===== 充电异常记录 =====")
        exceptions = ChargingException.query.all()
        print(f"充电异常记录总数: {len(exceptions)}")
        
        for exception in exceptions:
            status_map = {0: '未处理', 1: '已处理'}
            status_text = status_map.get(exception.status, '未知')
            
            space = None
            if exception.space_id:
                space = ParkingSpace.query.get(exception.space_id)
            
            space_number = space.space_number if space else "未知"
            
            print(f"ID: {exception.id}, 故障ID: {exception.fault_id}, 充电记录ID: {exception.charging_record_id}, 车位: {space_number}(ID:{exception.space_id}), 状态: {status_text}")
            
            # 查找关联的充电记录
            if exception.charging_record_id:
                charging_record = ChargingRecord.query.get(exception.charging_record_id)
                if charging_record:
                    status_map = {0: '进行中', 1: '已完成', 2: '异常'}
                    status_text = status_map.get(charging_record.status, '未知')
                    print(f"  关联的充电记录: ID: {charging_record.id}, 状态: {status_text}")
                else:
                    print("  关联的充电记录不存在")
            else:
                print("  没有关联的充电记录")
            
            print("")
        
        print("\n===== 关联统计 =====")
        # 统计有多少充电故障没有关联的充电异常
        faults_without_exceptions = 0
        for fault in faults:
            exceptions = ChargingException.query.filter_by(fault_id=fault.id).all()
            if not exceptions:
                faults_without_exceptions += 1
        
        print(f"没有关联充电异常的充电故障数: {faults_without_exceptions}/{len(faults)}")
        
        # 统计有多少充电异常没有关联的充电故障
        exceptions_without_faults = 0
        for exception in exceptions:
            if not exception.fault_id:
                exceptions_without_faults += 1
        
        print(f"没有关联充电故障的充电异常数: {exceptions_without_faults}/{len(exceptions)}")
        
        # 统计有多少充电异常没有关联的充电记录
        exceptions_without_records = 0
        for exception in exceptions:
            if not exception.charging_record_id:
                exceptions_without_records += 1
        
        print(f"没有关联充电记录的充电异常数: {exceptions_without_records}/{len(exceptions)}")

if __name__ == '__main__':
    check_relations()
