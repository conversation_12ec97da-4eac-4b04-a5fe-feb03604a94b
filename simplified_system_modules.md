```mermaid
flowchart TD
    %% 系统核心
    System[校园电动车管理系统] --> Auth[认证与权限管理]
    
    %% 主要功能模块
    System --> UserMgmt[用户管理]
    System --> VehicleMgmt[车辆管理]
    System --> ParkingCenter[停车中心]
    System --> ChargingCenter[充电中心]
    System --> ViolationCenter[违规中心]
    System --> AnnouncementCenter[公告管理]
    
    %% 用户管理子模块
    UserMgmt --> UserReg[用户注册/登录]
    UserMgmt --> UserProfile[个人信息管理]
    UserMgmt --> RolePermission[角色权限管理]
    
    %% 车辆管理子模块
    VehicleMgmt --> VehicleReg[车辆注册]
    VehicleMgmt --> VehicleInfo[车辆信息管理]
    
    %% 停车中心子模块
    ParkingCenter --> ParkingLotMgmt[停车场管理]
    ParkingCenter --> ParkingSpaceMgmt[停车位管理]
    ParkingCenter --> ParkingRecordMgmt[停车记录管理]
    
    %% 充电中心子模块
    ChargingCenter --> ChargingSpaceMgmt[充电车位管理]
    ChargingCenter --> ChargingRecordMgmt[充电记录管理]
    
    %% 违规中心子模块
    ViolationCenter --> ViolationRecordMgmt[违规记录管理]
    ViolationCenter --> AppealMgmt[申诉管理]
    
    %% 公告管理子模块
    AnnouncementCenter --> AnnouncementPublish[公告发布]
    AnnouncementCenter --> AnnouncementView[公告查看]
    
    %% 数据库
    Database[(数据库)] --- UserMgmt
    Database --- VehicleMgmt
    Database --- ParkingCenter
    Database --- ChargingCenter
    Database --- ViolationCenter
    Database --- AnnouncementCenter
    
    %% 样式定义
    classDef systemCore fill:#f9d5e5,stroke:#333,stroke-width:2px
    classDef mainModule fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef subModule fill:#e6e6fa,stroke:#333,stroke-width:1px
    classDef database fill:#f5f5f5,stroke:#333,stroke-width:1px
    
    class System systemCore
    class Auth,UserMgmt,VehicleMgmt,ParkingCenter,ChargingCenter,ViolationCenter,AnnouncementCenter mainModule
    class UserReg,UserProfile,RolePermission,VehicleReg,VehicleInfo,ParkingLotMgmt,ParkingSpaceMgmt,ParkingRecordMgmt,ChargingSpaceMgmt,ChargingRecordMgmt,ViolationRecordMgmt,AppealMgmt,AnnouncementPublish,AnnouncementView subModule
    class Database database
```
