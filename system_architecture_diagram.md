```mermaid
graph TD
    %% 主要模块定义
    User((用户)) --> Auth[认证授权模块]
    Auth --> UserMgmt[用户管理模块]
    Auth --> Dashboard[个人中心]
    
    %% 核心业务模块
    Dashboard --> VehicleMgmt[车辆管理]
    Dashboard --> ParkingCenter[停车中心]
    Dashboard --> ChargingCenter[充电中心]
    Dashboard --> ViolationCenter[违规中心]
    
    %% 管理模块
    Admin((管理员)) --> Auth
    Auth --> AdminPanel[管理控制台]
    AdminPanel --> UserAdmin[用户管理]
    AdminPanel --> VehicleAdmin[电动车管理]
    AdminPanel --> ParkingAdmin[停车场管理]
    AdminPanel --> ChargingAdmin[充电设施管理]
    AdminPanel --> ViolationAdmin[违规管理]
    AdminPanel --> AnnouncementAdmin[公告管理]
    
    %% 数据库
    Database[(数据库)] --- UserMgmt
    Database --- VehicleMgmt
    Database --- ParkingCenter
    Database --- ChargingCenter
    Database --- ViolationCenter
    Database --- AdminPanel
    
    %% 数据流向和交互关系
    %% 车辆注册流程
    subgraph 车辆注册流程
        User -- 1.登录 --> Auth
        Auth -- 2.验证身份 --> UserMgmt
        UserMgmt -- 3.进入个人中心 --> Dashboard
        Dashboard -- 4.访问车辆管理 --> VehicleMgmt
        VehicleMgmt -- 5.提交车辆信息 --> Database
        Database -- 6.存储车辆数据 --> VehicleMgmt
        VehicleMgmt -- 7.注册成功通知 --> User
    end
    
    %% 停车流程
    subgraph 停车流程
        User -- 1.登录 --> Auth
        Auth -- 2.验证身份 --> UserMgmt
        UserMgmt -- 3.进入停车中心 --> ParkingCenter
        ParkingCenter -- 4.查询停车场信息 --> Database
        Database -- 5.返回可用车位 --> ParkingCenter
        ParkingCenter -- 6.选择车辆和车位 --> Database
        Database -- 7.更新车位状态 --> ParkingCenter
        ParkingCenter -- 8.创建停车记录 --> Database
        Database -- 9.返回停车凭证 --> ParkingCenter
        ParkingCenter -- 10.显示停车成功 --> User
    end
    
    %% 充电流程
    subgraph 充电流程
        User -- 1.登录 --> Auth
        Auth -- 2.验证身份 --> UserMgmt
        UserMgmt -- 3.进入充电中心 --> ChargingCenter
        ChargingCenter -- 4.查询停车记录 --> Database
        Database -- 5.返回进行中停车 --> ChargingCenter
        ChargingCenter -- 6.选择充电车位 --> Database
        Database -- 7.验证车位可用性 --> ChargingCenter
        ChargingCenter -- 8.创建充电记录 --> Database
        Database -- 9.更新车位状态 --> ChargingCenter
        ChargingCenter -- 10.显示充电开始 --> User
    end
    
    %% 违规处理流程
    subgraph 违规处理流程
        Admin -- 1.登录 --> Auth
        Auth -- 2.验证管理员身份 --> AdminPanel
        AdminPanel -- 3.进入违规管理 --> ViolationAdmin
        ViolationAdmin -- 4.记录违规信息 --> Database
        Database -- 5.关联用户和车辆 --> ViolationAdmin
        ViolationAdmin -- 6.生成违规通知 --> Database
        Database -- 7.更新用户违规状态 --> UserMgmt
        UserMgmt -- 8.通知用户 --> User
        User -- 9.查看违规详情 --> ViolationCenter
        ViolationCenter -- 10.提交申诉 --> Database
        Database -- 11.更新违规状态 --> ViolationAdmin
        ViolationAdmin -- 12.处理申诉 --> Database
        Database -- 13.申诉结果通知 --> User
    end
    
    %% 模块间数据交互
    VehicleMgmt <--> ParkingCenter
    ParkingCenter <--> ChargingCenter
    VehicleMgmt <--> ViolationCenter
    UserMgmt <--> ViolationCenter
    
    %% 管理模块交互
    UserAdmin <--> UserMgmt
    VehicleAdmin <--> VehicleMgmt
    ParkingAdmin <--> ParkingCenter
    ChargingAdmin <--> ChargingCenter
    ViolationAdmin <--> ViolationCenter
    
    %% 样式定义
    classDef userModule fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef adminModule fill:#ffcccc,stroke:#333,stroke-width:1px
    classDef dataFlow fill:#f9f9f9,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5
    classDef database fill:#f5f5f5,stroke:#333,stroke-width:1px
    classDef actor fill:#e6e6e6,stroke:#333,stroke-width:2px,shape:circle
    
    class Auth,UserMgmt,Dashboard,VehicleMgmt,ParkingCenter,ChargingCenter,ViolationCenter userModule
    class AdminPanel,UserAdmin,VehicleAdmin,ParkingAdmin,ChargingAdmin,ViolationAdmin,AnnouncementAdmin adminModule
    class Database database
    class User,Admin actor
```
