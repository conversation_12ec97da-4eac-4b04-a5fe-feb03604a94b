```mermaid
flowchart TD
    %% 简化的用户信息管理流程图
    Start([开始]) --> AdminLogin[管理员登录]
    AdminLogin --> AccessUserMgmt[访问用户管理页面]
    
    %% 主要功能分支
    AccessUserMgmt --> ViewUserList[查看用户列表]
    
    %% 用户查询功能
    ViewUserList --> SearchUsers[搜索/筛选用户]
    
    %% 用户详情与编辑
    ViewUserList --> ViewUserDetail[查看用户详情]
    ViewUserDetail --> EditUser[编辑用户信息]
    EditUser --> ValidateUserData{验证数据}
    ValidateUserData -->|有效| SaveUser[保存用户信息]
    ValidateUserData -->|无效| EditUser
    
    %% 用户角色管理
    ViewUserDetail --> ManageUserRole[管理用户角色]
    ManageUserRole --> UpdateUserRole[更新用户角色]
    
    %% 创建新用户
    ViewUserList --> CreateNewUser[创建新用户]
    CreateNewUser --> SaveNewUser[保存新用户]
    
    %% 删除用户
    ViewUserDetail --> DeleteUser[删除用户]
    DeleteUser --> ConfirmDeletion{确认删除}
    ConfirmDeletion -->|确认| RemoveUser[移除用户]
    ConfirmDeletion -->|取消| ViewUserDetail
    
    %% 批量操作
    ViewUserList --> BatchOperations[批量操作]
    BatchOperations --> BatchDelete[批量删除]
    BatchOperations --> BatchRoleUpdate[批量角色更新]
    
    %% 数据导出
    ViewUserList --> ExportUserData[导出用户数据]
    
    %% 样式定义
    classDef start fill:#f9d5e5,stroke:#333,stroke-width:2px
    classDef process fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef decision fill:#ffffcc,stroke:#333,stroke-width:1px
    
    class Start start
    class AdminLogin,AccessUserMgmt,ViewUserList,SearchUsers,ViewUserDetail,EditUser,SaveUser,ManageUserRole,UpdateUserRole,CreateNewUser,SaveNewUser,RemoveUser,BatchOperations,BatchDelete,BatchRoleUpdate,ExportUserData process
    class ValidateUserData,ConfirmDeletion decision
```
