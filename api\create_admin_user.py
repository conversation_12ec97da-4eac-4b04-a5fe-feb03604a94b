"""
<PERSON><PERSON><PERSON> to create a default admin user in the Users table
"""
from app import db, create_app
from app.users.models import Users
import hashlib

def create_admin_user():
    """Create a default admin user in the Users table"""
    app = create_app()
    with app.app_context():
        # Check if admin user exists
        admin_user = Users.query.filter_by(u_name='admin').first()
        if not admin_user:
            print("Creating admin user...")
            # Generate a salt for the admin user
            salt = "admin-salt-123456"
            # Hash the password with the salt
            hashed_password = hashlib.sha256(f"111111{salt}".encode()).hexdigest()
            
            # Create the admin user
            admin_user = Users(
                u_name='admin',
                u_pwd=hashed_password,
                u_role='admin',
                u_belong='System',
                u_phone='13800138000'
            )
            admin_user.salt = salt
            db.session.add(admin_user)
            db.session.commit()
            print(f"Admin user created with ID: {admin_user.u_id}")
        else:
            print(f"Admin user already exists with ID: {admin_user.u_id}")
            # Update the admin user's password
            salt = admin_user.salt or "admin-salt-123456"
            hashed_password = hashlib.sha256(f"111111{salt}".encode()).hexdigest()
            admin_user.u_pwd = hashed_password
            admin_user.salt = salt
            db.session.commit()
            print(f"Admin user password updated")

if __name__ == "__main__":
    create_admin_user()
    print("Admin user creation/update completed successfully")
