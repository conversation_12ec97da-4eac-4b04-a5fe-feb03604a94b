"""
修复充电故障和充电异常记录之间的关联
"""
import os
import sys
from datetime import datetime

# 添加父目录到系统路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
sys.path.insert(0, parent_dir)

# 导入应用和模型
from app import create_app, db
from app.charging.models import ChargingFault, ChargingException, ChargingRecord
from app.users.models import Users
from app.bikes.models import Bikes
from app.parkinglots.models import ParkingSpace, ParkingLot
from app.parking_records.models import ParkingRecord

# 创建应用实例
app = create_app()

def fix_charging_exceptions():
    """修复充电故障和充电异常记录之间的关联"""
    with app.app_context():
        print("开始修复充电故障和充电异常记录之间的关联...")

        # 获取所有充电故障记录
        faults = ChargingFault.query.all()
        print(f"找到 {len(faults)} 条充电故障记录")

        # 遍历每个故障记录
        for fault in faults:
            print(f"\n处理故障记录 ID: {fault.id}, 车位: {fault.space_id}, 类型: {fault.fault_type}")

            # 查找与该故障关联的充电异常记录
            exceptions = ChargingException.query.filter_by(fault_id=fault.id).all()

            if exceptions:
                print(f"  已找到 {len(exceptions)} 条关联的充电异常记录")
                continue

            # 如果没有找到关联的异常记录，尝试通过车位ID查找
            exceptions = ChargingException.query.filter_by(space_id=fault.space_id).all()

            if exceptions:
                print(f"  找到 {len(exceptions)} 条与车位相关的充电异常记录，正在关联...")
                for exception in exceptions:
                    exception.fault_id = fault.id
                    print(f"  已将异常记录 ID: {exception.id} 关联到故障 ID: {fault.id}")
                db.session.commit()
                continue

            # 如果仍然没有找到异常记录，创建一个新的异常记录
            print(f"  未找到关联的充电异常记录，创建新记录...")

            # 准备异常描述
            exception_type = fault.fault_type
            description = f"充电故障报修：{fault.fault_description}（报修人：{fault.reporter_name}）"

            # 获取故障严重程度信息
            severity_info = ""
            if fault.severity == 'low':
                severity_info = "轻微"
            elif fault.severity == 'medium':
                severity_info = "一般"
            elif fault.severity == 'high':
                severity_info = "严重"
            elif fault.severity == 'critical':
                severity_info = "紧急"

            # 添加严重程度到描述
            description += f"，严重程度：{severity_info}"

            # 添加车位信息到描述
            space = ParkingSpace.query.get(fault.space_id)
            if space:
                description += f"，车位号：{space.space_number}"

            # 查找与该车位相关的充电记录
            charging_record = ChargingRecord.query.filter_by(
                parking_space_id=fault.space_id
            ).order_by(ChargingRecord.start_time.desc()).first()

            if charging_record:
                print(f"  找到与车位相关的充电记录 ID: {charging_record.id}")

                # 创建充电异常记录
                new_exception = ChargingException(
                    exception_type=exception_type,
                    description=description,
                    charging_record_id=charging_record.id,
                    fault_id=fault.id,
                    space_id=fault.space_id,
                    parking_lot_id=fault.parking_lot_id
                )

                db.session.add(new_exception)
                db.session.commit()
                print(f"  已创建充电异常记录 ID: {new_exception.id}")
            else:
                print(f"  未找到与车位相关的充电记录，创建临时记录...")

                # 查找一个用户和车辆来创建临时记录
                admin_user = Users.query.filter_by(u_role=2).first()  # 假设角色2是管理员
                if not admin_user:
                    admin_user = Users.query.first()

                vehicle = None
                if admin_user:
                    vehicle = Bikes.query.filter_by(belong_to=admin_user.u_id).first()

                if not vehicle:
                    vehicle = Bikes.query.first()

                if admin_user and vehicle:
                    # 创建临时停车记录
                    temp_parking_record = ParkingRecord(
                        vehicle_id=vehicle.b_id,
                        user_id=admin_user.u_id,
                        parking_lot_id=fault.parking_lot_id,
                        parking_space_id=fault.space_id
                    )

                    # 设置备注，标记为临时记录
                    temp_parking_record.remarks = f"临时记录，由故障修复脚本自动创建，故障ID: {fault.id}"

                    # 保存停车记录
                    db.session.add(temp_parking_record)
                    db.session.flush()  # 获取ID

                    # 创建临时充电记录
                    temp_charging_record = ChargingRecord(
                        parking_record_id=temp_parking_record.id,
                        vehicle_id=vehicle.b_id,
                        user_id=admin_user.u_id,
                        parking_lot_id=fault.parking_lot_id,
                        parking_space_id=fault.space_id
                    )

                    # 设置备注，标记为临时记录
                    temp_charging_record.remarks = f"临时记录，由故障修复脚本自动创建，故障ID: {fault.id}"

                    # 保存充电记录
                    db.session.add(temp_charging_record)
                    db.session.flush()  # 获取ID

                    print(f"  临时充电记录创建成功，ID: {temp_charging_record.id}")

                    # 创建充电异常记录
                    new_exception = ChargingException(
                        exception_type=exception_type,
                        description=description + "（由系统自动创建的临时记录）",
                        charging_record_id=temp_charging_record.id,
                        fault_id=fault.id,
                        space_id=fault.space_id,
                        parking_lot_id=fault.parking_lot_id
                    )

                    db.session.add(new_exception)
                    db.session.commit()
                    print(f"  已创建充电异常记录 ID: {new_exception.id}")
                else:
                    print("  无法找到用户或车辆来创建临时记录")

                    # 即使没有充电记录，也创建一个异常记录
                    new_exception = ChargingException(
                        exception_type=exception_type,
                        description=description + "（由系统自动创建，无关联充电记录）",
                        charging_record_id=None,  # 无关联充电记录
                        fault_id=fault.id,
                        space_id=fault.space_id,
                        parking_lot_id=fault.parking_lot_id
                    )

                    db.session.add(new_exception)
                    db.session.commit()
                    print(f"  已创建无关联充电记录的异常记录 ID: {new_exception.id}")

        print("\n修复完成！")

if __name__ == '__main__':
    fix_charging_exceptions()
