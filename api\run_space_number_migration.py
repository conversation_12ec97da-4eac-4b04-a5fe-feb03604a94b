#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
车位号格式迁移脚本
将所有车位号统一为按车位类型的命名格式：{类型前缀}-{停车场ID}-{序号}
"""

import os
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"space_number_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入应用和数据库模型
from app import create_app
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.parkinglots.utils import TYPE_PREFIX_MAP
from app import db

app = create_app()

def migrate_space_numbers():
    """迁移所有车位号为新格式"""
    with app.app_context():
        try:
            logger.info("开始迁移车位号格式...")
            
            # 获取所有停车场
            parking_lots = ParkingLot.query.all()
            logger.info(f"找到 {len(parking_lots)} 个停车场")
            
            total_updated = 0
            
            # 遍历每个停车场
            for lot in parking_lots:
                logger.info(f"处理停车场: {lot.name} (ID: {lot.id})")
                
                # 获取该停车场的所有车位
                spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id).all()
                logger.info(f"  找到 {len(spaces)} 个车位")
                
                # 按类型分组车位
                spaces_by_type = {}
                for space in spaces:
                    if space.type not in spaces_by_type:
                        spaces_by_type[space.type] = []
                    spaces_by_type[space.type].append(space)
                
                # 遍历每种类型的车位
                for type_id, type_spaces in spaces_by_type.items():
                    prefix = TYPE_PREFIX_MAP.get(type_id, 'N')
                    logger.info(f"  处理类型 {type_id} (前缀: {prefix}) 的车位, 共 {len(type_spaces)} 个")
                    
                    # 按序号排序车位
                    type_spaces.sort(key=lambda s: s.id)
                    
                    # 更新每个车位的编号
                    for i, space in enumerate(type_spaces, 1):
                        old_number = space.space_number
                        new_number = f"{prefix}-{lot.id}-{i}"
                        
                        # 检查是否需要更新
                        if old_number != new_number:
                            # 更新车位号
                            space.space_number = new_number
                            total_updated += 1
                            
                            logger.info(f"    更新车位: {old_number} -> {new_number}")
                        else:
                            logger.info(f"    车位号已符合规范，无需更新: {old_number}")
            
            # 提交更改
            if total_updated > 0:
                db.session.commit()
                logger.info(f"成功更新 {total_updated} 个车位号")
            else:
                logger.info("所有车位号已符合规范，无需更新")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"迁移车位号失败: {str(e)}")
            raise
        
        logger.info("车位号格式迁移完成")

if __name__ == '__main__':
    migrate_space_numbers()
