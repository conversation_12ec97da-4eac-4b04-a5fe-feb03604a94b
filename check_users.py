#!/usr/bin/env python
import sqlite3
import os
import hashlib

def main():
    """显示所有用户信息"""
    # 获取数据库路径
    db_path = os.path.join('api', 'sys.db')
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件 {db_path} 不存在")
        return
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查询所有用户
    cursor.execute("SELECT u_id, u_name, u_pwd, salt, u_role, u_belong, u_phone, u_email FROM users ORDER BY u_id")
    users = cursor.fetchall()
    
    # 关闭连接
    conn.close()
    
    # 显示用户信息
    print("\n系统中的所有用户信息：")
    print("=" * 100)
    print("ID | 用户名 | 密码哈希 | 盐值 | 角色 | 所属部门 | 电话 | 邮箱")
    print("-" * 100)
    
    for user in users:
        user_id, username, pwd_hash, salt, role, belong, phone, email = user
        # 截断密码哈希以便于显示
        short_hash = pwd_hash[:10] + "..." if pwd_hash else "无"
        print(f"{user_id} | {username} | {short_hash} | {salt} | {role} | {belong or '无'} | {phone or '无'} | {email or '无'}")
    
    print("=" * 100)
    
    # 提示用户可以使用的默认密码
    print("\n注意: 系统中可能存在以下默认密码:")
    print("- admin 用户: 111111 或 admin123")
    print("- security 用户: 111111 或 security123")
    print("- 普通用户: 111111 或 user123")
    print("- superadmin 用户: 123456")

if __name__ == "__main__":
    main()
