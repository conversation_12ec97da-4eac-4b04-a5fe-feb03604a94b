#!/usr/bin/env python
import sqlite3
import os
import hashlib
import uuid
from datetime import datetime

# 获取数据库路径
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sys.db')
print("数据库路径:", db_path)
print("数据库文件是否存在:", os.path.exists(db_path))

try:
    # 连接到数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    print("成功连接到数据库\n")
    
    # 检查是否已存在admin用户
    cursor.execute("SELECT * FROM users WHERE u_name='admin'")
    admin = cursor.fetchone()
    
    if admin:
        print(f"管理员用户已存在 (ID: {admin[0]})")
        # 更新为管理员角色
        salt = admin[6]  # 获取现有盐值
        password = "admin123"  # 设置新密码
        hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
        
        cursor.execute(
            "UPDATE users SET u_pwd=?, u_role='admin' WHERE u_name='admin'", 
            (hashed_password,)
        )
        conn.commit()
        print(f"已将用户 'admin' 更新为管理员角色，密码设置为: {password}")
    else:
        # 创建管理员用户
        salt = str(uuid.uuid4())
        password = "admin123"  # 设置密码
        hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
        now = datetime.now().isoformat()
        
        cursor.execute(
            "INSERT INTO users (u_name, u_pwd, u_role, u_belong, u_phone, salt, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            ("admin", hashed_password, "admin", "系统管理", "13800000000", salt, now, now)
        )
        conn.commit()
        
        # 获取新创建的用户ID
        cursor.execute("SELECT u_id FROM users WHERE u_name='admin'")
        admin_id = cursor.fetchone()[0]
        print(f"已创建管理员用户 'admin'，ID: {admin_id}，密码: {password}")
    
    # 查询所有管理员用户
    cursor.execute("SELECT * FROM users WHERE u_role='admin'")
    admins = cursor.fetchall()
    
    print(f"\n管理员用户列表 ({len(admins)}个):")
    for admin in admins:
        print(f"ID: {admin[0]}, 用户名: {admin[1]}, 角色: {admin[3]}, 部门: {admin[4]}, 电话: {admin[5]}")
    
    # 关闭连接
    conn.close()
    print("\n操作完成，数据库连接已关闭")
    
except Exception as e:
    print(f"错误: {e}")
