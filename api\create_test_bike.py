#!/usr/bin/env python
import os
import sys
from datetime import datetime

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app import create_app, db
from app.bikes.models import Bikes
from app.users.models import Users

def create_test_bike():
    """创建测试车辆"""
    app = create_app()
    
    with app.app_context():
        # 查找admin用户
        admin_user = Users.query.filter_by(u_role='admin').first()
        
        if not admin_user:
            print("未找到admin用户，请先创建admin用户")
            return
            
        # 生成唯一车牌号
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        bike_number = f"TEST-{timestamp}"
        
        # 检查车辆是否已存在
        existing_bike = Bikes.query.filter_by(b_num=bike_number).first()
        if existing_bike:
            print(f"车辆 {bike_number} 已存在")
            return existing_bike
            
        # 创建新车辆
        new_bike = Bikes(
            belong_to=admin_user.u_id,
            b_num=bike_number,
            brand="测试品牌",
            color="蓝色",
            b_type="电动车",
            status="可用"
        )
        
        try:
            db.session.add(new_bike)
            db.session.commit()
            print(f"成功创建测试车辆: {bike_number}, ID: {new_bike.b_id}, 所属用户ID: {new_bike.belong_to}")
            return new_bike
        except Exception as e:
            db.session.rollback()
            print(f"创建车辆失败: {str(e)}")
            return None

if __name__ == "__main__":
    bike = create_test_bike()
    if bike:
        print(f"测试车辆创建成功: ID={bike.b_id}, 车牌号={bike.b_num}")
    else:
        print("测试车辆创建失败")
