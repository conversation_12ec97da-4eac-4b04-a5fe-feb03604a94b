"""
简单的车辆禁用记录表结构更新脚本

此脚本直接使用SQLite命令更新车辆禁用记录表结构，避免循环导入问题。
"""

import sqlite3
import logging
import os
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库文件路径
DB_PATH = 'sys.db'

def check_column_exists(conn, table_name, column_name):
    """检查列是否存在"""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def add_column(conn, table_name, column_name, column_type):
    """添加列"""
    try:
        # 检查列是否已存在
        if check_column_exists(conn, table_name, column_name):
            logger.info(f"列 {column_name} 已存在于表 {table_name} 中，跳过")
            return True
        
        # 添加列
        cursor = conn.cursor()
        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
        logger.info(f"成功添加列 {column_name} 到表 {table_name}")
        return True
    except Exception as e:
        logger.error(f"添加列 {column_name} 到表 {table_name} 失败: {str(e)}")
        return False

def update_vehicle_disable_table():
    """更新车辆禁用记录表结构"""
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            logger.error(f"数据库文件 {DB_PATH} 不存在")
            return False
        
        # 连接数据库
        conn = sqlite3.connect(DB_PATH)
        
        # 检查表是否存在
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='vehicle_disable_records'")
        if not cursor.fetchone():
            logger.error("表 vehicle_disable_records 不存在，请先创建表")
            conn.close()
            return False
        
        # 添加新列
        columns_to_add = [
            ("disable_end_time", "DATETIME"),
            ("reason", "TEXT"),
            ("operator_id", "INTEGER"),
            ("enable_reason", "TEXT"),
            ("enable_operator_id", "INTEGER")
        ]
        
        success = True
        for column_name, column_type in columns_to_add:
            if not add_column(conn, "vehicle_disable_records", column_name, column_type):
                success = False
        
        # 修改violation_id列为可空
        # SQLite不支持直接修改列属性，需要创建新表并复制数据
        # 这里我们不执行这个操作，因为它比较复杂，可能会导致数据丢失
        # 我们只是记录一下，提醒用户手动处理
        logger.warning("SQLite不支持直接修改列属性，如果需要将violation_id列设为可空，请手动处理")
        
        # 关闭连接
        conn.close()
        
        return success
    except Exception as e:
        logger.error(f"更新车辆禁用记录表结构失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始更新车辆禁用记录表结构...")
    
    if update_vehicle_disable_table():
        print("车辆禁用记录表结构更新成功")
    else:
        print("车辆禁用记录表结构更新失败，请查看日志")
        sys.exit(1)
    
    # 检查更新后的表结构
    print("\n更新后的表结构:")
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(vehicle_disable_records)")
    for row in cursor.fetchall():
        print(f"列名: {row[1]}, 类型: {row[2]}, 是否可空: {row[3] == 0}")
    conn.close()
    
    print("\n更新完成")
