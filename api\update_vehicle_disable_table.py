"""
更新车辆禁用记录表结构

此脚本用于更新车辆禁用记录表结构，添加新字段。
"""

from app import create_app, db
from app.violations.models import VehicleDisableRecord
from app.bikes.models import Bikes
from flask import current_app
import logging
from sqlalchemy import Column, Integer, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.sql import text
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = create_app()

def check_column_exists(table_name, column_name):
    """检查列是否存在"""
    with db.engine.connect() as conn:
        # 使用PRAGMA table_info查询表结构（SQLite特有）
        result = conn.execute(text(f"PRAGMA table_info({table_name})"))
        columns = [row[1] for row in result]
        return column_name in columns

def add_column(table_name, column_name, column_type):
    """添加列"""
    try:
        with db.engine.connect() as conn:
            # 检查列是否已存在
            if check_column_exists(table_name, column_name):
                logger.info(f"列 {column_name} 已存在于表 {table_name} 中，跳过")
                return True
            
            # 添加列
            conn.execute(text(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"))
            logger.info(f"成功添加列 {column_name} 到表 {table_name}")
            return True
    except Exception as e:
        logger.error(f"添加列 {column_name} 到表 {table_name} 失败: {str(e)}")
        return False

def update_vehicle_disable_table():
    """更新车辆禁用记录表结构"""
    try:
        # 检查表是否存在
        with db.engine.connect() as conn:
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='vehicle_disable_records'"))
            if not result.fetchone():
                logger.error("表 vehicle_disable_records 不存在，请先创建表")
                return False
        
        # 添加新列
        columns_to_add = [
            ("disable_end_time", "DATETIME"),
            ("reason", "TEXT"),
            ("operator_id", "INTEGER"),
            ("enable_reason", "TEXT"),
            ("enable_operator_id", "INTEGER")
        ]
        
        success = True
        for column_name, column_type in columns_to_add:
            if not add_column("vehicle_disable_records", column_name, column_type):
                success = False
        
        # 修改violation_id列为可空
        # SQLite不支持直接修改列属性，需要创建新表并复制数据
        # 这里我们不执行这个操作，因为它比较复杂，可能会导致数据丢失
        # 我们只是记录一下，提醒用户手动处理
        logger.warning("SQLite不支持直接修改列属性，如果需要将violation_id列设为可空，请手动处理")
        
        return success
    except Exception as e:
        logger.error(f"更新车辆禁用记录表结构失败: {str(e)}")
        return False

if __name__ == "__main__":
    with app.app_context():
        print("开始更新车辆禁用记录表结构...")
        
        if update_vehicle_disable_table():
            print("车辆禁用记录表结构更新成功")
        else:
            print("车辆禁用记录表结构更新失败，请查看日志")
            sys.exit(1)
        
        # 检查更新后的表结构
        print("\n更新后的表结构:")
        with db.engine.connect() as conn:
            result = conn.execute(text("PRAGMA table_info(vehicle_disable_records)"))
            for row in result:
                print(f"列名: {row[1]}, 类型: {row[2]}, 是否可空: {row[3] == 0}")
        
        print("\n更新完成")
