#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复Flask应用上下文问题的脚本

这个脚本检查并修复可能导致"Working outside of application context"错误的问题。
"""

import os
import sys
import re
from pathlib import Path

def find_problematic_files():
    """查找可能存在问题的Python文件"""
    api_dir = Path(".")
    problematic_files = []
    
    patterns = [
        r"current_app\.[a-zA-Z_]+",  # 使用current_app
        r"request\.[a-zA-Z_]+",      # 使用request
        r"session\.[a-zA-Z_]+",      # 使用session
        r"g\.[a-zA-Z_]+"             # 使用g
    ]
    
    # 编译正则表达式
    compiled_patterns = [re.compile(pattern) for pattern in patterns]
    
    # 遍历所有Python文件
    for py_file in api_dir.glob("**/*.py"):
        # 跳过__pycache__目录
        if "__pycache__" in str(py_file):
            continue
            
        with open(py_file, "r", encoding="utf-8") as f:
            content = f.read()
            
        # 检查是否在模块级别使用了Flask上下文对象
        lines = content.split("\n")
        for i, line in enumerate(lines):
            # 跳过函数或类定义内的代码
            if re.match(r"^\s*(def|class)\s+", line):
                continue
                
            # 跳过注释
            if line.strip().startswith("#"):
                continue
                
            # 检查是否匹配任何模式
            for pattern in compiled_patterns:
                if pattern.search(line) and "with app.app_context()" not in line and "def " not in line:
                    problematic_files.append((py_file, i+1, line.strip()))
                    break
    
    return problematic_files

def fix_app_py():
    """修复app.py文件，确保正确初始化应用"""
    app_py_path = Path("app.py")
    
    if not app_py_path.exists():
        print("未找到app.py文件")
        return False
        
    with open(app_py_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查是否已经有eventlet.monkey_patch()
    if "eventlet.monkey_patch()" not in content:
        # 添加eventlet.monkey_patch()
        new_content = "import eventlet\neventlet.monkey_patch()\n\n" + content
        
        with open(app_py_path, "w", encoding="utf-8") as f:
            f.write(new_content)
            
        print("已添加eventlet.monkey_patch()到app.py")
    else:
        print("app.py已包含eventlet.monkey_patch()")
    
    return True

def fix_main_py():
    """修复main.py文件，确保正确初始化应用"""
    main_py_path = Path("main.py")
    
    if not main_py_path.exists():
        print("未找到main.py文件")
        return False
        
    with open(main_py_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查是否已经有eventlet.monkey_patch()
    if "eventlet.monkey_patch()" not in content:
        # 添加eventlet.monkey_patch()
        new_content = "import eventlet\neventlet.monkey_patch()\n\n" + content
        
        with open(main_py_path, "w", encoding="utf-8") as f:
            f.write(new_content)
            
        print("已添加eventlet.monkey_patch()到main.py")
    else:
        print("main.py已包含eventlet.monkey_patch()")
    
    return True

def create_run_script():
    """创建一个正确的运行脚本"""
    run_script_path = Path("run.py")
    
    run_script_content = """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
正确启动Flask应用的脚本
\"\"\"

import eventlet
eventlet.monkey_patch()

from app import create_app

app = create_app()

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000, debug=True)
"""
    
    with open(run_script_path, "w", encoding="utf-8") as f:
        f.write(run_script_content)
        
    print("已创建run.py脚本，请使用 'python run.py' 启动应用")
    
    return True

def main():
    """主函数"""
    print("开始检查和修复Flask应用上下文问题...")
    
    # 查找可能存在问题的文件
    problematic_files = find_problematic_files()
    
    if problematic_files:
        print("\n发现可能存在问题的文件:")
        for file_path, line_num, line_content in problematic_files:
            print(f"  {file_path}:{line_num} - {line_content}")
        
        print("\n这些文件可能在模块级别使用了Flask上下文对象，请确保它们只在函数内部或应用上下文中使用。")
    else:
        print("未发现在模块级别使用Flask上下文对象的问题。")
    
    # 修复app.py
    fix_app_py()
    
    # 修复main.py
    fix_main_py()
    
    # 创建运行脚本
    create_run_script()
    
    print("\n修复完成。请尝试使用 'python run.py' 启动应用。")

if __name__ == "__main__":
    main()
