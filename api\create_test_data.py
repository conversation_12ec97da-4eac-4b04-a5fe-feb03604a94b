#!/usr/bin/env python
"""
创建测试数据脚本
用于生成测试用户、车辆和违规记录，以测试违规录入功能
"""

import os
import sys
import random
from datetime import datetime, timedelta
import hashlib
import uuid

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入应用和模型
from app import create_app, db
from app.users.models import Users
from app.bikes.models import Bikes
from app.violations.models import ViolationRecord

# 创建测试数据
def create_test_data():
    app = create_app()
    with app.app_context():
        print("开始创建测试数据...")
        
        # 检查是否已有测试数据
        if Users.query.filter_by(u_name='test_user').first():
            print("测试数据已存在，跳过创建")
            return
        
        # 创建测试用户
        test_users = create_test_users()
        
        # 创建测试车辆
        test_bikes = create_test_bikes(test_users)
        
        # 创建测试违规记录
        create_test_violations(test_users, test_bikes)
        
        print("测试数据创建完成！")

# 创建测试用户
def create_test_users():
    print("创建测试用户...")
    test_users = []
    
    # 创建不同角色的测试用户
    user_data = [
        {'u_name': 'test_user', 'u_role': 'user', 'u_belong': '计算机学院'},
        {'u_name': 'test_security', 'u_role': 'security', 'u_belong': '保卫处'},
        {'u_name': 'test_admin', 'u_role': 'admin', 'u_belong': '管理部门'}
    ]
    
    for data in user_data:
        # 生成盐值
        salt = str(uuid.uuid4())
        # 使用相同的密码 "password123"
        password = "password123"
        hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
        
        user = Users(
            u_name=data['u_name'],
            u_pwd=hashed_password,
            salt=salt,
            u_role=data['u_role'],
            u_belong=data['u_belong'],
            u_phone=f"1380000{random.randint(1000, 9999)}",
            u_email=f"{data['u_name']}@example.com"
        )
        
        db.session.add(user)
        db.session.commit()
        test_users.append(user)
        print(f"创建用户: {user.u_name}, ID: {user.u_id}, 角色: {user.u_role}")
    
    return test_users

# 创建测试车辆
def create_test_bikes(users):
    print("创建测试车辆...")
    test_bikes = []
    
    # 为每个用户创建车辆
    for user in users:
        if user.u_role == 'user':  # 只为普通用户创建车辆
            # 创建2辆车
            for i in range(2):
                bike = Bikes(
                    b_num=f"TEST-{user.u_id}-{i+1}",
                    brand=random.choice(['雅迪', '爱玛', '小牛', '绿源', '新日']),
                    color=random.choice(['红色', '蓝色', '黑色', '白色', '灰色']),
                    b_type=random.choice(['电动自行车', '电动摩托车']),
                    status='可用',
                    belong_to=user.u_id
                )
                
                db.session.add(bike)
                db.session.commit()
                test_bikes.append(bike)
                print(f"创建车辆: {bike.b_num}, ID: {bike.b_id}, 所属用户: {user.u_name}")
    
    return test_bikes

# 创建测试违规记录
def create_test_violations(users, bikes):
    print("创建测试违规记录...")
    
    # 找到保安用户
    security_user = next((user for user in users if user.u_role == 'security'), None)
    if not security_user:
        print("未找到保安用户，跳过创建违规记录")
        return
    
    # 违规类型列表
    violation_types = [
        '违规停车', '占用消防通道', '占用无障碍通道', 
        '超时停车', '车辆损坏公物', '无证驾驶', '其他违规'
    ]
    
    # 违规地点列表
    locations = [
        '校园南门', '图书馆前', '教学楼A区', '食堂门口', 
        '宿舍区', '体育馆', '实验楼', '行政楼'
    ]
    
    # 为每辆车创建违规记录
    for bike in bikes:
        # 随机决定是否创建违规记录
        if random.random() < 0.7:  # 70%的概率创建违规记录
            # 随机生成违规时间（过去30天内）
            days_ago = random.randint(1, 30)
            violation_time = datetime.now() - timedelta(days=days_ago)
            
            violation = ViolationRecord(
                bike_number=bike.b_num,
                bike_id=bike.b_id,
                user_id=bike.belong_to,
                violation_time=violation_time,
                location=random.choice(locations),
                violation_type=random.choice(violation_types),
                description=f"这是一条测试违规记录，车辆 {bike.b_num} 在 {violation_time.strftime('%Y-%m-%d %H:%M')} 违规。",
                status=random.choice([0, 1]),  # 0-未处理，1-已处理
                recorder_id=security_user.u_id
            )
            
            db.session.add(violation)
            db.session.commit()
            print(f"创建违规记录: ID: {violation.id}, 车辆: {bike.b_num}, 类型: {violation.violation_type}")

# 主函数
if __name__ == "__main__":
    create_test_data()
