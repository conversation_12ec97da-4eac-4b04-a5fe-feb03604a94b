"""
运行充电相关表的迁移脚本
"""
import os
import sys
import sqlite3
from migrations.add_charging_tables import upgrade as upgrade_tables, downgrade as downgrade_tables
from migrations.add_charging_power_fields import upgrade as upgrade_power_fields, downgrade as downgrade_power_fields

def check_database():
    """检查数据库连接"""
    try:
        # 获取数据库文件路径
        db_path = os.path.join(os.path.dirname(__file__), 'sys.db')
        print(f"数据库文件路径: {db_path}")

        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            print("警告: 数据库文件不存在!")
            return False

        # 尝试连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查数据库中的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"数据库中的表: {[table[0] for table in tables]}")

        # 关闭连接
        conn.close()
        return True
    except Exception as e:
        print(f"数据库检查失败: {str(e)}")
        return False

if __name__ == '__main__':
    # 检查数据库
    if not check_database():
        print("数据库检查失败，请确保数据库文件存在并且可以访问")
        sys.exit(1)

    # 根据命令行参数执行升级或降级
    if len(sys.argv) > 1 and sys.argv[1] == 'downgrade':
        print("开始执行数据库降级...")
        print("1. 降级充电功率字段...")
        downgrade_power_fields()
        print("2. 降级充电表结构...")
        downgrade_tables()
    else:
        print("开始执行数据库升级...")
        print("1. 升级充电表结构...")
        upgrade_tables()
        print("2. 升级充电功率字段...")
        upgrade_power_fields()
