from app import create_app
app = create_app()
from app.violations.models import VehicleDisableRecord, ViolationRecord
from app.bikes.models import Bikes
from flask import current_app
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

with app.app_context():
    print('查询车辆禁用记录:')
    disable_records = VehicleDisableRecord.query.all()
    print(f'总共有 {len(disable_records)} 条车辆禁用记录')
    for record in disable_records[:5]:
        print(f'ID: {record.id}, 车辆ID: {record.bike_id}, 违规ID: {record.violation_id}, 是否激活: {record.is_active}')
    
    print('\n查询车牌号为111的车辆:')
    bike = Bikes.query.filter_by(b_num='111').first()
    if bike:
        print(f'车辆ID: {bike.b_id}, 车牌号: {bike.b_num}, 状态: {bike.status}, 所属用户: {bike.belong_to}')
        
        # 查询该车辆的禁用记录
        bike_disable_records = VehicleDisableRecord.query.filter_by(bike_id=bike.b_id).all()
        print(f'该车辆的禁用记录数量: {len(bike_disable_records)}')
        for record in bike_disable_records:
            print(f'禁用记录ID: {record.id}, 违规ID: {record.violation_id}, 是否激活: {record.is_active}, 开始时间: {record.disable_start_time}')
    else:
        print('未找到车牌号为111的车辆')
