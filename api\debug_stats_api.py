from flask import Flask, jsonify, request
from app import create_app
from app.parking_records.routes import get_parking_stats
import json

app = create_app()

# 在应用上下文中执行API调用
with app.app_context():
    # 模拟请求
    with app.test_request_context('/api/parking-records/stats?date_range=week'):
        # 调用API函数
        response = get_parking_stats()
        
        # 打印响应
        print("API响应状态码:", response.status_code)
        data = json.loads(response.get_data(as_text=True))
        print("\nAPI响应数据:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
        
        # 检查daily_stats数据
        if 'data' in data and 'daily_stats' in data['data']:
            daily_stats = data['data']['daily_stats']
            print("\ndaily_stats数据:")
            print(json.dumps(daily_stats, indent=2, ensure_ascii=False))
            
            # 检查是否有非零数据
            has_non_zero = False
            for day in daily_stats:
                if day.get('count', 0) > 0 or day.get('completed', 0) > 0 or day.get('active', 0) > 0:
                    has_non_zero = True
                    break
            
            print("\n是否有非零数据:", "是" if has_non_zero else "否")
        else:
            print("\n响应中没有daily_stats数据")
