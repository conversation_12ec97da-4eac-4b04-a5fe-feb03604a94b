#!/usr/bin/env python
import os
import sys
import sqlite3

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 创建Flask应用上下文
try:
    from app import create_app, db
    from app.users.models import Users
    
    app = create_app()
    
    print("通过Flask ORM查询数据库")
    with app.app_context():
        users = Users.query.all()
        print(f"找到 {len(users)} 个用户:")
        print("ID | 用户名 | 角色 | 所属部门 | 电话")
        print("-" * 70)
        for user in users:
            print(f"{user.u_id} | {user.u_name} | {user.u_role} | {user.u_belong or '无'} | {user.u_phone or '无'}")
    print("\n使用ORM查询完成")
except Exception as e:
    print(f"使用ORM查询失败: {e}")

# 直接使用sqlite3查询
try:
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sys.db')
    print("\n使用SQLite3直接查询")
    print("数据库路径:", db_path)
    print("数据库文件是否存在:", os.path.exists(db_path))

    # 连接到数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    print("成功连接到数据库\n")
    
    # 获取数据库中的表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print("数据库中的表:")
    for table in tables:
        print(f"- {table[0]}")
    print("")
    
    # 获取users表结构
    cursor.execute("PRAGMA table_info(users);")
    columns = cursor.fetchall()
    print("users表结构:")
    for col in columns:
        print(f"- {col[1]} ({col[2]}){' PRIMARY KEY' if col[5] else ''}")
    
    column_names = [col[1] for col in columns]
    
    # 打印所有用户记录
    print("\n所有用户记录:")
    
    # 动态生成查询语句，只包含存在的列
    select_columns = []
    expected_columns = ['u_id', 'u_name', 'u_role', 'u_belong', 'u_phone']
    
    for col in expected_columns:
        if col in column_names:
            select_columns.append(col)
    
    query = f"SELECT {', '.join(select_columns)} FROM users;"
    print(f"执行查询: {query}")
    
    cursor.execute(query)
    users = cursor.fetchall()
    
    if not users:
        print("没有找到任何用户记录。")
    else:
        print(f"共找到 {len(users)} 个用户记录：")
        # 打印列名
        print(" | ".join(select_columns))
        print("-" * 70)
        
        for user in users:
            # 打印用户数据
            user_data = []
            for i, value in enumerate(user):
                user_data.append(str(value) if value is not None else "无")
            print(" | ".join(user_data))
    
    # 关闭连接
    conn.close()
    print("\n数据库连接已关闭")
except Exception as e:
    print(f"连接数据库或执行查询时出错: {e}") 