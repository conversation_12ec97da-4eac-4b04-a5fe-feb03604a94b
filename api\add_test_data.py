#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime, timedelta
import random

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入应用和数据库
from app import create_app, db
from app.charging.models import ChargingRecord, ChargingException, ChargingFault
from app.parkinglots.models import ParkingSpace, ParkingLot
from app.bikes.models import Bikes
from app.users.models import Users

# 创建应用上下文
app = create_app()
with app.app_context():
    # 检查是否已有充电异常记录
    existing_exceptions = ChargingException.query.count()
    if existing_exceptions > 0:
        print(f"数据库中已有 {existing_exceptions} 条充电异常记录，不添加测试数据")
        sys.exit(0)
    
    # 获取充电故障记录
    faults = ChargingFault.query.all()
    if not faults:
        print("数据库中没有充电故障记录，请先添加充电故障记录")
        sys.exit(1)
    
    # 获取用户
    user = Users.query.first()
    if not user:
        print("数据库中没有用户，请先添加用户")
        sys.exit(1)
    
    # 获取车辆
    vehicle = Bikes.query.first()
    if not vehicle:
        print("数据库中没有车辆，请先添加车辆")
        sys.exit(1)
    
    # 为每个充电故障创建充电记录和充电异常
    for fault in faults:
        # 获取故障对应的车位
        space = ParkingSpace.query.get(fault.space_id)
        if not space:
            print(f"找不到ID为 {fault.space_id} 的车位")
            continue
        
        # 获取故障对应的停车场
        parking_lot = ParkingLot.query.get(fault.parking_lot_id)
        if not parking_lot:
            print(f"找不到ID为 {fault.parking_lot_id} 的停车场")
            continue
        
        # 创建充电记录
        start_time = fault.report_time - timedelta(hours=random.randint(1, 5))
        charging_record = ChargingRecord(
            parking_record_id=1,  # 假设停车记录ID为1
            vehicle_id=vehicle.b_id,
            user_id=user.u_id,
            parking_lot_id=parking_lot.id,
            parking_space_id=space.id,
            power=7.0  # 充电功率
        )
        charging_record.start_time = start_time
        charging_record.status = 2  # 异常状态
        
        # 保存充电记录
        db.session.add(charging_record)
        db.session.flush()  # 获取ID
        
        # 创建充电异常记录
        exception_types = ['charging_gun', 'communication', 'power', 'software', 'user_operation', 'other']
        exception_type = random.choice(exception_types)
        
        exception = ChargingException(
            charging_record_id=charging_record.id,
            exception_type=exception_type,
            description=f"测试充电异常：{fault.fault_description}"
        )
        exception.time = fault.report_time
        
        # 保存充电异常记录
        db.session.add(exception)
    
    # 提交所有更改
    db.session.commit()
    
    # 输出结果
    exceptions_count = ChargingException.query.count()
    print(f"成功添加 {exceptions_count} 条充电异常记录")
