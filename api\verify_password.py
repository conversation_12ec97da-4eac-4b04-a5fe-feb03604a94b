#!/usr/bin/env python
import sqlite3
import hashlib
import sys

def verify_password(username, password):
    """验证用户密码"""
    # 连接数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    # 查询用户
    cursor.execute("SELECT u_id, u_name, u_pwd, salt FROM users WHERE u_name=?", (username,))
    user = cursor.fetchone()
    
    if not user:
        print(f"用户 {username} 不存在")
        conn.close()
        return False
    
    # 获取用户信息
    user_id, user_name, stored_hash, salt = user
    
    # 使用相同的盐值对输入的密码进行哈希
    calculated_hash = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
    
    # 比较哈希值
    is_match = calculated_hash == stored_hash
    
    # 显示验证信息
    print(f"用户: {user_name} (ID: {user_id})")
    print(f"盐值: {salt}")
    print(f"存储的密码哈希: {stored_hash[:10]}...")
    print(f"计算的密码哈希: {calculated_hash[:10]}...")
    print(f"密码验证结果: {'成功' if is_match else '失败'}")
    
    # 关闭连接
    conn.close()
    return is_match

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python verify_password.py <用户名> <密码>")
        return
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    verify_password(username, password)

if __name__ == "__main__":
    main()
