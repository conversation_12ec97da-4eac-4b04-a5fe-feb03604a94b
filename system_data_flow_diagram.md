```mermaid
flowchart TD
    %% 系统主要组件
    User([用户]) --- |访问|UI[用户界面层]
    UI --- |请求/响应|API[API层]
    API --- |CRUD操作|DB[(数据库)]
    
    %% 核心业务模块
    subgraph 核心业务模块
        UserModule[用户管理模块]
        VehicleModule[车辆管理模块]
        ParkingModule[停车中心模块]
        ChargingModule[充电中心模块]
        ViolationModule[违规中心模块]
        AnnouncementModule[公告管理模块]
    end
    
    %% 模块与API层的连接
    API --- UserModule
    API --- VehicleModule
    API --- ParkingModule
    API --- ChargingModule
    API --- ViolationModule
    API --- AnnouncementModule
    
    %% 数据流向详细说明
    subgraph 数据流向
        direction TB
        
        %% 用户注册/登录数据流
        User -- 1.提交登录信息 --> UI
        UI -- 2.发送认证请求 --> API
        API -- 3.验证凭据 --> UserModule
        UserModule -- 4.查询用户信息 --> DB
        DB -- 5.返回用户数据 --> UserModule
        UserModule -- 6.生成JWT令牌 --> API
        API -- 7.返回认证结果 --> UI
        UI -- 8.显示个人中心 --> User
        
        %% 车辆注册数据流
        User -- 1.提交车辆信息 --> UI
        UI -- 2.发送车辆注册请求 --> API
        API -- 3.验证用户权限 --> UserModule
        API -- 4.处理车辆数据 --> VehicleModule
        VehicleModule -- 5.存储车辆信息 --> DB
        DB -- 6.确认存储成功 --> VehicleModule
        VehicleModule -- 7.返回注册结果 --> API
        API -- 8.发送响应 --> UI
        UI -- 9.显示注册成功 --> User
        
        %% 停车操作数据流
        User -- 1.浏览停车场 --> UI
        UI -- 2.请求停车场数据 --> API
        API -- 3.获取停车场信息 --> ParkingModule
        ParkingModule -- 4.查询停车场和车位 --> DB
        DB -- 5.返回可用车位 --> ParkingModule
        ParkingModule -- 6.返回车位数据 --> API
        API -- 7.发送响应 --> UI
        UI -- 8.显示可用车位 --> User
        User -- 9.选择车辆和车位 --> UI
        UI -- 10.发送停车请求 --> API
        API -- 11.验证用户和车辆 --> VehicleModule
        VehicleModule -- 12.确认车辆信息 --> API
        API -- 13.创建停车记录 --> ParkingModule
        ParkingModule -- 14.更新车位状态 --> DB
        ParkingModule -- 15.保存停车记录 --> DB
        DB -- 16.确认更新成功 --> ParkingModule
        ParkingModule -- 17.返回停车结果 --> API
        API -- 18.发送响应 --> UI
        UI -- 19.显示停车成功 --> User
        
        %% 充电操作数据流
        User -- 1.查看停车记录 --> UI
        UI -- 2.请求停车数据 --> API
        API -- 3.获取用户停车记录 --> ParkingModule
        ParkingModule -- 4.查询进行中停车 --> DB
        DB -- 5.返回停车记录 --> ParkingModule
        ParkingModule -- 6.返回停车数据 --> API
        API -- 7.发送响应 --> UI
        UI -- 8.显示停车记录 --> User
        User -- 9.选择开始充电 --> UI
        UI -- 10.发送充电请求 --> API
        API -- 11.验证停车状态 --> ParkingModule
        ParkingModule -- 12.确认车位类型 --> API
        API -- 13.创建充电记录 --> ChargingModule
        ChargingModule -- 14.更新车位状态 --> DB
        ChargingModule -- 15.保存充电记录 --> DB
        DB -- 16.确认更新成功 --> ChargingModule
        ChargingModule -- 17.返回充电结果 --> API
        API -- 18.发送响应 --> UI
        UI -- 19.显示充电开始 --> User
        
        %% 违规处理数据流
        User -- 1.查看违规记录 --> UI
        UI -- 2.请求违规数据 --> API
        API -- 3.获取用户违规记录 --> ViolationModule
        ViolationModule -- 4.查询违规信息 --> DB
        DB -- 5.返回违规记录 --> ViolationModule
        ViolationModule -- 6.返回违规数据 --> API
        API -- 7.发送响应 --> UI
        UI -- 8.显示违规记录 --> User
        User -- 9.提交申诉 --> UI
        UI -- 10.发送申诉请求 --> API
        API -- 11.创建申诉记录 --> ViolationModule
        ViolationModule -- 12.更新违规状态 --> DB
        ViolationModule -- 13.保存申诉信息 --> DB
        DB -- 14.确认更新成功 --> ViolationModule
        ViolationModule -- 15.返回申诉结果 --> API
        API -- 16.发送响应 --> UI
        UI -- 17.显示申诉已提交 --> User
    end
    
    %% 模块间数据交互
    UserModule <--> VehicleModule
    VehicleModule <--> ParkingModule
    ParkingModule <--> ChargingModule
    VehicleModule <--> ViolationModule
    ViolationModule <--> UserModule
    AnnouncementModule --> UserModule
    
    %% 样式定义
    classDef userInterface fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef apiLayer fill:#ffffcc,stroke:#333,stroke-width:1px
    classDef moduleLayer fill:#e6f3e6,stroke:#333,stroke-width:1px
    classDef dataLayer fill:#f5f5f5,stroke:#333,stroke-width:1px
    classDef actor fill:#e6e6e6,stroke:#333,stroke-width:2px
    
    class UI userInterface
    class API apiLayer
    class UserModule,VehicleModule,ParkingModule,ChargingModule,ViolationModule,AnnouncementModule moduleLayer
    class DB dataLayer
    class User actor
```
