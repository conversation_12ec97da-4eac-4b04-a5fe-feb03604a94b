#!/usr/bin/env python
import sqlite3

# 连接数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 查询车牌号包含111的车辆
cursor.execute("SELECT b_id, belong_to, b_num, brand, color, b_type, status FROM bikes WHERE b_num LIKE '%111%'")
bikes = cursor.fetchall()

# 显示结果
print('ID | 所属用户ID | 车牌号 | 品牌 | 颜色 | 类型 | 状态')
print('-' * 80)
for bike in bikes:
    print(f'{bike[0]} | {bike[1]} | {bike[2]} | {bike[3]} | {bike[4]} | {bike[5]} | {bike[6]}')

# 查询jyj用户
cursor.execute("SELECT u_id, u_name, u_role FROM users WHERE u_name='jyj'")
users = cursor.fetchall()

# 显示结果
print('\n用户ID | 用户名 | 角色')
print('-' * 30)
for user in users:
    print(f'{user[0]} | {user[1]} | {user[2]}')

# 查询jyj用户的所有车辆
if users:
    jyj_id = users[0][0]
    cursor.execute("SELECT b_id, b_num, brand, color, b_type, status FROM bikes WHERE belong_to=?", (jyj_id,))
    jyj_bikes = cursor.fetchall()
    
    print(f'\njyj用户(ID:{jyj_id})的所有车辆:')
    print('ID | 车牌号 | 品牌 | 颜色 | 类型 | 状态')
    print('-' * 80)
    for bike in jyj_bikes:
        print(f'{bike[0]} | {bike[1]} | {bike[2]} | {bike[3]} | {bike[4]} | {bike[5]}')

# 关闭连接
conn.close()
