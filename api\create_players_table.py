#!/usr/bin/env python
import os
import sys
import sqlite3
import hashlib
import uuid
from datetime import datetime
from passlib.hash import pbkdf2_sha256 as sha256

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 获取数据库路径
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sys.db')
print(f"数据库路径: {db_path}")
print(f"数据库文件是否存在: {os.path.exists(db_path)}")

try:
    # 连接到数据库
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
    cursor = conn.cursor()
    print("成功连接到数据库\n")
    
    # 检查players表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='players'")
    table_exists = cursor.fetchone()
    
    if not table_exists:
        print("创建players表...")
        cursor.execute('''
        CREATE TABLE players (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(120) NOT NULL UNIQUE,
            password VARCHAR(120) NOT NULL,
            user_id INTEGER,
            FOREIGN KEY (user_id) REFERENCES users(u_id)
        )
        ''')
        conn.commit()
        print("players表创建成功")
    else:
        print("players表已存在")
    
    # 获取admin用户
    cursor.execute("SELECT * FROM users WHERE u_name = 'admin'")
    admin_user = cursor.fetchone()
    
    if admin_user:
        print(f"找到admin用户 (ID: {admin_user['u_id']})")
        
        # 检查admin用户是否已存在于players表中
        cursor.execute("SELECT * FROM players WHERE username = 'admin'")
        admin_player = cursor.fetchone()
        
        if not admin_player:
            print("在players表中创建admin用户...")
            # 使用passlib生成密码哈希
            hashed_password = sha256.hash('111111')
            
            cursor.execute(
                "INSERT INTO players (username, password, user_id) VALUES (?, ?, ?)",
                ('admin', hashed_password, admin_user['u_id'])
            )
            conn.commit()
            
            # 获取新创建的用户ID
            cursor.execute("SELECT id FROM players WHERE username = 'admin'")
            player_id = cursor.fetchone()[0]
            print(f"已创建admin用户 (ID: {player_id})")
        else:
            print(f"admin用户已存在于players表中 (ID: {admin_player['id']})")
    else:
        print("未找到admin用户")
    
    # 获取superadmin用户
    cursor.execute("SELECT * FROM users WHERE u_name = 'superadmin'")
    superadmin_user = cursor.fetchone()
    
    if superadmin_user:
        print(f"找到superadmin用户 (ID: {superadmin_user['u_id']})")
        
        # 检查superadmin用户是否已存在于players表中
        cursor.execute("SELECT * FROM players WHERE username = 'superadmin'")
        superadmin_player = cursor.fetchone()
        
        if not superadmin_player:
            print("在players表中创建superadmin用户...")
            # 使用passlib生成密码哈希
            hashed_password = sha256.hash('111111')
            
            cursor.execute(
                "INSERT INTO players (username, password, user_id) VALUES (?, ?, ?)",
                ('superadmin', hashed_password, superadmin_user['u_id'])
            )
            conn.commit()
            
            # 获取新创建的用户ID
            cursor.execute("SELECT id FROM players WHERE username = 'superadmin'")
            player_id = cursor.fetchone()[0]
            print(f"已创建superadmin用户 (ID: {player_id})")
        else:
            print(f"superadmin用户已存在于players表中 (ID: {superadmin_player['id']})")
    else:
        print("未找到superadmin用户")
    
    # 获取testuser用户
    cursor.execute("SELECT * FROM users WHERE u_name = 'testuser'")
    testuser_user = cursor.fetchone()
    
    if testuser_user:
        print(f"找到testuser用户 (ID: {testuser_user['u_id']})")
        
        # 检查testuser用户是否已存在于players表中
        cursor.execute("SELECT * FROM players WHERE username = 'testuser'")
        testuser_player = cursor.fetchone()
        
        if not testuser_player:
            print("在players表中创建testuser用户...")
            # 使用passlib生成密码哈希
            hashed_password = sha256.hash('111111')
            
            cursor.execute(
                "INSERT INTO players (username, password, user_id) VALUES (?, ?, ?)",
                ('testuser', hashed_password, testuser_user['u_id'])
            )
            conn.commit()
            
            # 获取新创建的用户ID
            cursor.execute("SELECT id FROM players WHERE username = 'testuser'")
            player_id = cursor.fetchone()[0]
            print(f"已创建testuser用户 (ID: {player_id})")
        else:
            print(f"testuser用户已存在于players表中 (ID: {testuser_player['id']})")
    else:
        print("未找到testuser用户")
    
    # 获取qwe用户
    cursor.execute("SELECT * FROM users WHERE u_name = 'qwe'")
    qwe_user = cursor.fetchone()
    
    if qwe_user:
        print(f"找到qwe用户 (ID: {qwe_user['u_id']})")
        
        # 检查qwe用户是否已存在于players表中
        cursor.execute("SELECT * FROM players WHERE username = 'qwe'")
        qwe_player = cursor.fetchone()
        
        if not qwe_player:
            print("在players表中创建qwe用户...")
            # 使用passlib生成密码哈希
            hashed_password = sha256.hash('111111')
            
            cursor.execute(
                "INSERT INTO players (username, password, user_id) VALUES (?, ?, ?)",
                ('qwe', hashed_password, qwe_user['u_id'])
            )
            conn.commit()
            
            # 获取新创建的用户ID
            cursor.execute("SELECT id FROM players WHERE username = 'qwe'")
            player_id = cursor.fetchone()[0]
            print(f"已创建qwe用户 (ID: {player_id})")
        else:
            print(f"qwe用户已存在于players表中 (ID: {qwe_player['id']})")
    else:
        print("未找到qwe用户")
    
    # 验证所有players用户
    cursor.execute("SELECT p.id, p.username, p.user_id, u.u_name, u.u_role FROM players p LEFT JOIN users u ON p.user_id = u.u_id")
    players = cursor.fetchall()
    
    print("\nplayers表中的所有用户:")
    for player in players:
        print(f"ID: {player['id']}, 用户名: {player['username']}, 关联用户ID: {player['user_id']}, 关联用户名: {player['u_name']}, 角色: {player['u_role']}")
    
    # 关闭数据库连接
    conn.close()
    print("\n数据库连接已关闭")
    
except Exception as e:
    print(f"错误: {e}")
