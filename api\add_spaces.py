import sqlite3
import random
from datetime import datetime

# 连接到数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 检查是否已有车位
cursor.execute("SELECT COUNT(*) FROM parking_spaces WHERE parking_lot_id = 1")
space_count = cursor.fetchone()[0]

if space_count > 0:
    print(f"停车场已有 {space_count} 个车位，不添加新车位")
else:
    # 添加20个车位
    spaces_to_add = []
    for i in range(1, 21):
        # 随机分配车位类型：1=普通，2=充电，3=残疾人，4=VIP
        space_type = random.choices([1, 2, 3, 4], weights=[70, 15, 10, 5])[0]
        
        # 所有车位初始状态为空闲(0)
        status = 0
        
        # 创建车位记录
        space = (
            1,  # parking_lot_id
            f"A{i:02d}",  # space_number
            space_type,  # type
            status,  # status
            None,  # current_vehicle_id
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # created_at
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')   # updated_at
        )
        spaces_to_add.append(space)
    
    # 批量插入车位
    cursor.executemany(
        "INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
        spaces_to_add
    )
    
    # 更新停车场的总车位数
    cursor.execute("UPDATE parking_lots SET total_spaces = ? WHERE id = 1", (len(spaces_to_add),))
    
    # 提交事务
    conn.commit()
    print(f"成功添加 {len(spaces_to_add)} 个车位到停车场")

# 关闭连接
conn.close()
