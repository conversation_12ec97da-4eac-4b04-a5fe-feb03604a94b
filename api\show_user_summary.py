#!/usr/bin/env python
import sqlite3
import os

def main():
    """显示用户账户摘要信息"""
    # 获取数据库路径
    db_path = 'sys.db'
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件 {db_path} 不存在")
        return
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查询所有用户
    cursor.execute("SELECT u_id, u_name, u_role, u_belong, u_phone, u_email FROM users ORDER BY u_id")
    users = cursor.fetchall()
    
    # 关闭连接
    conn.close()
    
    # 显示用户摘要信息
    print("\n系统账户摘要信息")
    print("=" * 80)
    print("ID | 用户名 | 角色 | 所属部门 | 电话 | 邮箱 | 密码")
    print("-" * 80)
    
    for user in users:
        user_id, username, role, belong, phone, email = user
        # 所有用户的密码都是111111
        password = "111111"
        print(f"{user_id} | {username} | {role} | {belong or '无'} | {phone or '无'} | {email or '无'} | {password}")
    
    print("=" * 80)
    print("\n用户角色说明:")
    print("- admin: 系统管理员，拥有所有权限")
    print("- security: 安保人员，负责管理停车场和记录违规")
    print("- user: 普通用户，可以使用停车和充电服务")
    
    print("\n注意: 所有用户的默认密码都是 111111")

if __name__ == "__main__":
    main()
