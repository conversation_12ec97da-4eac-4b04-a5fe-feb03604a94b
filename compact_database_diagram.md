# 校园电动车管理系统数据库关系图

下面是一个紧凑的数据库实体关系图，只展示实体及其关系，不包含属性：

```mermaid
erDiagram
    %% 核心实体
    Users ||--o{ Bikes : "拥有"
    Users ||--o| Players : "关联"
    Users ||--o{ ParkingRecord : "创建"
    Users ||--o{ ChargingRecord : "创建"
    Users ||--o{ ViolationRecord : "接收/记录"
    Users ||--o{ Appeal : "提交"
    Users ||--o{ Evidence : "上传"
    Users ||--o{ Announcement : "发布"
    
    %% 车辆关系
    Bikes ||--o{ ParkingRecord : "参与"
    Bikes ||--o{ ChargingRecord : "参与"
    Bikes ||--o{ ViolationRecord : "涉及"
    
    %% 停车场关系
    ParkingLot ||--o{ ParkingSpace : "包含"
    ParkingLot ||--o{ ParkingRecord : "关联"
    
    %% 停车位关系
    ParkingSpace ||--o{ ParkingRecord : "使用"
    ParkingSpace ||--o{ ChargingRecord : "使用"
    ParkingSpace ||--o| Bikes : "当前停放"
    
    %% 停车记录关系
    ParkingRecord ||--o{ ChargingRecord : "关联"
    
    %% 充电关系
    ChargingRecord ||--o{ ChargingException : "发生"
    ChargingFault ||--o{ ChargingException : "关联"
    
    %% 违规关系
    ViolationType ||--o{ ViolationRecord : "分类"
    ViolationRecord ||--o| Appeal : "申诉"
    ViolationRecord ||--o{ Evidence : "包含"
    Appeal ||--o{ Evidence : "包含"
```

## 实体说明

1. **Users**: 用户信息，包括普通用户、管理员和保安
2. **Bikes**: 电动车信息
3. **Players**: 玩家账号，与用户关联
4. **ParkingLot**: 停车场信息
5. **ParkingSpace**: 停车位信息
6. **ParkingRecord**: 停车记录
7. **ChargingRecord**: 充电记录
8. **ChargingException**: 充电异常记录
9. **ChargingFault**: 充电故障记录
10. **ViolationType**: 违规类型
11. **ViolationRecord**: 违规记录
12. **Appeal**: 申诉记录
13. **Evidence**: 证据信息
14. **Announcement**: 公告信息

## 关系说明

- 用户可以拥有多辆电动车，创建多个停车/充电记录，接收多个违规记录，提交多个申诉
- 电动车可以参与多个停车/充电记录，涉及多个违规记录
- 停车场包含多个停车位，关联多个停车记录
- 停车位可以用于多个停车/充电记录，当前可以停放一辆电动车
- 停车记录可以关联多个充电记录
- 充电记录可以发生多个充电异常
- 充电故障可以关联多个充电异常
- 违规类型可以分类多个违规记录
- 违规记录可以有一个申诉，包含多个证据
- 申诉可以包含多个证据
