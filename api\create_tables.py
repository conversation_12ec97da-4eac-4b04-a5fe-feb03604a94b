from app import create_app, db
from app.violations.models import ViolationRecord, Appeal, Evidence

app = create_app()

with app.app_context():
    # 创建违规记录相关的表
    # 检查表是否存在
    inspector = db.inspect(db.engine)
    existing_tables = inspector.get_table_names()

    # 创建不存在的表
    if 'violation_records' not in existing_tables:
        ViolationRecord.__table__.create(db.engine)
        print("违规记录表已创建")

    if 'appeals' not in existing_tables:
        Appeal.__table__.create(db.engine)
        print("申诉记录表已创建")

    if 'evidences' not in existing_tables:
        Evidence.__table__.create(db.engine)
        print("证据表已创建")

    print("违规记录相关的表检查完成")
