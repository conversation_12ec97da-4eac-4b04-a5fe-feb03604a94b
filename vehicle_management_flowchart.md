```mermaid
flowchart TD
    %% 车辆信息管理流程图
    Start([开始]) --> AdminLogin[管理员登录]
    AdminLogin --> AccessVehicleMgmt[访问车辆管理页面]
    
    %% 车辆列表与搜索
    AccessVehicleMgmt --> ViewVehicleList[查看车辆列表]
    ViewVehicleList --> SearchVehicles[搜索/筛选车辆]
    SearchVehicles --> FilterByType[按类型筛选]
    SearchVehicles --> FilterByOwner[按所有者筛选]
    SearchVehicles --> FilterByStatus[按状态筛选]
    ViewVehicleList --> SortVehicles[排序车辆列表]
    
    %% 车辆详情查看
    ViewVehicleList --> ViewVehicleDetail[查看车辆详情]
    ViewVehicleDetail --> ViewOwnerInfo[查看所有者信息]
    ViewVehicleDetail --> ViewParkingHistory[查看停车历史]
    ViewVehicleDetail --> ViewChargingHistory[查看充电历史]
    ViewVehicleDetail --> ViewViolationHistory[查看违规历史]
    
    %% 车辆信息编辑
    ViewVehicleDetail --> EditVehicle[编辑车辆信息]
    EditVehicle --> ValidateVehicleData{验证数据}
    ValidateVehicleData -->|数据有效| SaveVehicleChanges[保存车辆信息]
    ValidateVehicleData -->|数据无效| ShowValidationError[显示验证错误]
    ShowValidationError --> EditVehicle
    
    %% 车辆所有权管理
    ViewVehicleDetail --> ManageOwnership[管理车辆所有权]
    ManageOwnership --> SearchUsers[搜索用户]
    SearchUsers --> SelectNewOwner[选择新所有者]
    SelectNewOwner --> ConfirmOwnerChange[确认所有权变更]
    ConfirmOwnerChange --> UpdateOwnership[更新车辆所有权]
    
    %% 注册新车辆
    ViewVehicleList --> RegisterNewVehicle[注册新车辆]
    RegisterNewVehicle --> SelectVehicleOwner[选择车辆所有者]
    SelectVehicleOwner --> FillVehicleForm[填写车辆表单]
    FillVehicleForm --> ValidateNewVehicle{验证数据}
    ValidateNewVehicle -->|数据有效| SaveNewVehicle[保存新车辆]
    ValidateNewVehicle -->|数据无效| ShowNewVehicleError[显示验证错误]
    ShowNewVehicleError --> FillVehicleForm
    
    %% 删除车辆
    ViewVehicleDetail --> DeleteVehicle[删除车辆]
    DeleteVehicle --> ConfirmVehicleDeletion{确认删除}
    ConfirmVehicleDeletion -->|确认| ProcessVehicleDeletion[处理删除]
    ConfirmVehicleDeletion -->|取消| CancelVehicleDeletion[取消操作]
    ProcessVehicleDeletion --> CheckVehicleRecords{检查关联记录}
    CheckVehicleRecords -->|有关联记录| HandleVehicleRecords[处理关联记录]
    HandleVehicleRecords --> RemoveVehicle[移除车辆]
    CheckVehicleRecords -->|无关联记录| RemoveVehicle
    
    %% 批量操作
    ViewVehicleList --> SelectMultipleVehicles[选择多个车辆]
    SelectMultipleVehicles --> BatchVehicleOperation[批量操作]
    BatchVehicleOperation --> BatchVehicleDelete[批量删除]
    BatchVehicleOperation --> BatchTypeUpdate[批量类型更新]
    BatchVehicleOperation --> BatchVehicleExport[批量导出]
    
    %% 车辆数据导出
    ViewVehicleList --> ExportVehicleData[导出车辆数据]
    ExportVehicleData --> SelectVehicleExportFormat[选择导出格式]
    SelectVehicleExportFormat --> GenerateVehicleExportFile[生成导出文件]
    GenerateVehicleExportFile --> DownloadVehicleFile[下载文件]
    
    %% 车辆类型管理
    AccessVehicleMgmt --> ManageVehicleTypes[管理车辆类型]
    ManageVehicleTypes --> ViewTypeList[查看类型列表]
    ViewTypeList --> AddNewType[添加新类型]
    ViewTypeList --> EditType[编辑类型]
    ViewTypeList --> DeleteType[删除类型]
    
    %% 样式定义
    classDef start fill:#f9d5e5,stroke:#333,stroke-width:2px
    classDef process fill:#d4f1f9,stroke:#333,stroke-width:1px
    classDef decision fill:#ffffcc,stroke:#333,stroke-width:1px
    classDef operation fill:#e6e6fa,stroke:#333,stroke-width:1px
    
    class Start start
    class AdminLogin,AccessVehicleMgmt,ViewVehicleList,SearchVehicles,FilterByType,FilterByOwner,FilterByStatus,SortVehicles,ViewVehicleDetail,ViewOwnerInfo,ViewParkingHistory,ViewChargingHistory,ViewViolationHistory,EditVehicle,SaveVehicleChanges,ShowValidationError,ManageOwnership,SearchUsers,SelectNewOwner,ConfirmOwnerChange,UpdateOwnership,RegisterNewVehicle,SelectVehicleOwner,FillVehicleForm,SaveNewVehicle,ShowNewVehicleError,DeleteVehicle,ProcessVehicleDeletion,CancelVehicleDeletion,HandleVehicleRecords,RemoveVehicle,SelectMultipleVehicles,BatchVehicleOperation,BatchVehicleDelete,BatchTypeUpdate,BatchVehicleExport,ExportVehicleData,SelectVehicleExportFormat,GenerateVehicleExportFile,DownloadVehicleFile,ManageVehicleTypes,ViewTypeList,AddNewType,EditType,DeleteType process
    class ValidateVehicleData,ValidateNewVehicle,ConfirmVehicleDeletion,CheckVehicleRecords decision
```
