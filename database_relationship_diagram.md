# 数据库表间关系流程图

```mermaid
%%{init: {'theme': 'neutral', 'themeVariables': { 'primaryColor': '#f0f8ff', 'primaryTextColor': '#003366', 'primaryBorderColor': '#7fb1d3', 'lineColor': '#004d99', 'secondaryColor': '#e6f3ff', 'tertiaryColor': '#fff0f0' }}}%%
erDiagram
    %% 用户和车辆管理
    USERS {
        int u_id PK
        string u_name
        string u_pwd
        string u_role
        string u_belong
        string u_phone
    }

    BIKES {
        int b_id PK
        int belong_to FK
        string b_num
        string brand
        string status
    }

    PLAYERS {
        int id PK
        string username
        int user_id FK
    }

    %% 停车场管理
    PARKING_LOTS {
        int id PK
        string name
        string address
        int total_spaces
        int occupied_spaces
        int status
    }

    PARKING_SPACES {
        int id PK
        int parking_lot_id FK
        string space_number
        int type
        int status
        int current_vehicle_id FK
    }

    PARKING_RECORDS {
        int id PK
        int vehicle_id FK
        int user_id FK
        int parking_lot_id FK
        int parking_space_id FK
        datetime entry_time
        datetime exit_time
        int status
    }

    %% 充电管理
    CHARGING_RECORDS {
        int id PK
        int parking_record_id FK
        int vehicle_id FK
        int user_id FK
        datetime start_time
        datetime end_time
        int status
    }

    CHARGING_FAULTS {
        int id PK
        int parking_lot_id FK
        int space_id FK
        string fault_type
        string severity
        int status
    }

    CHARGING_EXCEPTIONS {
        int id PK
        int charging_record_id FK
        int fault_id FK
        string exception_type
        int status
    }

    CHARGING_RESERVATIONS {
        int id PK
        int user_id FK
        int vehicle_id FK
        int parking_lot_id FK
        datetime start_time
        datetime end_time
        int status
    }

    %% 违规管理
    VIOLATION_RECORDS {
        int id PK
        int bike_id FK
        int user_id FK
        datetime violation_time
        int violation_type_id FK
        int status
        int recorder_id FK
    }

    VIOLATION_TYPES {
        int id PK
        string name
        int needs_admin
    }

    APPEALS {
        int id PK
        int violation_id FK
        int user_id FK
        string reason
        int status
        int handler_id FK
    }

    EVIDENCES {
        int id PK
        int related_id
        string related_type
        string file_path
        int uploader_id FK
    }

    %% 用户关系
    USERS ||--o{ BIKES : "拥有"
    USERS ||--o| PLAYERS : "关联"
    USERS ||--o{ PARKING_RECORDS : "创建"
    USERS ||--o{ CHARGING_RECORDS : "创建"
    USERS ||--o{ CHARGING_RESERVATIONS : "创建"
    USERS ||--o{ VIOLATION_RECORDS : "违规"
    USERS ||--o{ APPEALS : "申诉"
    USERS ||--o{ EVIDENCES : "上传"

    %% 车辆关系
    BIKES ||--o{ PARKING_RECORDS : "使用"
    BIKES ||--o{ CHARGING_RECORDS : "充电"
    BIKES ||--o{ CHARGING_RESERVATIONS : "预约"
    BIKES ||--o{ VIOLATION_RECORDS : "违规"

    %% 停车场关系
    PARKING_LOTS ||--o{ PARKING_SPACES : "包含"
    PARKING_LOTS ||--o{ PARKING_RECORDS : "使用"
    PARKING_LOTS ||--o{ CHARGING_RECORDS : "使用"
    PARKING_LOTS ||--o{ CHARGING_FAULTS : "发生"

    %% 停车位关系
    PARKING_SPACES ||--o{ PARKING_RECORDS : "使用"
    PARKING_SPACES ||--o{ CHARGING_RECORDS : "使用"
    PARKING_SPACES ||--o{ CHARGING_FAULTS : "发生"

    %% 停车记录关系
    PARKING_RECORDS ||--o{ CHARGING_RECORDS : "关联"

    %% 充电关系
    CHARGING_RECORDS ||--o{ CHARGING_EXCEPTIONS : "发生"
    CHARGING_FAULTS ||--o{ CHARGING_EXCEPTIONS : "关联"

    %% 违规关系
    VIOLATION_TYPES ||--o{ VIOLATION_RECORDS : "分类"
    VIOLATION_RECORDS ||--o| APPEALS : "申诉"
    VIOLATION_RECORDS ||--o{ EVIDENCES : "证据"
    APPEALS ||--o{ EVIDENCES : "证据"
```

## 表间关系说明

1. **用户(USERS)与其他表的关系**:
   - 一个用户可以拥有多辆电动车(BIKES)
   - 一个用户可以创建多个停车记录(PARKING_RECORDS)
   - 一个用户可以创建多个充电记录(CHARGING_RECORDS)
   - 一个用户可以创建多个充电预约(CHARGING_RESERVATIONS)
   - 一个用户可以有多个违规记录(VIOLATION_RECORDS)
   - 一个用户可以提交多个申诉(APPEALS)
   - 一个用户可以上传多个证据(EVIDENCES)
   - 一个用户可以关联一个玩家账号(PLAYERS)

2. **电动车(BIKES)与其他表的关系**:
   - 每辆电动车属于一个用户(USERS)
   - 一辆电动车可以有多个停车记录(PARKING_RECORDS)
   - 一辆电动车可以有多个充电记录(CHARGING_RECORDS)
   - 一辆电动车可以有多个充电预约(CHARGING_RESERVATIONS)
   - 一辆电动车可以有多个违规记录(VIOLATION_RECORDS)

3. **停车场(PARKING_LOTS)与其他表的关系**:
   - 一个停车场包含多个停车位(PARKING_SPACES)
   - 一个停车场可以有多个停车记录(PARKING_RECORDS)
   - 一个停车场可以有多个充电记录(CHARGING_RECORDS)
   - 一个停车场可以有多个充电故障(CHARGING_FAULTS)
   - 一个停车场可以有多个充电异常(CHARGING_EXCEPTIONS)
   - 一个停车场可以有多个充电预约(CHARGING_RESERVATIONS)

4. **停车位(PARKING_SPACES)与其他表的关系**:
   - 每个停车位属于一个停车场(PARKING_LOTS)
   - 一个停车位可以有多个停车记录(PARKING_RECORDS)
   - 一个停车位可以有多个充电记录(CHARGING_RECORDS)
   - 一个停车位可以有多个充电故障(CHARGING_FAULTS)
   - 一个停车位可以有多个充电异常(CHARGING_EXCEPTIONS)

5. **停车记录(PARKING_RECORDS)与其他表的关系**:
   - 每个停车记录关联一个用户(USERS)
   - 每个停车记录关联一辆电动车(BIKES)
   - 每个停车记录关联一个停车场(PARKING_LOTS)
   - 每个停车记录关联一个停车位(PARKING_SPACES)
   - 一个停车记录可以关联多个充电记录(CHARGING_RECORDS)

6. **充电相关表之间的关系**:
   - 充电记录(CHARGING_RECORDS)关联一个停车记录(PARKING_RECORDS)
   - 充电记录可以有多个充电异常(CHARGING_EXCEPTIONS)
   - 充电故障(CHARGING_FAULTS)可以关联多个充电异常(CHARGING_EXCEPTIONS)

7. **违规相关表之间的关系**:
   - 违规类型(VIOLATION_TYPES)可以关联多个违规记录(VIOLATION_RECORDS)
   - 一个违规记录(VIOLATION_RECORDS)可以有一个申诉(APPEALS)
   - 违规记录和申诉都可以关联多个证据(EVIDENCES)
