import sqlite3
import datetime

# 连接数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 获取当前日期
now = datetime.datetime.now()

# 添加当前日期范围内的测试数据
for i in range(7):
    # 计算日期：今天和过去6天
    day = now - datetime.timedelta(days=i)

    # 为每天添加1-3条记录
    for j in range(1, 4):
        # 计算时间：上午9点、下午2点、晚上7点
        hour = 9 if j == 1 else (14 if j == 2 else 19)

        # 设置开始时间
        start_time = day.replace(hour=hour, minute=0, second=0, microsecond=0)

        # 设置结束时间（2小时后）
        end_time = start_time + datetime.timedelta(hours=2)

        # 设置状态（已完成）
        status = 1

        # 插入记录
        cursor.execute('''
            INSERT INTO charging_records
            (parking_record_id, vehicle_id, user_id, parking_lot_id, parking_space_id, start_time, end_time, status, duration, power)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            1,  # parking_record_id
            1,  # vehicle_id
            1,  # user_id
            1,  # parking_lot_id
            1,  # parking_space_id
            start_time.strftime('%Y-%m-%d %H:%M:%S'),  # 使用当前日期
            end_time.strftime('%Y-%m-%d %H:%M:%S'),  # 使用当前日期
            status,
            120,  # duration (分钟)
            7.0   # power (kW)
        ))

        print(f"添加了充电记录: {start_time} - {end_time}")

# 提交更改
conn.commit()

# 查询添加的记录
cursor.execute('SELECT COUNT(*) FROM charging_records')
total = cursor.fetchone()[0]
print(f"\n当前充电记录总数: {total}")

# 查询当前日期范围内的记录
seven_days_ago = (now - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
cursor.execute('SELECT COUNT(*) FROM charging_records WHERE start_time >= ?', (seven_days_ago,))
recent_count = cursor.fetchone()[0]
print(f"过去7天的充电记录数: {recent_count}")

# 关闭连接
conn.close()

print("\n测试数据添加完成！")
