#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查充电车位编号格式
"""

import sqlite3
import os

def check_charging_spaces():
    """检查充电车位编号格式"""
    # 连接数据库
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    # 查询所有停车场
    cursor.execute("SELECT id, name FROM parking_lots")
    parking_lots = cursor.fetchall()
    
    print("停车场列表:")
    for lot in parking_lots:
        print(f"ID: {lot[0]}, 名称: {lot[1]}")
    
    # 查询所有充电车位
    cursor.execute("SELECT id, parking_lot_id, space_number, type, status FROM parking_spaces WHERE type = 3")
    charging_spaces = cursor.fetchall()
    
    print("\n充电车位列表:")
    print("ID | 停车场ID | 车位编号 | 类型 | 状态")
    print("-" * 50)
    for space in charging_spaces:
        print(f"{space[0]} | {space[1]} | {space[2]} | {space[3]} | {space[4]}")
    
    # 查询车位类型分布
    cursor.execute("SELECT type, COUNT(*) FROM parking_spaces GROUP BY type")
    type_counts = cursor.fetchall()
    
    print("\n车位类型分布:")
    print("类型 | 数量")
    print("-" * 20)
    for type_count in type_counts:
        type_name = {1: "普通车位", 2: "残疾人车位", 3: "充电车位"}.get(type_count[0], f"未知类型({type_count[0]})")
        print(f"{type_count[0]} ({type_name}) | {type_count[1]}")
    
    # 查询车位编号前缀分布
    cursor.execute("SELECT SUBSTR(space_number, 1, 1), COUNT(*) FROM parking_spaces GROUP BY SUBSTR(space_number, 1, 1)")
    prefix_counts = cursor.fetchall()
    
    print("\n车位编号前缀分布:")
    print("前缀 | 数量")
    print("-" * 20)
    for prefix_count in prefix_counts:
        print(f"{prefix_count[0]} | {prefix_count[1]}")
    
    # 关闭连接
    conn.close()

if __name__ == "__main__":
    # 切换到包含数据库的目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    check_charging_spaces()
