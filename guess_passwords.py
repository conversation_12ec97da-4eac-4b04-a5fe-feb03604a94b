#!/usr/bin/env python
import sqlite3
import hashlib
import sys

def check_password(username, password_to_check):
    """检查指定用户名的密码是否匹配"""
    # 连接数据库
    conn = sqlite3.connect('api/sys.db')
    cursor = conn.cursor()

    # 查询用户
    cursor.execute("SELECT u_id, u_name, u_pwd, salt FROM users WHERE u_name=?", (username,))
    user = cursor.fetchone()

    if not user:
        print(f"用户 {username} 不存在")
        conn.close()
        return False

    # 获取用户信息
    user_id, user_name, stored_hash, salt = user

    # 使用相同的盐值对输入的密码进行哈希
    calculated_hash = hashlib.sha256(f"{password_to_check}{salt}".encode()).hexdigest()

    # 比较哈希值
    is_match = calculated_hash == stored_hash

    # 关闭连接
    conn.close()
    return is_match

def main():
    """主函数"""
    # 连接数据库
    conn = sqlite3.connect('api/sys.db')
    cursor = conn.cursor()

    # 查询所有用户
    cursor.execute("SELECT u_name FROM users")
    users = [user[0] for user in cursor.fetchall()]
    print(f"找到 {len(users)} 个用户: {', '.join(users)}")

    # 关闭连接
    conn.close()

    # 常见密码列表
    common_passwords = [
        "123456", "password", "admin", "admin123", "12345678", "qwerty",
        "1234567890", "1234", "123123", "000000", "111111", "666666",
        "abcd1234", "password123", "admin123456", "welcome", "welcome123",
        "p@ssw0rd", "P@ssw0rd", "abc123", "123abc", "123456789", "654321",
        "admin@123", "admin123!@#", "root", "root123", "user", "user123",
        "security", "security123", "manager", "manager123"
    ]
    print(f"将尝试 {len(common_passwords)} 个常见密码")

    # 为每个用户尝试常见密码
    for username in users:
        print(f"尝试猜测用户 {username} 的密码...")
        for password in common_passwords:
            sys.stdout.write(f"尝试密码: {password}\r")
            sys.stdout.flush()
            if check_password(username, password):
                print(f"\n找到密码! 用户 {username} 的密码是: {password}")
                break
        else:
            print(f"\n未能猜测出用户 {username} 的密码")

if __name__ == "__main__":
    main()
