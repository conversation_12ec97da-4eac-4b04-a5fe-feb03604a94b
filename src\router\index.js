import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/profile',
    component: Layout,
    redirect: '/profile/info',
    name: 'Profile',
    meta: { title: '个人中心', icon: 'user' },
    children: [
      {
        path: 'info',
        name: 'ProfileInfo',
        component: () => import('@/views/profile/index'),
        meta: { title: '个人信息', icon: 'user' }
      },
      {
        path: 'mybikes',
        name: 'MyBikes',
        component: () => import('@/views/profile/mybikes'),
        meta: { title: '我的车辆', icon: 'el-icon-bicycle' }
      },
      {
        path: 'myparking',
        name: 'MyParking',
        component: () => import('@/views/parking/parking-center'),
        meta: { title: '我的停车', icon: 'el-icon-s-promotion' }
      },
      {
        path: 'charging',
        name: 'MyCharging',
        component: () => import('@/views/profile/charging'),
        meta: { title: '我的充电', icon: 'el-icon-lightning' }
      },
      {
        path: 'lot-details/:id',
        name: 'UserParkingLotDetails',
        component: () => import('@/views/parking/lot-details'),
        meta: { title: '停车场详情', icon: 'el-icon-location-information' },
        hidden: true
      },
      {
        path: 'violations',
        name: 'MyViolations',
        component: () => import('@/views/violations/user/records'),
        meta: { title: '我的违规', icon: 'el-icon-warning-outline' }
      },
      {
        path: 'violation-detail/:id',
        name: 'MyViolationDetail',
        component: () => import('@/views/violations/user/detail'),
        meta: { title: '违规详情' },
        hidden: true
      },
      {
        path: 'violation-appeal/:id',
        name: 'MyViolationAppeal',
        component: () => import('@/views/violations/user/appeal'),
        meta: { title: '提交申诉' },
        hidden: true
      },
      {
        path: 'appeal-detail/:id',
        name: 'MyAppealDetail',
        component: () => import('@/views/violations/user/appeal-detail'),
        meta: { title: '申诉详情' },
        hidden: true
      }
    ]
  },

  {
    path: '/bikes',
    component: Layout,
    redirect: '/bike/user',
    name: 'Bike',
    meta: { title: '电动车管理', icon: 'el-icon-bicycle' },
    children: [
      {
        path: 'user',
        name: 'user',
        component: () => import('@/views/users/index'),
        meta: { title: '用户信息', icon: 'el-icon-user' }
      },
      {
        path: '',
        name: 'Bikes',
        component: () => import('@/views/bikes/index'),
        meta: { title: '车辆管理', icon: 'el-icon-truck' }
      },
      {
        path: ':id',
        name: 'BikeDetail',
        component: () => import('@/views/bikes/detail'),
        meta: { title: '车辆详情', icon: 'el-icon-document' },
        hidden: true
      }
    ]
  },

  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/403',
    component: () => import('@/views/403'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: '仪表盘', icon: 'dashboard' }
    }]
  },

  {
    path: '/parking',
    component: Layout,
    redirect: '/parking/index',
    name: 'Parking',
    meta: {
      title: '停车中心',
      icon: 'el-icon-s-order'
    },
    children: [
      {
        path: 'index',
        name: 'ParkingIndex',
        component: () => import('@/views/parking/index'),
        meta: { title: '停车场管理', icon: 'el-icon-s-grid' }
      },
      {
        path: 'details/:id',
        name: 'ParkingDetails',
        component: () => import('@/views/parking/details'),
        meta: { title: '停车场详情', noCache: true },
        hidden: true
      },
      {
        path: 'edit/:id',
        name: 'ParkingEdit',
        component: () => import('@/views/parking/edit'),
        meta: { title: '编辑停车场车位', noCache: true, roles: ['admin'] },
        hidden: true
      },
      {
        path: 'records',
        name: 'ParkingRecords',
        component: () => import('@/views/parking/records'),
        meta: { title: '停车记录', icon: 'el-icon-time' }
      },
      {
        path: 'stats',
        name: 'ParkingStats',
        component: () => import('@/views/parking/trend-stats'),
        meta: { title: '停车趋势分析', icon: 'el-icon-data-analysis', roles: ['admin', 'security'] }
      }
    ]
  },

  {
    path: '/charging-admin',
    component: Layout,
    redirect: '/charging-admin/spaces',
    name: 'ChargingAdmin',
    meta: {
      title: '充电管理',
      icon: 'el-icon-lightning',
      roles: ['admin', 'security']
    },
    children: [
      {
        path: 'spaces',
        name: 'ChargingSpaces',
        component: () => import('@/views/charging/admin/spaces'),
        meta: { title: '充电车位管理', icon: 'el-icon-s-grid', roles: ['admin'] }
      },
      {
        path: 'records',
        name: 'ChargingRecords',
        component: () => import('@/views/charging/admin/records'),
        meta: { title: '充电记录管理', icon: 'el-icon-tickets', roles: ['admin', 'security'] }
      },
      {
        path: 'dashboard',
        name: 'ChargingDashboard',
        component: () => import('@/views/charging/admin/dashboard'),
        meta: { title: '充电统计', icon: 'el-icon-data-analysis', roles: ['admin', 'security'] }
      },
      {
        path: 'faults',
        name: 'ChargingFaults',
        component: () => import('@/views/charging/admin/faults'),
        meta: { title: '充电故障录入', icon: 'el-icon-warning', roles: ['admin', 'security'] }
      },
      {
        path: 'exceptions',
        name: 'ChargingExceptions',
        component: () => import('@/views/charging/admin/exceptions'),
        meta: { title: '异常处理', icon: 'el-icon-error', roles: ['admin'] }
      }
    ]
  },

  {
    path: '/violation',
    component: Layout,
    redirect: '/violation/public/records',
    name: 'Violation',
    meta: {
      title: '违规中心',
      icon: 'el-icon-warning'
    },
    children: [
      // 公开路由 - 所有用户可访问
      {
        path: 'public/records',
        name: 'ViolationPublicRecords',
        component: () => import('@/views/violations/public/records'),
        meta: { title: '违规公示', icon: 'el-icon-document' }
      },
      {
        path: 'public/detail/:id',
        name: 'PublicViolationDetail',
        component: () => import('@/views/violations/public/detail'),
        meta: { title: '违规详情' },
        hidden: true
      },

      // 保安路由
      {
        path: 'security/create',
        name: 'SecurityViolationCreate',
        component: () => import('@/views/violations/security/create'),
        meta: { title: '违规录入', icon: 'el-icon-edit-outline', roles: ['security', 'admin'] }
      },
      {
        path: 'security/detail/:id',
        name: 'SecurityViolationDetail',
        component: () => import('@/views/violations/security/detail'),
        meta: { title: '违规详情', roles: ['security', 'admin'] },
        hidden: true
      },

      // 管理员路由
      {
        path: 'admin/management',
        name: 'AdminViolationManagement',
        component: () => import('@/views/violations/admin/management'),
        meta: { title: '违规管理', icon: 'el-icon-chat-dot-square', roles: ['admin'] }
      },
      {
        path: 'admin/dashboard',
        name: 'AdminViolationDashboard',
        component: () => import('@/views/violations/admin/dashboard'),
        meta: { title: '违规统计', icon: 'el-icon-data-analysis', roles: ['admin'] }
      },
      {
        path: 'admin/detail/:id',
        name: 'AdminViolationDetail',
        component: () => import('@/views/violations/admin/detail'),
        meta: { title: '违规详情', roles: ['admin'] },
        hidden: true
      }
    ]
  },

  {
    path: '/announcements',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Announcements',
        component: () => import('@/views/announcements/index'),
        meta: { title: '公告栏', icon: 'el-icon-bell' }
      }
    ]
  },

  {
    path: '/temp',
    component: Layout,
    children: [
      {
        path: 'occupied-spaces',
        name: 'OccupiedSpaces',
        component: () => import('@/views/temp/occupied-spaces'),
        meta: { title: '已占用车位查询', icon: 'el-icon-search' }
      },
      {
        path: 'fix-spaces',
        name: 'FixSpaces',
        component: () => import('@/views/temp/fix-spaces'),
        meta: { title: '修复异常车位', icon: 'el-icon-s-tools' }
      },
      {
        path: 'check-unknown-spaces',
        name: 'CheckUnknownSpaces',
        component: () => import('@/views/temp/check-unknown-spaces'),
        meta: { title: '未知状态车位检查', icon: 'el-icon-warning' }
      }
    ]
  },

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
