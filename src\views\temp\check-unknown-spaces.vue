<template>
  <div class="app-container">
    <div class="dashboard-header">
      <div class="dashboard-title">
        <h2>未知状态车位检查</h2>
        <p class="subtitle">查看所有状态异常或未知的车位，并检查数据一致性问题</p>
      </div>
      <div class="dashboard-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="fetchAllData">刷新数据</el-button>
        <el-button type="success" icon="el-icon-check" @click="fixAllSpaces" :disabled="!hasProblematicSpaces || processing">
          一键修复所有异常车位
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="box-card">
      <div slot="header" class="clearfix">
        <span>异常状态车位列表</span>
        <el-tag type="danger" style="margin-left: 10px;">{{ problematicSpaces.length }} 个异常车位</el-tag>
      </div>

      <el-table
        :data="problematicSpaces"
        border
        style="width: 100%"
        :default-sort="{prop: 'parkingLotName', order: 'ascending'}"
      >
        <el-table-column
          prop="parkingLotName"
          label="停车场"
          sortable
          width="180"
        />
        <el-table-column
          prop="spaceNumber"
          label="车位号"
          sortable
          width="120"
        />
        <el-table-column
          prop="spaceType"
          label="车位类型"
          width="120"
        >
          <template slot-scope="scope">
            <el-tag :type="getSpaceTypeTag(scope.row.spaceType)">{{ scope.row.spaceTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="状态值"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === null ? 'danger' : 'warning'">
              {{ scope.row.status === null ? 'NULL' : scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="statusText"
          label="状态描述"
          width="120"
        >
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.statusText || '未知状态' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="currentVehicleId"
          label="车辆ID"
          width="100"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.currentVehicleId">{{ scope.row.currentVehicleId }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="issue"
          label="问题描述"
          min-width="200"
        />
        <el-table-column
          label="操作"
          width="180"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="success"
              @click="fixSpace(scope.row)"
              :loading="scope.row.processing"
            >修复为可用</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="problematicSpaces.length === 0 && !loading" class="empty-data">
        <el-empty description="没有发现状态异常的车位" :image-size="200">
          <el-button type="primary" @click="fetchAllData">重新检查</el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 操作日志 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>操作日志</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="logs = []">清空日志</el-button>
      </div>

      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="empty-log">
          暂无操作日志
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getParkingLots, getParkingSpaces, updateParkingSpaceStatus } from '@/api/parkinglot'
import request from '@/utils/request'

export default {
  name: 'CheckUnknownSpaces',
  data() {
    return {
      loading: true,
      processing: false,
      parkingLots: [],
      allSpaces: [],
      problematicSpaces: [],
      logs: []
    }
  },
  computed: {
    hasProblematicSpaces() {
      return this.problematicSpaces.length > 0
    }
  },
  created() {
    this.fetchAllData()
  },
  methods: {
    async fetchAllData() {
      this.loading = true
      this.addLog('info', '开始获取所有停车场数据...')

      try {
        // 获取所有停车场
        const response = await getParkingLots()
        let lotsData = response.data || response

        if (Array.isArray(lotsData)) {
          this.parkingLots = lotsData
        } else if (lotsData.items && Array.isArray(lotsData.items)) {
          this.parkingLots = lotsData.items
        } else {
          console.error('无法解析停车场数据:', lotsData)
          this.parkingLots = []
          this.addLog('error', '无法解析停车场数据')
        }

        this.addLog('success', `成功获取 ${this.parkingLots.length} 个停车场`)

        // 清空车位列表
        this.allSpaces = []
        this.problematicSpaces = []

        // 获取每个停车场的车位信息
        for (const lot of this.parkingLots) {
          await this.fetchParkingSpaces(lot)
        }

        this.addLog('info', `总共找到 ${this.allSpaces.length} 个车位，其中 ${this.problematicSpaces.length} 个存在状态异常`)

      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
        this.addLog('error', `获取数据失败: ${error.message || '未知错误'}`)
      } finally {
        this.loading = false
      }
    },

    async fetchParkingSpaces(lot) {
      try {
        this.addLog('info', `正在获取停车场 "${lot.name}" 的车位数据...`)

        // 获取所有车位，不过滤状态
        const response = await getParkingSpaces(lot.id, { limit: 500 })
        let spacesData = response.data || response

        let spaces = []
        if (Array.isArray(spacesData)) {
          spaces = spacesData
        } else if (spacesData.items && Array.isArray(spacesData.items)) {
          spaces = spacesData.items
        } else if (spacesData.spaces && Array.isArray(spacesData.spaces)) {
          spaces = spacesData.spaces
        } else {
          console.error(`无法解析停车场 ${lot.id} 的车位数据:`, spacesData)
          this.addLog('error', `无法解析停车场 "${lot.name}" 的车位数据`)
          return
        }

        this.addLog('success', `停车场 "${lot.name}" 有 ${spaces.length} 个车位`)

        // 添加到车位列表
        for (const space of spaces) {
          const spaceInfo = {
            id: space.id,
            parkingLotId: lot.id,
            parkingLotName: lot.name,
            spaceNumber: space.space_number,
            spaceType: space.type,
            spaceTypeName: this.getSpaceTypeName(space.type),
            status: space.status,
            statusText: this.getSpaceStatusName(space.status),
            currentVehicleId: space.current_vehicle_id,
            occupiedSince: space.occupied_since || null,
            processing: false,
            issue: ''
          }

          this.allSpaces.push(spaceInfo)

          // 检查状态是否异常
          if (space.status === null || space.status === undefined ||
              (space.status !== 0 && space.status !== 1 && space.status !== 2)) {

            spaceInfo.issue = `状态值异常: ${space.status === null ? 'NULL' : space.status}`
            this.problematicSpaces.push(spaceInfo)
            this.addLog('warning', `发现状态异常车位: ${lot.name} - ${space.space_number}，状态值: ${space.status === null ? 'NULL' : space.status}`)
          }

          // 检查数据一致性问题
          else if (space.status === 1 && !space.current_vehicle_id) {
            spaceInfo.issue = '标记为已占用但没有关联车辆ID'
            this.problematicSpaces.push(spaceInfo)
            this.addLog('warning', `发现数据不一致车位: ${lot.name} - ${space.space_number}，标记为已占用但没有关联车辆`)
          }
          else if (space.status !== 1 && space.current_vehicle_id) {
            spaceInfo.issue = `标记为${this.getSpaceStatusName(space.status)}但有关联车辆ID: ${space.current_vehicle_id}`
            this.problematicSpaces.push(spaceInfo)
            this.addLog('warning', `发现数据不一致车位: ${lot.name} - ${space.space_number}，标记为${this.getSpaceStatusName(space.status)}但有关联车辆ID: ${space.current_vehicle_id}`)
          }
        }
      } catch (error) {
        console.error(`获取停车场 ${lot.id} 的车位数据失败:`, error)
        this.addLog('error', `获取停车场 "${lot.name}" 的车位数据失败: ${error.message || '未知错误'}`)
      }
    },

    async fixSpace(space) {
      space.processing = true
      this.addLog('info', `开始修复车位: ${space.parkingLotName} - ${space.spaceNumber}...`)

      try {
        await updateParkingSpaceStatus(space.id, 0) // 设置为可用状态

        this.addLog('success', `成功将车位 ${space.parkingLotName} - ${space.spaceNumber} 修改为可用状态`)
        this.$message.success(`成功修复车位: ${space.spaceNumber}`)

        // 从列表中移除
        const index = this.problematicSpaces.findIndex(s => s.id === space.id)
        if (index !== -1) {
          this.problematicSpaces.splice(index, 1)
        }
      } catch (error) {
        console.error(`修复车位 ${space.id} 失败:`, error)
        this.addLog('error', `修复车位 ${space.parkingLotName} - ${space.spaceNumber} 失败: ${error.message || '未知错误'}`)
        this.$message.error(`修复失败: ${error.message || '未知错误'}`)
      } finally {
        space.processing = false
      }
    },

    async fixAllSpaces() {
      if (this.problematicSpaces.length === 0) {
        this.$message.info('没有需要修复的车位')
        return
      }

      this.processing = true
      this.addLog('info', `开始批量修复 ${this.problematicSpaces.length} 个异常车位...`)

      try {
        // 创建一个副本，因为我们会在循环中修改原数组
        const spacesToFix = [...this.problematicSpaces]

        for (const space of spacesToFix) {
          await this.fixSpace(space)
        }

        this.addLog('success', '所有异常车位修复完成')
        this.$message.success('所有异常车位已修复')
      } catch (error) {
        console.error('批量修复车位失败:', error)
        this.addLog('error', `批量修复车位失败: ${error.message || '未知错误'}`)
        this.$message.error(`批量修复失败: ${error.message || '未知错误'}`)
      } finally {
        this.processing = false
      }
    },

    getSpaceTypeName(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || '未知类型'
    },

    getSpaceTypeTag(type) {
      const typeMap = {
        1: '',
        2: 'success',
        3: 'primary'
      }
      return typeMap[type] || 'info'
    },

    getSpaceStatusName(status) {
      const statusMap = {
        0: '空闲',
        1: '已占用',
        2: '维护中'
      }
      return statusMap[status] || '未知状态'
    },

    addLog(type, message) {
      const now = new Date()
      const timeStr = now.toLocaleTimeString()
      this.logs.unshift({
        type,
        time: timeStr,
        message
      })

      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .dashboard-title {
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
    }

    .subtitle {
      margin: 0;
      font-size: 14px;
      color: #909399;
    }
  }

  .dashboard-actions {
    display: flex;
    gap: 10px;
  }
}

.empty-data {
  padding: 30px 0;
  text-align: center;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: monospace;

  .log-item {
    padding: 5px 0;
    border-bottom: 1px dashed #ebeef5;

    &:last-child {
      border-bottom: none;
    }

    &.info {
      color: #409EFF;
    }

    &.success {
      color: #67C23A;
    }

    &.warning {
      color: #E6A23C;
    }

    &.error {
      color: #F56C6C;
    }

    .log-time {
      margin-right: 10px;
      font-weight: bold;
    }
  }

  .empty-log {
    text-align: center;
    color: #909399;
    padding: 20px 0;
  }
}
</style>
