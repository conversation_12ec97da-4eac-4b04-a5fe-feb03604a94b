from app import create_app, db
from app.violations.models import VehicleDisableRecord, ViolationRecord, Appeal
from app.bikes.models import Bikes
from app.users.models import Users
from flask import current_app
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = create_app()

with app.app_context():
    try:
        print("开始测试申诉处理时禁用车辆的功能...")
        
        # 1. 创建测试用户
        user = Users.query.filter_by(u_name='jyj').first()
        if not user:
            print("未找到jyj用户，创建测试用户")
            user = Users(u_name='jyj', u_pwd='123456', u_role='user')
            db.session.add(user)
            db.session.commit()
            print(f"创建测试用户: ID={user.u_id}, 用户名={user.u_name}")
        else:
            print(f"找到测试用户: ID={user.u_id}, 用户名={user.u_name}")
        
        # 2. 创建测试车辆
        bike = Bikes.query.filter_by(b_num='111').first()
        if not bike:
            print("未找到111车辆，创建测试车辆")
            bike = Bikes(b_num='111', belong_to=user.u_id, status='可用')
            db.session.add(bike)
            db.session.commit()
            print(f"创建测试车辆: ID={bike.b_id}, 车牌号={bike.b_num}, 状态={bike.status}")
        else:
            print(f"找到测试车辆: ID={bike.b_id}, 车牌号={bike.b_num}, 状态={bike.status}")
            # 确保车辆状态为可用
            if bike.status != '可用':
                bike.status = '可用'
                db.session.commit()
                print(f"重置车辆状态为: {bike.status}")
        
        # 3. 创建测试违规记录
        violation = ViolationRecord.query.filter_by(bike_id=bike.b_id).first()
        if not violation:
            print("未找到相关违规记录，创建测试违规记录")
            violation = ViolationRecord(
                bike_number=bike.b_num,
                bike_id=bike.b_id,
                user_id=user.u_id,
                violation_time=datetime.now(),
                location='测试地点',
                violation_type='违规停车',
                description='测试违规',
                status=1,  # 已处理
                recorder_id=1,  # 假设管理员ID为1
                handler_id=1
            )
            db.session.add(violation)
            db.session.commit()
            print(f"创建测试违规记录: ID={violation.id}, 状态={violation.status}")
        else:
            print(f"找到测试违规记录: ID={violation.id}, 状态={violation.status}")
            # 确保违规记录状态为已处理
            if violation.status != 1:
                violation.status = 1
                db.session.commit()
                print(f"重置违规记录状态为: {violation.status}")
        
        # 4. 创建测试申诉
        appeal = Appeal.query.filter_by(violation_id=violation.id).first()
        if not appeal:
            print("未找到相关申诉，创建测试申诉")
            appeal = Appeal(
                violation_id=violation.id,
                user_id=user.u_id,
                reason='测试申诉理由',
                status=0  # 待审核
            )
            db.session.add(appeal)
            db.session.commit()
            print(f"创建测试申诉: ID={appeal.id}, 状态={appeal.status}")
        else:
            print(f"找到测试申诉: ID={appeal.id}, 状态={appeal.status}")
            # 确保申诉状态为待审核
            if appeal.status != 0:
                appeal.status = 0
                db.session.commit()
                print(f"重置申诉状态为: {appeal.status}")
        
        # 5. 模拟处理申诉（拒绝）
        print("\n模拟处理申诉（拒绝）...")
        appeal.status = 2  # 未通过
        appeal.comment = '测试拒绝申诉'
        appeal.handler_id = 1  # 假设管理员ID为1
        
        # 更新违规记录状态
        violation.status = 1  # 已处理
        violation.result = f"申诉未通过: {appeal.comment}"
        violation.handler_id = 1
        
        # 禁用车辆
        if violation.bike_id:
            bike = Bikes.query.get(violation.bike_id)
            if bike:
                bike.status = "废弃"
                print(f"禁用车辆 {bike.b_num} (ID: {bike.b_id})")
                
                # 检查是否已有该车辆的禁用记录
                existing_record = VehicleDisableRecord.query.filter_by(
                    bike_id=violation.bike_id,
                    is_active=True
                ).first()
                
                if existing_record:
                    # 更新现有记录
                    existing_record.violation_id = violation.id
                    existing_record.disable_start_time = datetime.now()
                    existing_record.is_active = True
                    print(f"更新车辆 {bike.b_num} 的禁用记录")
                else:
                    # 创建新记录
                    disable_record = VehicleDisableRecord(
                        bike_id=violation.bike_id,
                        violation_id=violation.id,
                        disable_start_time=datetime.now(),
                        is_active=True
                    )
                    db.session.add(disable_record)
                    print(f"创建车辆 {bike.b_num} 的禁用记录")
        
        # 提交更改
        db.session.commit()
        
        # 6. 验证结果
        print("\n验证结果...")
        # 重新查询车辆状态
        bike = Bikes.query.get(bike.b_id)
        print(f"车辆状态: {bike.status}")
        
        # 查询禁用记录
        disable_records = VehicleDisableRecord.query.filter_by(bike_id=bike.b_id).all()
        print(f"禁用记录数量: {len(disable_records)}")
        for record in disable_records:
            print(f"禁用记录ID: {record.id}, 违规ID: {record.violation_id}, 是否激活: {record.is_active}")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        db.session.rollback()
