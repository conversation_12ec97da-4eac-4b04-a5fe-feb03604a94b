from flask import Blueprint, request, g
from app.utils.auth import admin_required
from app.utils.response import api_response
from app.announcements.models import Announcement
from app import db

announcements = Blueprint('announcements', __name__)

@announcements.route('', methods=['GET'])
def get_announcements():
    """获取公告列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    type_filter = request.args.get('type')
    
    query = Announcement.query
    if type_filter:
        query = query.filter_by(type=type_filter)
    
    announcements = query.order_by(Announcement.created_at.desc()).paginate(
        page=page, per_page=per_page
    )
    
    return api_response(data={
        'items': [announcement.to_dict() for announcement in announcements.items],
        'total': announcements.total,
        'pages': announcements.pages
    })

@announcements.route('/<int:id>', methods=['GET'])
def get_announcement(id):
    """获取单个公告详情"""
    announcement = Announcement.query.get(id)
    if not announcement:
        return api_response(message="公告不存在", status="error", code=404)
    
    return api_response(data=announcement.to_dict())

@announcements.route('', methods=['POST'])
@admin_required
def create_announcement():
    """创建公告（仅管理员）"""
    data = request.get_json()
    
    if not data or not all(k in data for k in ['title', 'content', 'type']):
        return api_response(message="缺少必要字段", status="error", code=400)
    
    # 验证通知类型
    valid_types = ['system', 'parking', 'violation']
    if data['type'] not in valid_types:
        return api_response(message="无效的通知类型", status="error", code=400)
    
    announcement = Announcement(
        title=data['title'],
        content=data['content'],
        type=data['type'],
        created_by=g.user.u_id
    )
    
    db.session.add(announcement)
    db.session.commit()
    
    return api_response(data=announcement.to_dict())

@announcements.route('/<int:id>', methods=['PUT'])
@admin_required
def update_announcement(id):
    """更新公告（仅管理员）"""
    announcement = Announcement.query.get(id)
    if not announcement:
        return api_response(message="公告不存在", status="error", code=404)
    
    data = request.get_json()
    if not data:
        return api_response(message="无效的请求数据", status="error", code=400)
    
    # 更新字段
    if 'title' in data:
        announcement.title = data['title']
    if 'content' in data:
        announcement.content = data['content']
    if 'type' in data:
        if data['type'] not in ['system', 'parking', 'violation']:
            return api_response(message="无效的通知类型", status="error", code=400)
        announcement.type = data['type']
    
    db.session.commit()
    return api_response(data=announcement.to_dict())

@announcements.route('/<int:id>', methods=['DELETE'])
@admin_required
def delete_announcement(id):
    """删除公告（仅管理员）"""
    announcement = Announcement.query.get(id)
    if not announcement:
        return api_response(message="公告不存在", status="error", code=404)
    
    db.session.delete(announcement)
    db.session.commit()
    
    return api_response(message="公告已删除") 