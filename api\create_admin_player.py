#!/usr/bin/env python

import os
import sys
import hashlib
import uuid

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app import create_app, db
from app.users.models import Users
from app.players.models import Players

# 创建Flask应用上下文
app = create_app()

def create_admin_user_and_player():
    """
    创建管理员用户和关联的玩家账号
    用户名: admin
    密码: 111111
    角色: admin
    """
    with app.app_context():
        # 1. 创建或更新Users表中的admin用户
        existing_user = Users.query.filter_by(u_name='admin').first()
        if existing_user:
            print(f"Users表中管理员用户 'admin' 已存在 (ID: {existing_user.u_id})")
            # 确保角色设置为admin
            if existing_user.u_role != 'admin':
                existing_user.u_role = 'admin'
                db.session.commit()
                print("已将Users表中用户角色更新为 'admin'")
            user_id = existing_user.u_id
        else:
            # 创建管理员用户
            salt = str(uuid.uuid4())
            password = "111111"
            hashed_password = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
            
            admin_user = Users(
                u_name='admin',
                u_pwd=hashed_password,
                u_role='admin',
                u_belong='系统管理',
                u_phone='13800000000',
                u_email='<EMAIL>'
            )
            # 手动设置盐值
            admin_user.salt = salt
            
            # 添加到数据库
            db.session.add(admin_user)
            db.session.commit()
            
            print(f"已在Users表中创建管理员用户 'admin' (ID: {admin_user.u_id})")
            user_id = admin_user.u_id

        # 2. 创建或更新Players表中的admin玩家
        existing_player = Players.query.filter_by(username='admin').first()
        if existing_player:
            print(f"Players表中玩家 'admin' 已存在 (ID: {existing_player.id})")
            
            # 确保关联到正确的user_id
            if existing_player.user_id != user_id:
                existing_player.user_id = user_id
                db.session.commit()
                print(f"已更新Players表中admin玩家的user_id为 {user_id}")
                
            # 更新密码
            password = "111111"
            hashed_password = Players.generate_hash(password)
            existing_player.password = hashed_password
            db.session.commit()
            print("已更新Players表中admin玩家的密码")
        else:
            # 创建Players表中的admin玩家
            password = "111111"
            hashed_password = Players.generate_hash(password)
            
            admin_player = Players(
                username='admin',
                password=hashed_password,
                user_id=user_id
            )
            
            # 添加到数据库
            db.session.add(admin_player)
            db.session.commit()
            
            print(f"已在Players表中创建admin玩家 (ID: {admin_player.id})")
        
        print("完成! Admin用户现在可以登录系统")
        print("用户名: admin")
        print("密码: 111111")

if __name__ == "__main__":
    create_admin_user_and_player() 